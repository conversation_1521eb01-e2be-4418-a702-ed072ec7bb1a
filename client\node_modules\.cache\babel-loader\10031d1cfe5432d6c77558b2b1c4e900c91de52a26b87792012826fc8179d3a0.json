{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lingolink\\\\client\\\\src\\\\components\\\\ApplyTranslation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './ApplyTranslation-new.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ApplyTranslation = () => {\n  _s();\n  const navigate = useNavigate();\n  const [step, setStep] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formData, setFormData] = useState({\n    sourceLanguage: '',\n    targetLanguage: '',\n    documentType: 'general',\n    pages: 1,\n    pricePerPage: 15,\n    deadline: '',\n    urgency: 'standard',\n    additionalServices: {\n      proofreading: false,\n      formatting: false,\n      certification: false,\n      notarization: false\n    },\n    description: '',\n    attachments: [],\n    contactMethod: 'email',\n    specialInstructions: ''\n  });\n\n  // Languages data\n  const languages = [{\n    code: 'en',\n    name: 'English',\n    flag: '🇺🇸'\n  }, {\n    code: 'fr',\n    name: 'French',\n    flag: '🇫🇷'\n  }, {\n    code: 'ar',\n    name: 'Arabic',\n    flag: '🇸🇦'\n  }, {\n    code: 'es',\n    name: 'Spanish',\n    flag: '🇪🇸'\n  }, {\n    code: 'de',\n    name: 'German',\n    flag: '🇩🇪'\n  }, {\n    code: 'it',\n    name: 'Italian',\n    flag: '🇮🇹'\n  }, {\n    code: 'pt',\n    name: 'Portuguese',\n    flag: '🇵🇹'\n  }, {\n    code: 'ru',\n    name: 'Russian',\n    flag: '🇷🇺'\n  }, {\n    code: 'zh',\n    name: 'Chinese',\n    flag: '🇨🇳'\n  }, {\n    code: 'ja',\n    name: 'Japanese',\n    flag: '🇯🇵'\n  }];\n  const documentTypes = [{\n    value: 'general',\n    label: 'General Document',\n    icon: '📄'\n  }, {\n    value: 'legal',\n    label: 'Legal Document',\n    icon: '⚖️'\n  }, {\n    value: 'technical',\n    label: 'Technical Document',\n    icon: '🔧'\n  }, {\n    value: 'medical',\n    label: 'Medical Document',\n    icon: '🏥'\n  }, {\n    value: 'academic',\n    label: 'Academic Document',\n    icon: '🎓'\n  }, {\n    value: 'business',\n    label: 'Business Document',\n    icon: '💼'\n  }, {\n    value: 'marketing',\n    label: 'Marketing Material',\n    icon: '📢'\n  }];\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setErrors(prev => ({\n      ...prev,\n      [name]: ''\n    })); // Clear error when user types\n\n    if (type === 'checkbox') {\n      setFormData(prev => ({\n        ...prev,\n        additionalServices: {\n          ...prev.additionalServices,\n          [name]: checked\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    const validFiles = files.filter(file => {\n      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];\n      return validTypes.includes(file.type) && file.size <= 10 * 1024 * 1024; // 10MB limit\n    });\n    setFormData(prev => ({\n      ...prev,\n      attachments: [...prev.attachments, ...validFiles]\n    }));\n  };\n  const removeFile = index => {\n    setFormData(prev => ({\n      ...prev,\n      attachments: prev.attachments.filter((_, i) => i !== index)\n    }));\n  };\n  const calculateTotal = () => {\n    let basePrice = formData.pages * formData.pricePerPage;\n    let total = basePrice;\n\n    // Urgency multiplier\n    const urgencyMultipliers = {\n      standard: 1,\n      express: 1.25,\n      urgent: 1.5,\n      emergency: 2\n    };\n    total *= urgencyMultipliers[formData.urgency] || 1;\n\n    // Additional services\n    if (formData.additionalServices.proofreading) total *= 1.15;\n    if (formData.additionalServices.formatting) total *= 1.10;\n    if (formData.additionalServices.certification) total *= 1.20;\n    if (formData.additionalServices.notarization) total *= 1.25;\n    return {\n      basePrice: basePrice.toFixed(2),\n      total: total.toFixed(2),\n      savings: basePrice > total ? (basePrice - total).toFixed(2) : 0\n    };\n  };\n  const validateStep = stepNumber => {\n    const newErrors = {};\n    switch (stepNumber) {\n      case 1:\n        if (!formData.sourceLanguage) newErrors.sourceLanguage = 'Source language is required';\n        if (!formData.targetLanguage) newErrors.targetLanguage = 'Target language is required';\n        if (formData.sourceLanguage === formData.targetLanguage) newErrors.targetLanguage = 'Target language must be different from source';\n        if (!formData.documentType) newErrors.documentType = 'Document type is required';\n        break;\n      case 2:\n        if (!formData.pages || formData.pages < 1) newErrors.pages = 'Number of pages must be at least 1';\n        if (!formData.pricePerPage || formData.pricePerPage < 15) newErrors.pricePerPage = 'Price per page must be at least 15 TND';\n        if (!formData.deadline) newErrors.deadline = 'Deadline is required';\n        break;\n      case 3:\n        if (!formData.description.trim()) newErrors.description = 'Project description is required';\n        if (formData.attachments.length === 0) newErrors.attachments = 'At least one document must be uploaded';\n        break;\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const nextStep = () => {\n    if (validateStep(step)) {\n      setStep(prev => Math.min(prev + 1, 4));\n    }\n  };\n  const prevStep = () => {\n    setStep(prev => Math.max(prev - 1, 1));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateStep(4)) return;\n    setIsLoading(true);\n    try {\n      // TODO: Implement actual API call\n      const response = await fetch('/api/translation-requests', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          ...formData,\n          totalPrice: calculateTotal().total,\n          status: 'pending',\n          submittedAt: new Date().toISOString()\n        })\n      });\n      if (response.ok) {\n        navigate('/dashboard', {\n          state: {\n            message: 'Translation request submitted successfully!',\n            type: 'success'\n          }\n        });\n      } else {\n        throw new Error('Failed to submit request');\n      }\n    } catch (error) {\n      console.error('Error submitting translation request:', error);\n      setErrors({\n        submit: 'Failed to submit request. Please try again.'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Auto-calculate total when form data changes\n  useEffect(() => {\n    calculateTotal();\n  }, [formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices]);\n  const renderStep = () => {\n    var _selectedLanguages$so, _selectedLanguages$so2, _selectedLanguages$ta, _selectedLanguages$ta2;\n    switch (step) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\uD83D\\uDCCB Document Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Tell us about your translation project\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Source Language\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"select-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"sourceLanguage\",\n                  value: formData.sourceLanguage,\n                  onChange: handleInputChange,\n                  className: errors.sourceLanguage ? 'error' : '',\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Choose source language\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), languages.map(lang => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: lang.code,\n                    children: [lang.flag, \" \", lang.name]\n                  }, lang.code, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), errors.sourceLanguage && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error-message\",\n                  children: errors.sourceLanguage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Target Language\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"select-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"targetLanguage\",\n                  value: formData.targetLanguage,\n                  onChange: handleInputChange,\n                  className: errors.targetLanguage ? 'error' : '',\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Choose target language\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this), languages.map(lang => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: lang.code,\n                    children: [lang.flag, \" \", lang.name]\n                  }, lang.code, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), errors.targetLanguage && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error-message\",\n                  children: errors.targetLanguage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Document Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"document-type-grid\",\n              children: documentTypes.map(type => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `document-type-card ${formData.documentType === type.value ? 'selected' : ''}`,\n                onClick: () => handleInputChange({\n                  target: {\n                    name: 'documentType',\n                    value: type.value\n                  }\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"document-icon\",\n                  children: type.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"document-label\",\n                  children: type.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, type.value, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), errors.documentType && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"error-message\",\n              children: errors.documentType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this);\n      case 2:\n        const pricing = calculateTotal();\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u2699\\uFE0F Service Options\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Configure your translation requirements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Number of Pages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"pages\",\n                  min: \"1\",\n                  value: formData.pages,\n                  onChange: handleInputChange,\n                  className: errors.pages ? 'error' : '',\n                  placeholder: \"Enter number of pages\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), errors.pages && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error-message\",\n                  children: errors.pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 36\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Price per Page (Min. 15 TND)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"pricePerPage\",\n                  min: \"15\",\n                  value: formData.pricePerPage,\n                  onChange: handleInputChange,\n                  className: errors.pricePerPage ? 'error' : '',\n                  placeholder: \"15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-suffix\",\n                  children: \"TND\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), errors.pricePerPage && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error-message\",\n                  children: errors.pricePerPage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Deadline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"deadline\",\n                  value: formData.deadline,\n                  onChange: handleInputChange,\n                  className: errors.deadline ? 'error' : '',\n                  min: new Date().toISOString().split('T')[0]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this), errors.deadline && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error-message\",\n                  children: errors.deadline\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 39\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Urgency Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"urgency-options\",\n                children: [{\n                  value: 'standard',\n                  label: 'Standard',\n                  desc: 'Normal delivery',\n                  multiplier: '1x'\n                }, {\n                  value: 'express',\n                  label: 'Express',\n                  desc: '3-5 days',\n                  multiplier: '1.25x'\n                }, {\n                  value: 'urgent',\n                  label: 'Urgent',\n                  desc: '1-2 days',\n                  multiplier: '1.5x'\n                }, {\n                  value: 'emergency',\n                  label: 'Emergency',\n                  desc: 'Same day',\n                  multiplier: '2x'\n                }].map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `urgency-card ${formData.urgency === option.value ? 'selected' : ''}`,\n                  onClick: () => handleInputChange({\n                    target: {\n                      name: 'urgency',\n                      value: option.value\n                    }\n                  }),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"urgency-label\",\n                    children: option.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"urgency-desc\",\n                    children: option.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"urgency-multiplier\",\n                    children: option.multiplier\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this)]\n                }, option.value, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"additional-services\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"\\uD83D\\uDCCB Additional Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"services-grid\",\n              children: [{\n                key: 'proofreading',\n                label: 'Proofreading',\n                desc: 'Quality review and corrections',\n                price: '+15%',\n                icon: '✏️'\n              }, {\n                key: 'formatting',\n                label: 'Formatting',\n                desc: 'Document layout and styling',\n                price: '+10%',\n                icon: '📐'\n              }, {\n                key: 'certification',\n                label: 'Certification',\n                desc: 'Official certified translation',\n                price: '+20%',\n                icon: '🏆'\n              }, {\n                key: 'notarization',\n                label: 'Notarization',\n                desc: 'Notarized document service',\n                price: '+25%',\n                icon: '📋'\n              }].map(service => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `service-card ${formData.additionalServices[service.key] ? 'selected' : ''}`,\n                onClick: () => handleInputChange({\n                  target: {\n                    name: service.key,\n                    type: 'checkbox',\n                    checked: !formData.additionalServices[service.key]\n                  }\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-icon\",\n                  children: service.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"service-label\",\n                    children: service.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"service-desc\",\n                    children: service.desc\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"service-price\",\n                  children: service.price\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this)]\n              }, service.key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-summary\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-breakdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Base Price (\", formData.pages, \" pages \\xD7 \", formData.pricePerPage, \" TND)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [pricing.basePrice, \" TND\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-item total\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Total Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"total-amount\",\n                  children: [pricing.total, \" TND\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\uD83D\\uDCC4 Documents & Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Upload your documents and provide additional information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Project Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"textarea-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                placeholder: \"Please provide specific requirements, context, terminology preferences, or any special instructions for your translation...\",\n                rows: \"5\",\n                className: errors.description ? 'error' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"char-count\",\n                children: [formData.description.length, \"/500\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), errors.description && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"error-message\",\n                children: errors.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Upload Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-upload-area\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                multiple: true,\n                onChange: handleFileChange,\n                accept: \".doc,.docx,.pdf,.txt\",\n                id: \"file-upload\",\n                className: \"file-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"file-upload\",\n                className: \"file-upload-label\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"upload-icon\",\n                  children: \"\\uD83D\\uDCC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"upload-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"Click to upload or drag and drop\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"upload-hint\",\n                    children: \"PDF, DOC, DOCX, TXT (Max 10MB each)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), errors.attachments && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"error-message\",\n                children: errors.attachments\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), formData.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uploaded-files\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [\"Uploaded Files (\", formData.attachments.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), formData.attachments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-icon\",\n                    children: \"\\uD83D\\uDCC4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-details\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"file-name\",\n                      children: file.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"file-size\",\n                      children: [(file.size / 1024 / 1024).toFixed(2), \" MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"remove-file\",\n                  onClick: () => removeFile(index),\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Special Instructions (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"textarea-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"specialInstructions\",\n                value: formData.specialInstructions,\n                onChange: handleInputChange,\n                placeholder: \"Any additional notes, glossary terms, or specific formatting requirements...\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Preferred Contact Method\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-methods\",\n              children: [{\n                value: 'email',\n                label: 'Email',\n                icon: '📧'\n              }, {\n                value: 'phone',\n                label: 'Phone',\n                icon: '📞'\n              }, {\n                value: 'whatsapp',\n                label: 'WhatsApp',\n                icon: '💬'\n              }].map(method => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `contact-method ${formData.contactMethod === method.value ? 'selected' : ''}`,\n                onClick: () => handleInputChange({\n                  target: {\n                    name: 'contactMethod',\n                    value: method.value\n                  }\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"method-icon\",\n                  children: method.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"method-label\",\n                  children: method.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this)]\n              }, method.value, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this);\n      case 4:\n        const finalPricing = calculateTotal();\n        const selectedLanguages = {\n          source: languages.find(l => l.code === formData.sourceLanguage),\n          target: languages.find(l => l.code === formData.targetLanguage)\n        };\n        const selectedDocType = documentTypes.find(t => t.value === formData.documentType);\n        const selectedServices = Object.entries(formData.additionalServices).filter(([, value]) => value).map(([key]) => key);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u2705 Review & Submit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please review your translation request before submitting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"review-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDCCB Project Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Languages\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: [(_selectedLanguages$so = selectedLanguages.source) === null || _selectedLanguages$so === void 0 ? void 0 : _selectedLanguages$so.flag, \" \", (_selectedLanguages$so2 = selectedLanguages.source) === null || _selectedLanguages$so2 === void 0 ? void 0 : _selectedLanguages$so2.name, \" \\u2192 \", (_selectedLanguages$ta = selectedLanguages.target) === null || _selectedLanguages$ta === void 0 ? void 0 : _selectedLanguages$ta.flag, \" \", (_selectedLanguages$ta2 = selectedLanguages.target) === null || _selectedLanguages$ta2 === void 0 ? void 0 : _selectedLanguages$ta2.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Document Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: [selectedDocType === null || selectedDocType === void 0 ? void 0 : selectedDocType.icon, \" \", selectedDocType === null || selectedDocType === void 0 ? void 0 : selectedDocType.label]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Pages\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: [formData.pages, \" pages\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Deadline\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: new Date(formData.deadline).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u2699\\uFE0F Service Options\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Urgency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: formData.urgency\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Additional Services\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: selectedServices.length > 0 ? selectedServices.join(', ') : 'None'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Contact Method\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: formData.contactMethod\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-label\",\n                    children: \"Files\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"summary-value\",\n                    children: [formData.attachments.length, \" file(s)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDCB0 Pricing Breakdown\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pricing-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Base Price (\", formData.pages, \" pages \\xD7 \", formData.pricePerPage, \" TND)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [finalPricing.basePrice, \" TND\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this), formData.urgency !== 'standard' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pricing-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Urgency (\", formData.urgency, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"+\", ((calculateTotal().total / finalPricing.basePrice - 1) * 100).toFixed(0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pricing-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: service\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Included\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, service, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pricing-item total\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"total-price\",\n                    children: [finalPricing.total, \" TND\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-banner\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u274C \", errors.submit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const stepTitles = [{\n    title: 'Document Details',\n    icon: '📋'\n  }, {\n    title: 'Service Options',\n    icon: '⚙️'\n  }, {\n    title: 'Documents & Details',\n    icon: '📄'\n  }, {\n    title: 'Review & Submit',\n    icon: '✅'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"apply-translation-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"apply-translation\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: () => navigate('/dashboard'),\n          children: \"\\u2190 Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Apply for Translation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Submit your translation request in 4 easy steps\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-bar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-track\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\",\n            style: {\n              width: `${(step - 1) / 3 * 100}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-steps\",\n          children: stepTitles.map((stepInfo, index) => {\n            const stepNumber = index + 1;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `step ${stepNumber === step ? 'active' : ''} ${stepNumber < step ? 'completed' : ''}`,\n              onClick: () => stepNumber < step && setStep(stepNumber),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-circle\",\n                children: stepNumber < step ? '✓' : stepInfo.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-label\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-title\",\n                  children: stepInfo.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-number\",\n                  children: [\"Step \", stepNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)]\n            }, stepNumber, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"translation-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-container\",\n          children: renderStep()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-navigation\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-buttons\",\n            children: [step > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-secondary\",\n              onClick: prevStep,\n              disabled: isLoading,\n              children: \"\\u2190 Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"nav-spacer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), step < 4 ? /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-primary\",\n              onClick: nextStep,\n              disabled: isLoading,\n              children: \"Next \\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn-submit\",\n              disabled: isLoading,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 23\n                }, this), \"Submitting...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: \"Submit Request \\u2713\"\n              }, void 0, false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-indicator\",\n            children: [\"Step \", step, \" of 4\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 617,\n    columnNumber: 5\n  }, this);\n};\n_s(ApplyTranslation, \"YdN7i3+RddLvApT3GIG9TIupERA=\", false, function () {\n  return [useNavigate];\n});\n_c = ApplyTranslation;\nexport default ApplyTranslation;\nvar _c;\n$RefreshReg$(_c, \"ApplyTranslation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ApplyTranslation", "_s", "navigate", "step", "setStep", "isLoading", "setIsLoading", "errors", "setErrors", "formData", "setFormData", "sourceLanguage", "targetLanguage", "documentType", "pages", "pricePerPage", "deadline", "urgency", "additionalServices", "proofreading", "formatting", "certification", "notarization", "description", "attachments", "contactMethod", "specialInstructions", "languages", "code", "name", "flag", "documentTypes", "value", "label", "icon", "handleInputChange", "e", "type", "checked", "target", "prev", "handleFileChange", "files", "Array", "from", "validFiles", "filter", "file", "validTypes", "includes", "size", "removeFile", "index", "_", "i", "calculateTotal", "basePrice", "total", "urgencyMultipliers", "standard", "express", "urgent", "emergency", "toFixed", "savings", "validateStep", "<PERSON><PERSON><PERSON><PERSON>", "newErrors", "trim", "length", "Object", "keys", "nextStep", "Math", "min", "prevStep", "max", "handleSubmit", "preventDefault", "response", "fetch", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "totalPrice", "status", "submittedAt", "Date", "toISOString", "ok", "state", "message", "Error", "error", "console", "submit", "renderStep", "_selectedLanguages$so", "_selectedLanguages$so2", "_selectedLanguages$ta", "_selectedLanguages$ta2", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "map", "lang", "onClick", "pricing", "placeholder", "split", "desc", "multiplier", "option", "key", "price", "service", "rows", "multiple", "accept", "id", "htmlFor", "finalPricing", "selectedLanguages", "source", "find", "l", "selectedDocType", "t", "selectedServices", "entries", "toLocaleDateString", "join", "<PERSON><PERSON><PERSON><PERSON>", "title", "style", "width", "stepInfo", "onSubmit", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lingolink/client/src/components/ApplyTranslation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './ApplyTranslation-new.css';\n\nconst ApplyTranslation = () => {\n  const navigate = useNavigate();\n  const [step, setStep] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [formData, setFormData] = useState({\n    sourceLanguage: '',\n    targetLanguage: '',\n    documentType: 'general',\n    pages: 1,\n    pricePerPage: 15,\n    deadline: '',\n    urgency: 'standard',\n    additionalServices: {\n      proofreading: false,\n      formatting: false,\n      certification: false,\n      notarization: false\n    },\n    description: '',\n    attachments: [],\n    contactMethod: 'email',\n    specialInstructions: ''\n  });\n\n  // Languages data\n  const languages = [\n    { code: 'en', name: 'English', flag: '🇺🇸' },\n    { code: 'fr', name: 'French', flag: '🇫🇷' },\n    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },\n    { code: 'es', name: 'Spanish', flag: '🇪🇸' },\n    { code: 'de', name: 'German', flag: '🇩🇪' },\n    { code: 'it', name: 'Italian', flag: '🇮🇹' },\n    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },\n    { code: 'ru', name: 'Russian', flag: '🇷🇺' },\n    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },\n    { code: 'ja', name: 'Japanese', flag: '🇯🇵' }\n  ];\n\n  const documentTypes = [\n    { value: 'general', label: 'General Document', icon: '📄' },\n    { value: 'legal', label: 'Legal Document', icon: '⚖️' },\n    { value: 'technical', label: 'Technical Document', icon: '🔧' },\n    { value: 'medical', label: 'Medical Document', icon: '🏥' },\n    { value: 'academic', label: 'Academic Document', icon: '🎓' },\n    { value: 'business', label: 'Business Document', icon: '💼' },\n    { value: 'marketing', label: 'Marketing Material', icon: '📢' }\n  ];\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setErrors(prev => ({ ...prev, [name]: '' })); // Clear error when user types\n\n    if (type === 'checkbox') {\n      setFormData(prev => ({\n        ...prev,\n        additionalServices: {\n          ...prev.additionalServices,\n          [name]: checked\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  const handleFileChange = (e) => {\n    const files = Array.from(e.target.files);\n    const validFiles = files.filter(file => {\n      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];\n      return validTypes.includes(file.type) && file.size <= 10 * 1024 * 1024; // 10MB limit\n    });\n\n    setFormData(prev => ({\n      ...prev,\n      attachments: [...prev.attachments, ...validFiles]\n    }));\n  };\n\n  const removeFile = (index) => {\n    setFormData(prev => ({\n      ...prev,\n      attachments: prev.attachments.filter((_, i) => i !== index)\n    }));\n  };\n\n  const calculateTotal = () => {\n    let basePrice = formData.pages * formData.pricePerPage;\n    let total = basePrice;\n\n    // Urgency multiplier\n    const urgencyMultipliers = {\n      standard: 1,\n      express: 1.25,\n      urgent: 1.5,\n      emergency: 2\n    };\n    total *= urgencyMultipliers[formData.urgency] || 1;\n\n    // Additional services\n    if (formData.additionalServices.proofreading) total *= 1.15;\n    if (formData.additionalServices.formatting) total *= 1.10;\n    if (formData.additionalServices.certification) total *= 1.20;\n    if (formData.additionalServices.notarization) total *= 1.25;\n\n    return {\n      basePrice: basePrice.toFixed(2),\n      total: total.toFixed(2),\n      savings: basePrice > total ? (basePrice - total).toFixed(2) : 0\n    };\n  };\n\n  const validateStep = (stepNumber) => {\n    const newErrors = {};\n\n    switch(stepNumber) {\n      case 1:\n        if (!formData.sourceLanguage) newErrors.sourceLanguage = 'Source language is required';\n        if (!formData.targetLanguage) newErrors.targetLanguage = 'Target language is required';\n        if (formData.sourceLanguage === formData.targetLanguage) newErrors.targetLanguage = 'Target language must be different from source';\n        if (!formData.documentType) newErrors.documentType = 'Document type is required';\n        break;\n      case 2:\n        if (!formData.pages || formData.pages < 1) newErrors.pages = 'Number of pages must be at least 1';\n        if (!formData.pricePerPage || formData.pricePerPage < 15) newErrors.pricePerPage = 'Price per page must be at least 15 TND';\n        if (!formData.deadline) newErrors.deadline = 'Deadline is required';\n        break;\n      case 3:\n        if (!formData.description.trim()) newErrors.description = 'Project description is required';\n        if (formData.attachments.length === 0) newErrors.attachments = 'At least one document must be uploaded';\n        break;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const nextStep = () => {\n    if (validateStep(step)) {\n      setStep(prev => Math.min(prev + 1, 4));\n    }\n  };\n\n  const prevStep = () => {\n    setStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (!validateStep(4)) return;\n\n    setIsLoading(true);\n    try {\n      // TODO: Implement actual API call\n      const response = await fetch('/api/translation-requests', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          ...formData,\n          totalPrice: calculateTotal().total,\n          status: 'pending',\n          submittedAt: new Date().toISOString()\n        })\n      });\n\n      if (response.ok) {\n        navigate('/dashboard', {\n          state: {\n            message: 'Translation request submitted successfully!',\n            type: 'success'\n          }\n        });\n      } else {\n        throw new Error('Failed to submit request');\n      }\n    } catch (error) {\n      console.error('Error submitting translation request:', error);\n      setErrors({ submit: 'Failed to submit request. Please try again.' });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Auto-calculate total when form data changes\n  useEffect(() => {\n    calculateTotal();\n  }, [formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices]);\n\n  const renderStep = () => {\n    switch(step) {\n      case 1:\n        return (\n          <div className=\"step-content\">\n            <div className=\"step-header\">\n              <h2>📋 Document Details</h2>\n              <p>Tell us about your translation project</p>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Source Language</label>\n                <div className=\"select-wrapper\">\n                  <select\n                    name=\"sourceLanguage\"\n                    value={formData.sourceLanguage}\n                    onChange={handleInputChange}\n                    className={errors.sourceLanguage ? 'error' : ''}\n                  >\n                    <option value=\"\">Choose source language</option>\n                    {languages.map(lang => (\n                      <option key={lang.code} value={lang.code}>\n                        {lang.flag} {lang.name}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.sourceLanguage && <span className=\"error-message\">{errors.sourceLanguage}</span>}\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label>Target Language</label>\n                <div className=\"select-wrapper\">\n                  <select\n                    name=\"targetLanguage\"\n                    value={formData.targetLanguage}\n                    onChange={handleInputChange}\n                    className={errors.targetLanguage ? 'error' : ''}\n                  >\n                    <option value=\"\">Choose target language</option>\n                    {languages.map(lang => (\n                      <option key={lang.code} value={lang.code}>\n                        {lang.flag} {lang.name}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.targetLanguage && <span className=\"error-message\">{errors.targetLanguage}</span>}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label>Document Type</label>\n              <div className=\"document-type-grid\">\n                {documentTypes.map(type => (\n                  <div\n                    key={type.value}\n                    className={`document-type-card ${formData.documentType === type.value ? 'selected' : ''}`}\n                    onClick={() => handleInputChange({ target: { name: 'documentType', value: type.value } })}\n                  >\n                    <div className=\"document-icon\">{type.icon}</div>\n                    <div className=\"document-label\">{type.label}</div>\n                  </div>\n                ))}\n              </div>\n              {errors.documentType && <span className=\"error-message\">{errors.documentType}</span>}\n            </div>\n          </div>\n        );\n      case 2:\n        const pricing = calculateTotal();\n        return (\n          <div className=\"step-content\">\n            <div className=\"step-header\">\n              <h2>⚙️ Service Options</h2>\n              <p>Configure your translation requirements</p>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Number of Pages</label>\n                <div className=\"input-wrapper\">\n                  <input\n                    type=\"number\"\n                    name=\"pages\"\n                    min=\"1\"\n                    value={formData.pages}\n                    onChange={handleInputChange}\n                    className={errors.pages ? 'error' : ''}\n                    placeholder=\"Enter number of pages\"\n                  />\n                  {errors.pages && <span className=\"error-message\">{errors.pages}</span>}\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label>Price per Page (Min. 15 TND)</label>\n                <div className=\"input-wrapper\">\n                  <input\n                    type=\"number\"\n                    name=\"pricePerPage\"\n                    min=\"15\"\n                    value={formData.pricePerPage}\n                    onChange={handleInputChange}\n                    className={errors.pricePerPage ? 'error' : ''}\n                    placeholder=\"15\"\n                  />\n                  <span className=\"input-suffix\">TND</span>\n                  {errors.pricePerPage && <span className=\"error-message\">{errors.pricePerPage}</span>}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"form-grid\">\n              <div className=\"form-group\">\n                <label>Deadline</label>\n                <div className=\"input-wrapper\">\n                  <input\n                    type=\"date\"\n                    name=\"deadline\"\n                    value={formData.deadline}\n                    onChange={handleInputChange}\n                    className={errors.deadline ? 'error' : ''}\n                    min={new Date().toISOString().split('T')[0]}\n                  />\n                  {errors.deadline && <span className=\"error-message\">{errors.deadline}</span>}\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label>Urgency Level</label>\n                <div className=\"urgency-options\">\n                  {[\n                    { value: 'standard', label: 'Standard', desc: 'Normal delivery', multiplier: '1x' },\n                    { value: 'express', label: 'Express', desc: '3-5 days', multiplier: '1.25x' },\n                    { value: 'urgent', label: 'Urgent', desc: '1-2 days', multiplier: '1.5x' },\n                    { value: 'emergency', label: 'Emergency', desc: 'Same day', multiplier: '2x' }\n                  ].map(option => (\n                    <div\n                      key={option.value}\n                      className={`urgency-card ${formData.urgency === option.value ? 'selected' : ''}`}\n                      onClick={() => handleInputChange({ target: { name: 'urgency', value: option.value } })}\n                    >\n                      <div className=\"urgency-label\">{option.label}</div>\n                      <div className=\"urgency-desc\">{option.desc}</div>\n                      <div className=\"urgency-multiplier\">{option.multiplier}</div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"additional-services\">\n              <h4>📋 Additional Services</h4>\n              <div className=\"services-grid\">\n                {[\n                  { key: 'proofreading', label: 'Proofreading', desc: 'Quality review and corrections', price: '+15%', icon: '✏️' },\n                  { key: 'formatting', label: 'Formatting', desc: 'Document layout and styling', price: '+10%', icon: '📐' },\n                  { key: 'certification', label: 'Certification', desc: 'Official certified translation', price: '+20%', icon: '🏆' },\n                  { key: 'notarization', label: 'Notarization', desc: 'Notarized document service', price: '+25%', icon: '📋' }\n                ].map(service => (\n                  <div\n                    key={service.key}\n                    className={`service-card ${formData.additionalServices[service.key] ? 'selected' : ''}`}\n                    onClick={() => handleInputChange({\n                      target: {\n                        name: service.key,\n                        type: 'checkbox',\n                        checked: !formData.additionalServices[service.key]\n                      }\n                    })}\n                  >\n                    <div className=\"service-icon\">{service.icon}</div>\n                    <div className=\"service-info\">\n                      <div className=\"service-label\">{service.label}</div>\n                      <div className=\"service-desc\">{service.desc}</div>\n                    </div>\n                    <div className=\"service-price\">{service.price}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"price-summary\">\n              <div className=\"price-breakdown\">\n                <div className=\"price-item\">\n                  <span>Base Price ({formData.pages} pages × {formData.pricePerPage} TND)</span>\n                  <span>{pricing.basePrice} TND</span>\n                </div>\n                <div className=\"price-item total\">\n                  <span>Total Price</span>\n                  <span className=\"total-amount\">{pricing.total} TND</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n      case 3:\n        return (\n          <div className=\"step-content\">\n            <div className=\"step-header\">\n              <h2>📄 Documents & Details</h2>\n              <p>Upload your documents and provide additional information</p>\n            </div>\n\n            <div className=\"form-group\">\n              <label>Project Description</label>\n              <div className=\"textarea-wrapper\">\n                <textarea\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleInputChange}\n                  placeholder=\"Please provide specific requirements, context, terminology preferences, or any special instructions for your translation...\"\n                  rows=\"5\"\n                  className={errors.description ? 'error' : ''}\n                />\n                <div className=\"char-count\">{formData.description.length}/500</div>\n                {errors.description && <span className=\"error-message\">{errors.description}</span>}\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label>Upload Documents</label>\n              <div className=\"file-upload-area\">\n                <input\n                  type=\"file\"\n                  multiple\n                  onChange={handleFileChange}\n                  accept=\".doc,.docx,.pdf,.txt\"\n                  id=\"file-upload\"\n                  className=\"file-input\"\n                />\n                <label htmlFor=\"file-upload\" className=\"file-upload-label\">\n                  <div className=\"upload-icon\">📁</div>\n                  <div className=\"upload-text\">\n                    <div>Click to upload or drag and drop</div>\n                    <div className=\"upload-hint\">PDF, DOC, DOCX, TXT (Max 10MB each)</div>\n                  </div>\n                </label>\n                {errors.attachments && <span className=\"error-message\">{errors.attachments}</span>}\n              </div>\n\n              {formData.attachments.length > 0 && (\n                <div className=\"uploaded-files\">\n                  <h4>Uploaded Files ({formData.attachments.length})</h4>\n                  {formData.attachments.map((file, index) => (\n                    <div key={index} className=\"file-item\">\n                      <div className=\"file-info\">\n                        <div className=\"file-icon\">📄</div>\n                        <div className=\"file-details\">\n                          <div className=\"file-name\">{file.name}</div>\n                          <div className=\"file-size\">{(file.size / 1024 / 1024).toFixed(2)} MB</div>\n                        </div>\n                      </div>\n                      <button\n                        type=\"button\"\n                        className=\"remove-file\"\n                        onClick={() => removeFile(index)}\n                      >\n                        ✕\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            <div className=\"form-group\">\n              <label>Special Instructions (Optional)</label>\n              <div className=\"textarea-wrapper\">\n                <textarea\n                  name=\"specialInstructions\"\n                  value={formData.specialInstructions}\n                  onChange={handleInputChange}\n                  placeholder=\"Any additional notes, glossary terms, or specific formatting requirements...\"\n                  rows=\"3\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label>Preferred Contact Method</label>\n              <div className=\"contact-methods\">\n                {[\n                  { value: 'email', label: 'Email', icon: '📧' },\n                  { value: 'phone', label: 'Phone', icon: '📞' },\n                  { value: 'whatsapp', label: 'WhatsApp', icon: '💬' }\n                ].map(method => (\n                  <div\n                    key={method.value}\n                    className={`contact-method ${formData.contactMethod === method.value ? 'selected' : ''}`}\n                    onClick={() => handleInputChange({ target: { name: 'contactMethod', value: method.value } })}\n                  >\n                    <div className=\"method-icon\">{method.icon}</div>\n                    <div className=\"method-label\">{method.label}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        );\n      case 4:\n        const finalPricing = calculateTotal();\n        const selectedLanguages = {\n          source: languages.find(l => l.code === formData.sourceLanguage),\n          target: languages.find(l => l.code === formData.targetLanguage)\n        };\n        const selectedDocType = documentTypes.find(t => t.value === formData.documentType);\n        const selectedServices = Object.entries(formData.additionalServices)\n          .filter(([, value]) => value)\n          .map(([key]) => key);\n\n        return (\n          <div className=\"step-content\">\n            <div className=\"step-header\">\n              <h2>✅ Review & Submit</h2>\n              <p>Please review your translation request before submitting</p>\n            </div>\n\n            <div className=\"review-summary\">\n              <div className=\"summary-section\">\n                <h3>📋 Project Details</h3>\n                <div className=\"summary-grid\">\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Languages</div>\n                    <div className=\"summary-value\">\n                      {selectedLanguages.source?.flag} {selectedLanguages.source?.name} → {selectedLanguages.target?.flag} {selectedLanguages.target?.name}\n                    </div>\n                  </div>\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Document Type</div>\n                    <div className=\"summary-value\">\n                      {selectedDocType?.icon} {selectedDocType?.label}\n                    </div>\n                  </div>\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Pages</div>\n                    <div className=\"summary-value\">{formData.pages} pages</div>\n                  </div>\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Deadline</div>\n                    <div className=\"summary-value\">{new Date(formData.deadline).toLocaleDateString()}</div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"summary-section\">\n                <h3>⚙️ Service Options</h3>\n                <div className=\"summary-grid\">\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Urgency</div>\n                    <div className=\"summary-value\">{formData.urgency}</div>\n                  </div>\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Additional Services</div>\n                    <div className=\"summary-value\">\n                      {selectedServices.length > 0 ? selectedServices.join(', ') : 'None'}\n                    </div>\n                  </div>\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Contact Method</div>\n                    <div className=\"summary-value\">{formData.contactMethod}</div>\n                  </div>\n                  <div className=\"summary-item\">\n                    <div className=\"summary-label\">Files</div>\n                    <div className=\"summary-value\">{formData.attachments.length} file(s)</div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"summary-section\">\n                <h3>💰 Pricing Breakdown</h3>\n                <div className=\"pricing-details\">\n                  <div className=\"pricing-item\">\n                    <span>Base Price ({formData.pages} pages × {formData.pricePerPage} TND)</span>\n                    <span>{finalPricing.basePrice} TND</span>\n                  </div>\n                  {formData.urgency !== 'standard' && (\n                    <div className=\"pricing-item\">\n                      <span>Urgency ({formData.urgency})</span>\n                      <span>+{((calculateTotal().total / finalPricing.basePrice - 1) * 100).toFixed(0)}%</span>\n                    </div>\n                  )}\n                  {selectedServices.map(service => (\n                    <div key={service} className=\"pricing-item\">\n                      <span>{service}</span>\n                      <span>Included</span>\n                    </div>\n                  ))}\n                  <div className=\"pricing-item total\">\n                    <span>Total Amount</span>\n                    <span className=\"total-price\">{finalPricing.total} TND</span>\n                  </div>\n                </div>\n              </div>\n\n              {errors.submit && (\n                <div className=\"error-banner\">\n                  <span>❌ {errors.submit}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        );\n      default:\n        return null;\n    }\n  };\n\n  const stepTitles = [\n    { title: 'Document Details', icon: '📋' },\n    { title: 'Service Options', icon: '⚙️' },\n    { title: 'Documents & Details', icon: '📄' },\n    { title: 'Review & Submit', icon: '✅' }\n  ];\n\n  return (\n    <div className=\"apply-translation-container\">\n      <div className=\"apply-translation\">\n        {/* Header */}\n        <div className=\"page-header\">\n          <button className=\"back-button\" onClick={() => navigate('/dashboard')}>\n            ← Back to Dashboard\n          </button>\n          <h1>Apply for Translation</h1>\n          <p>Submit your translation request in 4 easy steps</p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"progress-bar\">\n          <div className=\"progress-track\">\n            <div\n              className=\"progress-fill\"\n              style={{ width: `${((step - 1) / 3) * 100}%` }}\n            />\n          </div>\n          <div className=\"progress-steps\">\n            {stepTitles.map((stepInfo, index) => {\n              const stepNumber = index + 1;\n              return (\n                <div\n                  key={stepNumber}\n                  className={`step ${stepNumber === step ? 'active' : ''} ${stepNumber < step ? 'completed' : ''}`}\n                  onClick={() => stepNumber < step && setStep(stepNumber)}\n                >\n                  <div className=\"step-circle\">\n                    {stepNumber < step ? '✓' : stepInfo.icon}\n                  </div>\n                  <div className=\"step-label\">\n                    <div className=\"step-title\">{stepInfo.title}</div>\n                    <div className=\"step-number\">Step {stepNumber}</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Form Content */}\n        <form onSubmit={handleSubmit} className=\"translation-form\">\n          <div className=\"form-container\">\n            {renderStep()}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"form-navigation\">\n            <div className=\"nav-buttons\">\n              {step > 1 && (\n                <button\n                  type=\"button\"\n                  className=\"btn-secondary\"\n                  onClick={prevStep}\n                  disabled={isLoading}\n                >\n                  ← Previous\n                </button>\n              )}\n              <div className=\"nav-spacer\" />\n              {step < 4 ? (\n                <button\n                  type=\"button\"\n                  className=\"btn-primary\"\n                  onClick={nextStep}\n                  disabled={isLoading}\n                >\n                  Next →\n                </button>\n              ) : (\n                <button\n                  type=\"submit\"\n                  className=\"btn-submit\"\n                  disabled={isLoading}\n                >\n                  {isLoading ? (\n                    <>\n                      <div className=\"spinner\" />\n                      Submitting...\n                    </>\n                  ) : (\n                    <>\n                      Submit Request ✓\n                    </>\n                  )}\n                </button>\n              )}\n            </div>\n            <div className=\"step-indicator\">\n              Step {step} of 4\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default ApplyTranslation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,SAAS;IACvBC,KAAK,EAAE,CAAC;IACRC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,UAAU;IACnBC,kBAAkB,EAAE;MAClBC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,OAAO;IACtBC,mBAAmB,EAAE;EACvB,CAAC,CAAC;;EAEF;EACA,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC7C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC5C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC5C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC7C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC5C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC7C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAO,CAAC,EAChD;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC7C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAO,CAAC,EAC7C;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAO,CAAC,CAC/C;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC3D;IAAEF,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACvD;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/D;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC3D;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC7D;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC7D;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAE;EAAK,CAAC,CAChE;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEP,IAAI;MAAEG,KAAK;MAAEK,IAAI;MAAEC;IAAQ,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC/C/B,SAAS,CAACgC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACX,IAAI,GAAG;IAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9C,IAAIQ,IAAI,KAAK,UAAU,EAAE;MACvB3B,WAAW,CAAC8B,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPtB,kBAAkB,EAAE;UAClB,GAAGsB,IAAI,CAACtB,kBAAkB;UAC1B,CAACW,IAAI,GAAGS;QACV;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL5B,WAAW,CAAC8B,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACX,IAAI,GAAGG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMS,gBAAgB,GAAIL,CAAC,IAAK;IAC9B,MAAMM,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACR,CAAC,CAACG,MAAM,CAACG,KAAK,CAAC;IACxC,MAAMG,UAAU,GAAGH,KAAK,CAACI,MAAM,CAACC,IAAI,IAAI;MACtC,MAAMC,UAAU,GAAG,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,yEAAyE,EAAE,YAAY,CAAC;MACrJ,OAAOA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACV,IAAI,CAAC,IAAIU,IAAI,CAACG,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAC1E,CAAC,CAAC;IAEFxC,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhB,WAAW,EAAE,CAAC,GAAGgB,IAAI,CAAChB,WAAW,EAAE,GAAGqB,UAAU;IAClD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMM,UAAU,GAAIC,KAAK,IAAK;IAC5B1C,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhB,WAAW,EAAEgB,IAAI,CAAChB,WAAW,CAACsB,MAAM,CAAC,CAACO,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKF,KAAK;IAC5D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,SAAS,GAAG/C,QAAQ,CAACK,KAAK,GAAGL,QAAQ,CAACM,YAAY;IACtD,IAAI0C,KAAK,GAAGD,SAAS;;IAErB;IACA,MAAME,kBAAkB,GAAG;MACzBC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,GAAG;MACXC,SAAS,EAAE;IACb,CAAC;IACDL,KAAK,IAAIC,kBAAkB,CAACjD,QAAQ,CAACQ,OAAO,CAAC,IAAI,CAAC;;IAElD;IACA,IAAIR,QAAQ,CAACS,kBAAkB,CAACC,YAAY,EAAEsC,KAAK,IAAI,IAAI;IAC3D,IAAIhD,QAAQ,CAACS,kBAAkB,CAACE,UAAU,EAAEqC,KAAK,IAAI,IAAI;IACzD,IAAIhD,QAAQ,CAACS,kBAAkB,CAACG,aAAa,EAAEoC,KAAK,IAAI,IAAI;IAC5D,IAAIhD,QAAQ,CAACS,kBAAkB,CAACI,YAAY,EAAEmC,KAAK,IAAI,IAAI;IAE3D,OAAO;MACLD,SAAS,EAAEA,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC;MAC/BN,KAAK,EAAEA,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;MACvBC,OAAO,EAAER,SAAS,GAAGC,KAAK,GAAG,CAACD,SAAS,GAAGC,KAAK,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;IAChE,CAAC;EACH,CAAC;EAED,MAAME,YAAY,GAAIC,UAAU,IAAK;IACnC,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,QAAOD,UAAU;MACf,KAAK,CAAC;QACJ,IAAI,CAACzD,QAAQ,CAACE,cAAc,EAAEwD,SAAS,CAACxD,cAAc,GAAG,6BAA6B;QACtF,IAAI,CAACF,QAAQ,CAACG,cAAc,EAAEuD,SAAS,CAACvD,cAAc,GAAG,6BAA6B;QACtF,IAAIH,QAAQ,CAACE,cAAc,KAAKF,QAAQ,CAACG,cAAc,EAAEuD,SAAS,CAACvD,cAAc,GAAG,+CAA+C;QACnI,IAAI,CAACH,QAAQ,CAACI,YAAY,EAAEsD,SAAS,CAACtD,YAAY,GAAG,2BAA2B;QAChF;MACF,KAAK,CAAC;QACJ,IAAI,CAACJ,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,GAAG,CAAC,EAAEqD,SAAS,CAACrD,KAAK,GAAG,oCAAoC;QACjG,IAAI,CAACL,QAAQ,CAACM,YAAY,IAAIN,QAAQ,CAACM,YAAY,GAAG,EAAE,EAAEoD,SAAS,CAACpD,YAAY,GAAG,wCAAwC;QAC3H,IAAI,CAACN,QAAQ,CAACO,QAAQ,EAAEmD,SAAS,CAACnD,QAAQ,GAAG,sBAAsB;QACnE;MACF,KAAK,CAAC;QACJ,IAAI,CAACP,QAAQ,CAACc,WAAW,CAAC6C,IAAI,CAAC,CAAC,EAAED,SAAS,CAAC5C,WAAW,GAAG,iCAAiC;QAC3F,IAAId,QAAQ,CAACe,WAAW,CAAC6C,MAAM,KAAK,CAAC,EAAEF,SAAS,CAAC3C,WAAW,GAAG,wCAAwC;QACvG;IACJ;IAEAhB,SAAS,CAAC2D,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIP,YAAY,CAAC9D,IAAI,CAAC,EAAE;MACtBC,OAAO,CAACoC,IAAI,IAAIiC,IAAI,CAACC,GAAG,CAAClC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;EAED,MAAMmC,QAAQ,GAAGA,CAAA,KAAM;IACrBvE,OAAO,CAACoC,IAAI,IAAIiC,IAAI,CAACG,GAAG,CAACpC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,CAAC;EAED,MAAMqC,YAAY,GAAG,MAAOzC,CAAC,IAAK;IAChCA,CAAC,CAAC0C,cAAc,CAAC,CAAC;IAClB,IAAI,CAACb,YAAY,CAAC,CAAC,CAAC,EAAE;IAEtB3D,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF;MACA,MAAMyE,QAAQ,GAAG,MAAMC,KAAK,CAAC,2BAA2B,EAAE;QACxDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB,GAAG9E,QAAQ;UACX+E,UAAU,EAAEjC,cAAc,CAAC,CAAC,CAACE,KAAK;UAClCgC,MAAM,EAAE,SAAS;UACjBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACtC,CAAC;MACH,CAAC,CAAC;MAEF,IAAIb,QAAQ,CAACc,EAAE,EAAE;QACf3F,QAAQ,CAAC,YAAY,EAAE;UACrB4F,KAAK,EAAE;YACLC,OAAO,EAAE,6CAA6C;YACtD1D,IAAI,EAAE;UACR;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAI2D,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DzF,SAAS,CAAC;QAAE2F,MAAM,EAAE;MAA8C,CAAC,CAAC;IACtE,CAAC,SAAS;MACR7F,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAZ,SAAS,CAAC,MAAM;IACd6D,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC9C,QAAQ,CAACK,KAAK,EAAEL,QAAQ,CAACM,YAAY,EAAEN,QAAQ,CAACQ,OAAO,EAAER,QAAQ,CAACS,kBAAkB,CAAC,CAAC;EAE1F,MAAMkF,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACvB,QAAOrG,IAAI;MACT,KAAK,CAAC;QACJ,oBACEN,OAAA;UAAK4G,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7G,OAAA;YAAK4G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B7G,OAAA;cAAA6G,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BjH,OAAA;cAAA6G,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7G,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BjH,OAAA;gBAAK4G,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B7G,OAAA;kBACEgC,IAAI,EAAC,gBAAgB;kBACrBG,KAAK,EAAEvB,QAAQ,CAACE,cAAe;kBAC/BoG,QAAQ,EAAE5E,iBAAkB;kBAC5BsE,SAAS,EAAElG,MAAM,CAACI,cAAc,GAAG,OAAO,GAAG,EAAG;kBAAA+F,QAAA,gBAEhD7G,OAAA;oBAAQmC,KAAK,EAAC,EAAE;oBAAA0E,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC/CnF,SAAS,CAACqF,GAAG,CAACC,IAAI,iBACjBpH,OAAA;oBAAwBmC,KAAK,EAAEiF,IAAI,CAACrF,IAAK;oBAAA8E,QAAA,GACtCO,IAAI,CAACnF,IAAI,EAAC,GAAC,EAACmF,IAAI,CAACpF,IAAI;kBAAA,GADXoF,IAAI,CAACrF,IAAI;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRvG,MAAM,CAACI,cAAc,iBAAId,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEnG,MAAM,CAACI;gBAAc;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjH,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BjH,OAAA;gBAAK4G,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B7G,OAAA;kBACEgC,IAAI,EAAC,gBAAgB;kBACrBG,KAAK,EAAEvB,QAAQ,CAACG,cAAe;kBAC/BmG,QAAQ,EAAE5E,iBAAkB;kBAC5BsE,SAAS,EAAElG,MAAM,CAACK,cAAc,GAAG,OAAO,GAAG,EAAG;kBAAA8F,QAAA,gBAEhD7G,OAAA;oBAAQmC,KAAK,EAAC,EAAE;oBAAA0E,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC/CnF,SAAS,CAACqF,GAAG,CAACC,IAAI,iBACjBpH,OAAA;oBAAwBmC,KAAK,EAAEiF,IAAI,CAACrF,IAAK;oBAAA8E,QAAA,GACtCO,IAAI,CAACnF,IAAI,EAAC,GAAC,EAACmF,IAAI,CAACpF,IAAI;kBAAA,GADXoF,IAAI,CAACrF,IAAI;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRvG,MAAM,CAACK,cAAc,iBAAIf,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEnG,MAAM,CAACK;gBAAc;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7G,OAAA;cAAA6G,QAAA,EAAO;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5BjH,OAAA;cAAK4G,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAChC3E,aAAa,CAACiF,GAAG,CAAC3E,IAAI,iBACrBxC,OAAA;gBAEE4G,SAAS,EAAE,sBAAsBhG,QAAQ,CAACI,YAAY,KAAKwB,IAAI,CAACL,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC1FkF,OAAO,EAAEA,CAAA,KAAM/E,iBAAiB,CAAC;kBAAEI,MAAM,EAAE;oBAAEV,IAAI,EAAE,cAAc;oBAAEG,KAAK,EAAEK,IAAI,CAACL;kBAAM;gBAAE,CAAC,CAAE;gBAAA0E,QAAA,gBAE1F7G,OAAA;kBAAK4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAErE,IAAI,CAACH;gBAAI;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDjH,OAAA;kBAAK4G,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAErE,IAAI,CAACJ;gBAAK;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAL7CzE,IAAI,CAACL,KAAK;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMZ,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLvG,MAAM,CAACM,YAAY,iBAAIhB,OAAA;cAAM4G,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEnG,MAAM,CAACM;YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,MAAMK,OAAO,GAAG5D,cAAc,CAAC,CAAC;QAChC,oBACE1D,OAAA;UAAK4G,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7G,OAAA;YAAK4G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B7G,OAAA;cAAA6G,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BjH,OAAA;cAAA6G,QAAA,EAAG;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7G,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BjH,OAAA;gBAAK4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7G,OAAA;kBACEwC,IAAI,EAAC,QAAQ;kBACbR,IAAI,EAAC,OAAO;kBACZ6C,GAAG,EAAC,GAAG;kBACP1C,KAAK,EAAEvB,QAAQ,CAACK,KAAM;kBACtBiG,QAAQ,EAAE5E,iBAAkB;kBAC5BsE,SAAS,EAAElG,MAAM,CAACO,KAAK,GAAG,OAAO,GAAG,EAAG;kBACvCsG,WAAW,EAAC;gBAAuB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,EACDvG,MAAM,CAACO,KAAK,iBAAIjB,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEnG,MAAM,CAACO;gBAAK;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjH,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAO;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CjH,OAAA;gBAAK4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7G,OAAA;kBACEwC,IAAI,EAAC,QAAQ;kBACbR,IAAI,EAAC,cAAc;kBACnB6C,GAAG,EAAC,IAAI;kBACR1C,KAAK,EAAEvB,QAAQ,CAACM,YAAa;kBAC7BgG,QAAQ,EAAE5E,iBAAkB;kBAC5BsE,SAAS,EAAElG,MAAM,CAACQ,YAAY,GAAG,OAAO,GAAG,EAAG;kBAC9CqG,WAAW,EAAC;gBAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFjH,OAAA;kBAAM4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACxCvG,MAAM,CAACQ,YAAY,iBAAIlB,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEnG,MAAM,CAACQ;gBAAY;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7G,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBjH,OAAA;gBAAK4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7G,OAAA;kBACEwC,IAAI,EAAC,MAAM;kBACXR,IAAI,EAAC,UAAU;kBACfG,KAAK,EAAEvB,QAAQ,CAACO,QAAS;kBACzB+F,QAAQ,EAAE5E,iBAAkB;kBAC5BsE,SAAS,EAAElG,MAAM,CAACS,QAAQ,GAAG,OAAO,GAAG,EAAG;kBAC1C0D,GAAG,EAAE,IAAIiB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACyB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,EACDvG,MAAM,CAACS,QAAQ,iBAAInB,OAAA;kBAAM4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEnG,MAAM,CAACS;gBAAQ;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjH,OAAA;cAAK4G,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7G,OAAA;gBAAA6G,QAAA,EAAO;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BjH,OAAA;gBAAK4G,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC7B,CACC;kBAAE1E,KAAK,EAAE,UAAU;kBAAEC,KAAK,EAAE,UAAU;kBAAEqF,IAAI,EAAE,iBAAiB;kBAAEC,UAAU,EAAE;gBAAK,CAAC,EACnF;kBAAEvF,KAAK,EAAE,SAAS;kBAAEC,KAAK,EAAE,SAAS;kBAAEqF,IAAI,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAQ,CAAC,EAC7E;kBAAEvF,KAAK,EAAE,QAAQ;kBAAEC,KAAK,EAAE,QAAQ;kBAAEqF,IAAI,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAO,CAAC,EAC1E;kBAAEvF,KAAK,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAEqF,IAAI,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAK,CAAC,CAC/E,CAACP,GAAG,CAACQ,MAAM,iBACV3H,OAAA;kBAEE4G,SAAS,EAAE,gBAAgBhG,QAAQ,CAACQ,OAAO,KAAKuG,MAAM,CAACxF,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;kBACjFkF,OAAO,EAAEA,CAAA,KAAM/E,iBAAiB,CAAC;oBAAEI,MAAM,EAAE;sBAAEV,IAAI,EAAE,SAAS;sBAAEG,KAAK,EAAEwF,MAAM,CAACxF;oBAAM;kBAAE,CAAC,CAAE;kBAAA0E,QAAA,gBAEvF7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEc,MAAM,CAACvF;kBAAK;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnDjH,OAAA;oBAAK4G,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEc,MAAM,CAACF;kBAAI;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDjH,OAAA;oBAAK4G,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEc,MAAM,CAACD;kBAAU;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GANxDU,MAAM,CAACxF,KAAK;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7G,OAAA;cAAA6G,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BjH,OAAA;cAAK4G,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B,CACC;gBAAEe,GAAG,EAAE,cAAc;gBAAExF,KAAK,EAAE,cAAc;gBAAEqF,IAAI,EAAE,gCAAgC;gBAAEI,KAAK,EAAE,MAAM;gBAAExF,IAAI,EAAE;cAAK,CAAC,EACjH;gBAAEuF,GAAG,EAAE,YAAY;gBAAExF,KAAK,EAAE,YAAY;gBAAEqF,IAAI,EAAE,6BAA6B;gBAAEI,KAAK,EAAE,MAAM;gBAAExF,IAAI,EAAE;cAAK,CAAC,EAC1G;gBAAEuF,GAAG,EAAE,eAAe;gBAAExF,KAAK,EAAE,eAAe;gBAAEqF,IAAI,EAAE,gCAAgC;gBAAEI,KAAK,EAAE,MAAM;gBAAExF,IAAI,EAAE;cAAK,CAAC,EACnH;gBAAEuF,GAAG,EAAE,cAAc;gBAAExF,KAAK,EAAE,cAAc;gBAAEqF,IAAI,EAAE,4BAA4B;gBAAEI,KAAK,EAAE,MAAM;gBAAExF,IAAI,EAAE;cAAK,CAAC,CAC9G,CAAC8E,GAAG,CAACW,OAAO,iBACX9H,OAAA;gBAEE4G,SAAS,EAAE,gBAAgBhG,QAAQ,CAACS,kBAAkB,CAACyG,OAAO,CAACF,GAAG,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;gBACxFP,OAAO,EAAEA,CAAA,KAAM/E,iBAAiB,CAAC;kBAC/BI,MAAM,EAAE;oBACNV,IAAI,EAAE8F,OAAO,CAACF,GAAG;oBACjBpF,IAAI,EAAE,UAAU;oBAChBC,OAAO,EAAE,CAAC7B,QAAQ,CAACS,kBAAkB,CAACyG,OAAO,CAACF,GAAG;kBACnD;gBACF,CAAC,CAAE;gBAAAf,QAAA,gBAEH7G,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEiB,OAAO,CAACzF;gBAAI;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEiB,OAAO,CAAC1F;kBAAK;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDjH,OAAA;oBAAK4G,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEiB,OAAO,CAACL;kBAAI;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEiB,OAAO,CAACD;gBAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAf/Ca,OAAO,CAACF,GAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B7G,OAAA;cAAK4G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7G,OAAA;gBAAK4G,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7G,OAAA;kBAAA6G,QAAA,GAAM,cAAY,EAACjG,QAAQ,CAACK,KAAK,EAAC,cAAS,EAACL,QAAQ,CAACM,YAAY,EAAC,OAAK;gBAAA;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EjH,OAAA;kBAAA6G,QAAA,GAAOS,OAAO,CAAC3D,SAAS,EAAC,MAAI;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNjH,OAAA;gBAAK4G,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B7G,OAAA;kBAAA6G,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBjH,OAAA;kBAAM4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAES,OAAO,CAAC1D,KAAK,EAAC,MAAI;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,oBACEjH,OAAA;UAAK4G,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7G,OAAA;YAAK4G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B7G,OAAA;cAAA6G,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BjH,OAAA;cAAA6G,QAAA,EAAG;YAAwD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7G,OAAA;cAAA6G,QAAA,EAAO;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClCjH,OAAA;cAAK4G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B7G,OAAA;gBACEgC,IAAI,EAAC,aAAa;gBAClBG,KAAK,EAAEvB,QAAQ,CAACc,WAAY;gBAC5BwF,QAAQ,EAAE5E,iBAAkB;gBAC5BiF,WAAW,EAAC,6HAA6H;gBACzIQ,IAAI,EAAC,GAAG;gBACRnB,SAAS,EAAElG,MAAM,CAACgB,WAAW,GAAG,OAAO,GAAG;cAAG;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACFjH,OAAA;gBAAK4G,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAEjG,QAAQ,CAACc,WAAW,CAAC8C,MAAM,EAAC,MAAI;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAClEvG,MAAM,CAACgB,WAAW,iBAAI1B,OAAA;gBAAM4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEnG,MAAM,CAACgB;cAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7G,OAAA;cAAA6G,QAAA,EAAO;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BjH,OAAA;cAAK4G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B7G,OAAA;gBACEwC,IAAI,EAAC,MAAM;gBACXwF,QAAQ;gBACRd,QAAQ,EAAEtE,gBAAiB;gBAC3BqF,MAAM,EAAC,sBAAsB;gBAC7BC,EAAE,EAAC,aAAa;gBAChBtB,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACFjH,OAAA;gBAAOmI,OAAO,EAAC,aAAa;gBAACvB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACxD7G,OAAA;kBAAK4G,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCjH,OAAA;kBAAK4G,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1B7G,OAAA;oBAAA6G,QAAA,EAAK;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3CjH,OAAA;oBAAK4G,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAmC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACPvG,MAAM,CAACiB,WAAW,iBAAI3B,OAAA;gBAAM4G,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEnG,MAAM,CAACiB;cAAW;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,EAELrG,QAAQ,CAACe,WAAW,CAAC6C,MAAM,GAAG,CAAC,iBAC9BxE,OAAA;cAAK4G,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7G,OAAA;gBAAA6G,QAAA,GAAI,kBAAgB,EAACjG,QAAQ,CAACe,WAAW,CAAC6C,MAAM,EAAC,GAAC;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACtDrG,QAAQ,CAACe,WAAW,CAACwF,GAAG,CAAC,CAACjE,IAAI,EAAEK,KAAK,kBACpCvD,OAAA;gBAAiB4G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACpC7G,OAAA;kBAAK4G,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB7G,OAAA;oBAAK4G,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnCjH,OAAA;oBAAK4G,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B7G,OAAA;sBAAK4G,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAE3D,IAAI,CAAClB;oBAAI;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CjH,OAAA;sBAAK4G,SAAS,EAAC,WAAW;sBAAAC,QAAA,GAAE,CAAC3D,IAAI,CAACG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjH,OAAA;kBACEwC,IAAI,EAAC,QAAQ;kBACboE,SAAS,EAAC,aAAa;kBACvBS,OAAO,EAAEA,CAAA,KAAM/D,UAAU,CAACC,KAAK,CAAE;kBAAAsD,QAAA,EAClC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAdD1D,KAAK;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7G,OAAA;cAAA6G,QAAA,EAAO;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9CjH,OAAA;cAAK4G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B7G,OAAA;gBACEgC,IAAI,EAAC,qBAAqB;gBAC1BG,KAAK,EAAEvB,QAAQ,CAACiB,mBAAoB;gBACpCqF,QAAQ,EAAE5E,iBAAkB;gBAC5BiF,WAAW,EAAC,8EAA8E;gBAC1FQ,IAAI,EAAC;cAAG;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7G,OAAA;cAAA6G,QAAA,EAAO;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvCjH,OAAA;cAAK4G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7B,CACC;gBAAE1E,KAAK,EAAE,OAAO;gBAAEC,KAAK,EAAE,OAAO;gBAAEC,IAAI,EAAE;cAAK,CAAC,EAC9C;gBAAEF,KAAK,EAAE,OAAO;gBAAEC,KAAK,EAAE,OAAO;gBAAEC,IAAI,EAAE;cAAK,CAAC,EAC9C;gBAAEF,KAAK,EAAE,UAAU;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,IAAI,EAAE;cAAK,CAAC,CACrD,CAAC8E,GAAG,CAAC/B,MAAM,iBACVpF,OAAA;gBAEE4G,SAAS,EAAE,kBAAkBhG,QAAQ,CAACgB,aAAa,KAAKwD,MAAM,CAACjD,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;gBACzFkF,OAAO,EAAEA,CAAA,KAAM/E,iBAAiB,CAAC;kBAAEI,MAAM,EAAE;oBAAEV,IAAI,EAAE,eAAe;oBAAEG,KAAK,EAAEiD,MAAM,CAACjD;kBAAM;gBAAE,CAAC,CAAE;gBAAA0E,QAAA,gBAE7F7G,OAAA;kBAAK4G,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEzB,MAAM,CAAC/C;gBAAI;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEzB,MAAM,CAAChD;gBAAK;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAL7C7B,MAAM,CAACjD,KAAK;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QACJ,MAAMmB,YAAY,GAAG1E,cAAc,CAAC,CAAC;QACrC,MAAM2E,iBAAiB,GAAG;UACxBC,MAAM,EAAExG,SAAS,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzG,IAAI,KAAKnB,QAAQ,CAACE,cAAc,CAAC;UAC/D4B,MAAM,EAAEZ,SAAS,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzG,IAAI,KAAKnB,QAAQ,CAACG,cAAc;QAChE,CAAC;QACD,MAAM0H,eAAe,GAAGvG,aAAa,CAACqG,IAAI,CAACG,CAAC,IAAIA,CAAC,CAACvG,KAAK,KAAKvB,QAAQ,CAACI,YAAY,CAAC;QAClF,MAAM2H,gBAAgB,GAAGlE,MAAM,CAACmE,OAAO,CAAChI,QAAQ,CAACS,kBAAkB,CAAC,CACjE4B,MAAM,CAAC,CAAC,GAAGd,KAAK,CAAC,KAAKA,KAAK,CAAC,CAC5BgF,GAAG,CAAC,CAAC,CAACS,GAAG,CAAC,KAAKA,GAAG,CAAC;QAEtB,oBACE5H,OAAA;UAAK4G,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7G,OAAA;YAAK4G,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B7G,OAAA;cAAA6G,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BjH,OAAA;cAAA6G,QAAA,EAAG;YAAwD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAENjH,OAAA;YAAK4G,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7G,OAAA;cAAK4G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7G,OAAA;gBAAA6G,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BjH,OAAA;gBAAK4G,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7G,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAL,qBAAA,GAC3B6B,iBAAiB,CAACC,MAAM,cAAA9B,qBAAA,uBAAxBA,qBAAA,CAA0BvE,IAAI,EAAC,GAAC,GAAAwE,sBAAA,GAAC4B,iBAAiB,CAACC,MAAM,cAAA7B,sBAAA,uBAAxBA,sBAAA,CAA0BzE,IAAI,EAAC,UAAG,GAAA0E,qBAAA,GAAC2B,iBAAiB,CAAC3F,MAAM,cAAAgE,qBAAA,uBAAxBA,qBAAA,CAA0BzE,IAAI,EAAC,GAAC,GAAA0E,sBAAA,GAAC0B,iBAAiB,CAAC3F,MAAM,cAAAiE,sBAAA,uBAAxBA,sBAAA,CAA0B3E,IAAI;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClDjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAC3B4B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEpG,IAAI,EAAC,GAAC,EAACoG,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAErG,KAAK;kBAAA;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAEjG,QAAQ,CAACK,KAAK,EAAC,QAAM;kBAAA;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7CjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAE,IAAIf,IAAI,CAAClF,QAAQ,CAACO,QAAQ,CAAC,CAAC0H,kBAAkB,CAAC;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjH,OAAA;cAAK4G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7G,OAAA;gBAAA6G,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BjH,OAAA;gBAAK4G,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7G,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5CjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEjG,QAAQ,CAACQ;kBAAO;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3B8B,gBAAgB,CAACnE,MAAM,GAAG,CAAC,GAAGmE,gBAAgB,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG;kBAAM;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEjG,QAAQ,CAACgB;kBAAa;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACNjH,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CjH,OAAA;oBAAK4G,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAEjG,QAAQ,CAACe,WAAW,CAAC6C,MAAM,EAAC,UAAQ;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjH,OAAA;cAAK4G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7G,OAAA;gBAAA6G,QAAA,EAAI;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BjH,OAAA;gBAAK4G,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B7G,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAA6G,QAAA,GAAM,cAAY,EAACjG,QAAQ,CAACK,KAAK,EAAC,cAAS,EAACL,QAAQ,CAACM,YAAY,EAAC,OAAK;kBAAA;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EjH,OAAA;oBAAA6G,QAAA,GAAOuB,YAAY,CAACzE,SAAS,EAAC,MAAI;kBAAA;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,EACLrG,QAAQ,CAACQ,OAAO,KAAK,UAAU,iBAC9BpB,OAAA;kBAAK4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7G,OAAA;oBAAA6G,QAAA,GAAM,WAAS,EAACjG,QAAQ,CAACQ,OAAO,EAAC,GAAC;kBAAA;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCjH,OAAA;oBAAA6G,QAAA,GAAM,GAAC,EAAC,CAAC,CAACnD,cAAc,CAAC,CAAC,CAACE,KAAK,GAAGwE,YAAY,CAACzE,SAAS,GAAG,CAAC,IAAI,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CACN,EACA0B,gBAAgB,CAACxB,GAAG,CAACW,OAAO,iBAC3B9H,OAAA;kBAAmB4G,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzC7G,OAAA;oBAAA6G,QAAA,EAAOiB;kBAAO;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtBjH,OAAA;oBAAA6G,QAAA,EAAM;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAFba,OAAO;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGZ,CACN,CAAC,eACFjH,OAAA;kBAAK4G,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACjC7G,OAAA;oBAAA6G,QAAA,EAAM;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzBjH,OAAA;oBAAM4G,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAEuB,YAAY,CAACxE,KAAK,EAAC,MAAI;kBAAA;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELvG,MAAM,CAAC4F,MAAM,iBACZtG,OAAA;cAAK4G,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B7G,OAAA;gBAAA6G,QAAA,GAAM,SAAE,EAACnG,MAAM,CAAC4F,MAAM;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAM8B,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,kBAAkB;IAAE3G,IAAI,EAAE;EAAK,CAAC,EACzC;IAAE2G,KAAK,EAAE,iBAAiB;IAAE3G,IAAI,EAAE;EAAK,CAAC,EACxC;IAAE2G,KAAK,EAAE,qBAAqB;IAAE3G,IAAI,EAAE;EAAK,CAAC,EAC5C;IAAE2G,KAAK,EAAE,iBAAiB;IAAE3G,IAAI,EAAE;EAAI,CAAC,CACxC;EAED,oBACErC,OAAA;IAAK4G,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eAC1C7G,OAAA;MAAK4G,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhC7G,OAAA;QAAK4G,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7G,OAAA;UAAQ4G,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAMhH,QAAQ,CAAC,YAAY,CAAE;UAAAwG,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjH,OAAA;UAAA6G,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BjH,OAAA;UAAA6G,QAAA,EAAG;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAGNjH,OAAA;QAAK4G,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7G,OAAA;UAAK4G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B7G,OAAA;YACE4G,SAAS,EAAC,eAAe;YACzBqC,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAI,CAAC5I,IAAI,GAAG,CAAC,IAAI,CAAC,GAAI,GAAG;YAAI;UAAE;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjH,OAAA;UAAK4G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BkC,UAAU,CAAC5B,GAAG,CAAC,CAACgC,QAAQ,EAAE5F,KAAK,KAAK;YACnC,MAAMc,UAAU,GAAGd,KAAK,GAAG,CAAC;YAC5B,oBACEvD,OAAA;cAEE4G,SAAS,EAAE,QAAQvC,UAAU,KAAK/D,IAAI,GAAG,QAAQ,GAAG,EAAE,IAAI+D,UAAU,GAAG/D,IAAI,GAAG,WAAW,GAAG,EAAE,EAAG;cACjG+G,OAAO,EAAEA,CAAA,KAAMhD,UAAU,GAAG/D,IAAI,IAAIC,OAAO,CAAC8D,UAAU,CAAE;cAAAwC,QAAA,gBAExD7G,OAAA;gBAAK4G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBxC,UAAU,GAAG/D,IAAI,GAAG,GAAG,GAAG6I,QAAQ,CAAC9G;cAAI;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNjH,OAAA;gBAAK4G,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7G,OAAA;kBAAK4G,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEsC,QAAQ,CAACH;gBAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDjH,OAAA;kBAAK4G,SAAS,EAAC,aAAa;kBAAAC,QAAA,GAAC,OAAK,EAACxC,UAAU;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA,GAVD5C,UAAU;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWZ,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjH,OAAA;QAAMoJ,QAAQ,EAAEpE,YAAa;QAAC4B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACxD7G,OAAA;UAAK4G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BN,UAAU,CAAC;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNjH,OAAA;UAAK4G,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7G,OAAA;YAAK4G,SAAS,EAAC,aAAa;YAAAC,QAAA,GACzBvG,IAAI,GAAG,CAAC,iBACPN,OAAA;cACEwC,IAAI,EAAC,QAAQ;cACboE,SAAS,EAAC,eAAe;cACzBS,OAAO,EAAEvC,QAAS;cAClBuE,QAAQ,EAAE7I,SAAU;cAAAqG,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDjH,OAAA;cAAK4G,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7B3G,IAAI,GAAG,CAAC,gBACPN,OAAA;cACEwC,IAAI,EAAC,QAAQ;cACboE,SAAS,EAAC,aAAa;cACvBS,OAAO,EAAE1C,QAAS;cAClB0E,QAAQ,EAAE7I,SAAU;cAAAqG,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETjH,OAAA;cACEwC,IAAI,EAAC,QAAQ;cACboE,SAAS,EAAC,YAAY;cACtByC,QAAQ,EAAE7I,SAAU;cAAAqG,QAAA,EAEnBrG,SAAS,gBACRR,OAAA,CAAAE,SAAA;gBAAA2G,QAAA,gBACE7G,OAAA;kBAAK4G,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAE7B;cAAA,eAAE,CAAC,gBAEHjH,OAAA,CAAAE,SAAA;gBAAA2G,QAAA,EAAE;cAEF,gBAAE;YACH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNjH,OAAA;YAAK4G,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,OACzB,EAACvG,IAAI,EAAC,OACb;UAAA;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7G,EAAA,CArsBID,gBAAgB;EAAA,QACHL,WAAW;AAAA;AAAAwJ,EAAA,GADxBnJ,gBAAgB;AAusBtB,eAAeA,gBAAgB;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}