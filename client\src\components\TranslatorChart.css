.translator-chart-container {
  width: 100%;
  height: 300px;
  margin: 20px 0;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.translator-chart {
  width: 100%;
  height: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .translator-chart-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .translator-chart-container {
    height: 200px;
  }
}
