.onboarding-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.5s ease;
}

.onboarding-container {
  background-color: var(--card-bg);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 800px;
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  overflow: hidden;
  animation: scaleIn 0.5s ease;
}

.onboarding-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 10;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--text-muted);
  transition: all 0.2s ease;
}

.onboarding-close:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.onboarding-content {
  display: flex;
  flex-direction: column;
  padding: 40px;
}

.onboarding-image {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.onboarding-image img {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}

.onboarding-text {
  text-align: center;
  margin-bottom: 30px;
}

.onboarding-text h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.onboarding-text p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.onboarding-progress {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 30px;
}

.progress-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.progress-dot.active {
  background-color: var(--accent-color);
  transform: scale(1.2);
}

.onboarding-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn-primary {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--accent-color-dark);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.btn-skip {
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 1rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.btn-skip:hover {
  color: var(--text-primary);
  text-decoration: underline;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .onboarding-container {
    width: 95vw;
    max-height: 95vh;
  }
  
  .onboarding-content {
    padding: 20px;
  }
  
  .onboarding-text h2 {
    font-size: 1.5rem;
  }
  
  .onboarding-text p {
    font-size: 1rem;
  }
  
  .onboarding-actions {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .btn-skip {
    order: -1;
    width: 100%;
    margin-bottom: 10px;
  }
}
