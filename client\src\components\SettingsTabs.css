.settings-content {
  padding: 0;
}

.settings-tabs {
  display: flex;
  flex-direction: column;
}

.settings-tab-list {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  flex-wrap: wrap; /* Allow tabs to wrap to next line if needed */
  scrollbar-width: none; /* Firefox */
}

.settings-tab-list::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.settings-tab {
  padding: 12px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-color-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.settings-tab:hover {
  color: var(--text-color);
}

.settings-tab.active {
  color: var(--accent-color);
  border-bottom: 2px solid var(--accent-color);
}

.settings-tab-content {
  padding: 0 16px 16px;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100%; /* Ensure content doesn't exceed container width */
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-color);
}
