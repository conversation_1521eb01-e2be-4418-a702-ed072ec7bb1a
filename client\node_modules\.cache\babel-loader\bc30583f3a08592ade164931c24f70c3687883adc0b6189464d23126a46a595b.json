{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lingolink\\\\client\\\\src\\\\components\\\\ProfileCustomizationPopup.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './ProfileCustomizationPopup.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileCustomizationPopup = ({\n  isVisible,\n  onComplete,\n  onSave,\n  userType,\n  initialData = {}\n}) => {\n  _s();\n  var _initialData$notifica, _initialData$notifica2, _initialData$notifica3, _initialData$notifica4, _initialData$notifica5, _initialData$notifica6, _initialData$privacy$, _initialData$privacy, _initialData$privacy$2, _initialData$privacy2, _initialData$privacy3;\n  const [currentStep, setCurrentStep] = useState(0);\n  const [formData, setFormData] = useState({\n    // Basic Info\n    fullName: initialData.fullName || '',\n    bio: initialData.bio || '',\n    location: initialData.location || '',\n    phone: initialData.phone || '',\n    profileImage: initialData.profileImage || null,\n    // Translator specific\n    languages: initialData.languages || [],\n    specializations: initialData.specializations || [],\n    yearsOfExperience: initialData.yearsOfExperience || '',\n    hourlyRate: initialData.hourlyRate || '',\n    certifications: initialData.certifications || '',\n    // Client specific\n    companyName: initialData.companyName || '',\n    industry: initialData.industry || '',\n    projectTypes: initialData.projectTypes || [],\n    // Preferences\n    notifications: {\n      email: (_initialData$notifica = (_initialData$notifica2 = initialData.notifications) === null || _initialData$notifica2 === void 0 ? void 0 : _initialData$notifica2.email) !== null && _initialData$notifica !== void 0 ? _initialData$notifica : true,\n      browser: (_initialData$notifica3 = (_initialData$notifica4 = initialData.notifications) === null || _initialData$notifica4 === void 0 ? void 0 : _initialData$notifica4.browser) !== null && _initialData$notifica3 !== void 0 ? _initialData$notifica3 : true,\n      sms: (_initialData$notifica5 = (_initialData$notifica6 = initialData.notifications) === null || _initialData$notifica6 === void 0 ? void 0 : _initialData$notifica6.sms) !== null && _initialData$notifica5 !== void 0 ? _initialData$notifica5 : false\n    },\n    privacy: {\n      showEmail: (_initialData$privacy$ = (_initialData$privacy = initialData.privacy) === null || _initialData$privacy === void 0 ? void 0 : _initialData$privacy.showEmail) !== null && _initialData$privacy$ !== void 0 ? _initialData$privacy$ : false,\n      showPhone: (_initialData$privacy$2 = (_initialData$privacy2 = initialData.privacy) === null || _initialData$privacy2 === void 0 ? void 0 : _initialData$privacy2.showPhone) !== null && _initialData$privacy$2 !== void 0 ? _initialData$privacy$2 : false,\n      profileVisibility: ((_initialData$privacy3 = initialData.privacy) === null || _initialData$privacy3 === void 0 ? void 0 : _initialData$privacy3.profileVisibility) || 'public'\n    }\n  });\n  const [isLoading, setSaving] = useState(false);\n  const [errors, setErrors] = useState({});\n  const availableLanguages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Chinese', 'Japanese', 'Korean', 'Arabic', 'Hindi'];\n  const availableSpecializations = ['Technical Translation', 'Legal Documents', 'Medical Translation', 'Business Communication', 'Marketing Content', 'Academic Papers', 'Literary Translation', 'Website Localization', 'Software Localization'];\n  const availableIndustries = ['Technology', 'Healthcare', 'Finance', 'Legal', 'Education', 'Manufacturing', 'Retail', 'Media', 'Government', 'Non-profit'];\n  const steps = userType === 'translator' ? [{\n    title: \"Basic Information\",\n    subtitle: \"Tell us about yourself\",\n    fields: ['fullName', 'bio', 'location', 'phone']\n  }, {\n    title: \"Professional Details\",\n    subtitle: \"Your expertise and experience\",\n    fields: ['languages', 'specializations', 'yearsOfExperience', 'hourlyRate', 'certifications']\n  }, {\n    title: \"Preferences & Privacy\",\n    subtitle: \"Customize your experience\",\n    fields: ['notifications', 'privacy']\n  }] : [{\n    title: \"Basic Information\",\n    subtitle: \"Tell us about yourself\",\n    fields: ['fullName', 'bio', 'location', 'phone']\n  }, {\n    title: \"Business Details\",\n    subtitle: \"Your company and projects\",\n    fields: ['companyName', 'industry', 'projectTypes']\n  }, {\n    title: \"Preferences & Privacy\",\n    subtitle: \"Customize your experience\",\n    fields: ['notifications', 'privacy']\n  }];\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleNestedChange = (parent, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [parent]: {\n        ...prev[parent],\n        [field]: value\n      }\n    }));\n  };\n  const handleArrayChange = (field, value, isAdd = true) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: isAdd ? [...prev[field], value] : prev[field].filter(item => item !== value)\n    }));\n  };\n  const validateStep = stepIndex => {\n    const stepFields = steps[stepIndex].fields;\n    const stepErrors = {};\n    stepFields.forEach(field => {\n      if (field === 'fullName' && !formData.fullName.trim()) {\n        stepErrors.fullName = 'Full name is required';\n      }\n      if (field === 'languages' && userType === 'translator' && formData.languages.length === 0) {\n        stepErrors.languages = 'Please select at least one language';\n      }\n      if (field === 'specializations' && userType === 'translator' && formData.specializations.length === 0) {\n        stepErrors.specializations = 'Please select at least one specialization';\n      }\n    });\n    setErrors(stepErrors);\n    return Object.keys(stepErrors).length === 0;\n  };\n  const nextStep = () => {\n    if (validateStep(currentStep)) {\n      if (currentStep < steps.length - 1) {\n        setCurrentStep(currentStep + 1);\n      } else {\n        handleSave();\n      }\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      await onSave(formData);\n      onComplete();\n    } catch (error) {\n      console.error('Error saving profile:', error);\n      setErrors({\n        general: 'Failed to save profile. Please try again.'\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleImageUpload = event => {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        handleInputChange('profileImage', e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  if (!isVisible) return null;\n  const currentStepData = steps[currentStep];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-customization-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-customization-popup\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"customization-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"customization-progress\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${(currentStep + 1) / steps.length * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"progress-text\",\n            children: [\"Step \", currentStep + 1, \" of \", steps.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"customization-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"step-title\",\n            children: currentStepData.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"step-subtitle\",\n            children: currentStepData.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step-form\",\n          children: [currentStep === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-image-upload\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-preview\",\n                children: formData.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: formData.profileImage,\n                  alt: \"Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-placeholder\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"40\",\n                    height: \"40\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"7\",\n                      r: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"profileImage\",\n                accept: \"image/*\",\n                onChange: handleImageUpload,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"profileImage\",\n                className: \"upload-btn\",\n                children: \"Upload Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Full Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.fullName,\n                onChange: e => handleInputChange('fullName', e.target.value),\n                placeholder: \"Enter your full name\",\n                className: errors.fullName ? 'error' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), errors.fullName && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"error-text\",\n                children: errors.fullName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Bio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.bio,\n                onChange: e => handleInputChange('bio', e.target.value),\n                placeholder: \"Tell us about yourself...\",\n                rows: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Location\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.location,\n                  onChange: e => handleInputChange('location', e.target.value),\n                  placeholder: \"City, Country\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: formData.phone,\n                  onChange: e => handleInputChange('phone', e.target.value),\n                  placeholder: \"+****************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), currentStep === 1 && userType === 'translator' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Languages *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-grid\",\n                children: availableLanguages.map(lang => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.languages.includes(lang),\n                    onChange: e => handleArrayChange('languages', lang, e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: lang\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this)]\n                }, lang, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), errors.languages && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"error-text\",\n                children: errors.languages\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Specializations *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-grid\",\n                children: availableSpecializations.map(spec => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.specializations.includes(spec),\n                    onChange: e => handleArrayChange('specializations', spec, e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: spec\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this)]\n                }, spec, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), errors.specializations && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"error-text\",\n                children: errors.specializations\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 46\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Years of Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.yearsOfExperience,\n                  onChange: e => handleInputChange('yearsOfExperience', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0-1\",\n                    children: \"0-1 years\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"2-5\",\n                    children: \"2-5 years\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"6-10\",\n                    children: \"6-10 years\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"10+\",\n                    children: \"10+ years\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Hourly Rate (USD)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: formData.hourlyRate,\n                  onChange: e => handleInputChange('hourlyRate', e.target.value),\n                  placeholder: \"25\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Certifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.certifications,\n                onChange: e => handleInputChange('certifications', e.target.value),\n                placeholder: \"e.g., ATA Certified, CAT Tools Expert\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), currentStep === 1 && userType === 'client' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.companyName,\n                onChange: e => handleInputChange('companyName', e.target.value),\n                placeholder: \"Your company name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Industry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: formData.industry,\n                onChange: e => handleInputChange('industry', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select industry\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), availableIndustries.map(industry => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: industry,\n                  children: industry\n                }, industry, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Typical Project Types\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-grid\",\n                children: ['Documents', 'Websites', 'Marketing Materials', 'Legal Contracts', 'Technical Manuals', 'Academic Papers'].map(type => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.projectTypes.includes(type),\n                    onChange: e => handleArrayChange('projectTypes', type, e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 25\n                  }, this)]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this), currentStep === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Notification Preferences\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preference-options\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"preference-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.notifications.email,\n                    onChange: e => handleNestedChange('notifications', 'email', e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Email notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"preference-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.notifications.browser,\n                    onChange: e => handleNestedChange('notifications', 'browser', e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Browser notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"preference-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.notifications.sms,\n                    onChange: e => handleNestedChange('notifications', 'sms', e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"SMS notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Privacy Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preference-options\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"preference-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.privacy.showEmail,\n                    onChange: e => handleNestedChange('privacy', 'showEmail', e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Show email in profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"preference-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.privacy.showPhone,\n                    onChange: e => handleNestedChange('privacy', 'showPhone', e.target.checked)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Show phone in profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Profile Visibility\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.privacy.profileVisibility,\n                  onChange: e => handleNestedChange('privacy', 'profileVisibility', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"public\",\n                    children: \"Public - Visible to everyone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"registered\",\n                    children: \"Registered users only\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"private\",\n                    children: \"Private - Only you can see\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-message\",\n            children: errors.general\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"customization-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"customization-btn secondary\",\n          onClick: prevStep,\n          disabled: currentStep === 0,\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"customization-btn primary\",\n          onClick: nextStep,\n          disabled: isLoading,\n          children: isLoading ? 'Saving...' : currentStep === steps.length - 1 ? 'Complete Setup' : 'Next'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfileCustomizationPopup, \"aHb+48f95kzUEzsDe1t44q5oyBk=\");\n_c = ProfileCustomizationPopup;\nexport default ProfileCustomizationPopup;\nvar _c;\n$RefreshReg$(_c, \"ProfileCustomizationPopup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ProfileCustomizationPopup", "isVisible", "onComplete", "onSave", "userType", "initialData", "_s", "_initialData$notifica", "_initialData$notifica2", "_initialData$notifica3", "_initialData$notifica4", "_initialData$notifica5", "_initialData$notifica6", "_initialData$privacy$", "_initialData$privacy", "_initialData$privacy$2", "_initialData$privacy2", "_initialData$privacy3", "currentStep", "setCurrentStep", "formData", "setFormData", "fullName", "bio", "location", "phone", "profileImage", "languages", "specializations", "yearsOfExperience", "hourlyRate", "certifications", "companyName", "industry", "projectTypes", "notifications", "email", "browser", "sms", "privacy", "showEmail", "showPhone", "profileVisibility", "isLoading", "setSaving", "errors", "setErrors", "availableLanguages", "availableSpecializations", "availableIndustries", "steps", "title", "subtitle", "fields", "handleInputChange", "field", "value", "prev", "handleNestedChange", "parent", "handleArrayChange", "isAdd", "filter", "item", "validateStep", "stepIndex", "stepFields", "stepErrors", "for<PERSON>ach", "trim", "length", "Object", "keys", "nextStep", "handleSave", "prevStep", "error", "console", "general", "handleImageUpload", "event", "file", "target", "files", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "currentStepData", "className", "children", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "xmlns", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "cx", "cy", "r", "type", "id", "accept", "onChange", "display", "htmlFor", "placeholder", "rows", "map", "lang", "checked", "includes", "spec", "min", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lingolink/client/src/components/ProfileCustomizationPopup.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ProfileCustomizationPopup.css';\n\nconst ProfileCustomizationPopup = ({ isVisible, onComplete, onSave, userType, initialData = {} }) => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [formData, setFormData] = useState({\n    // Basic Info\n    fullName: initialData.fullName || '',\n    bio: initialData.bio || '',\n    location: initialData.location || '',\n    phone: initialData.phone || '',\n    profileImage: initialData.profileImage || null,\n    \n    // Translator specific\n    languages: initialData.languages || [],\n    specializations: initialData.specializations || [],\n    yearsOfExperience: initialData.yearsOfExperience || '',\n    hourlyRate: initialData.hourlyRate || '',\n    certifications: initialData.certifications || '',\n    \n    // Client specific\n    companyName: initialData.companyName || '',\n    industry: initialData.industry || '',\n    projectTypes: initialData.projectTypes || [],\n    \n    // Preferences\n    notifications: {\n      email: initialData.notifications?.email ?? true,\n      browser: initialData.notifications?.browser ?? true,\n      sms: initialData.notifications?.sms ?? false\n    },\n    privacy: {\n      showEmail: initialData.privacy?.showEmail ?? false,\n      showPhone: initialData.privacy?.showPhone ?? false,\n      profileVisibility: initialData.privacy?.profileVisibility || 'public'\n    }\n  });\n\n  const [isLoading, setSaving] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  const availableLanguages = [\n    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', \n    'Russian', 'Chinese', 'Japanese', 'Korean', 'Arabic', 'Hindi'\n  ];\n\n  const availableSpecializations = [\n    'Technical Translation', 'Legal Documents', 'Medical Translation',\n    'Business Communication', 'Marketing Content', 'Academic Papers',\n    'Literary Translation', 'Website Localization', 'Software Localization'\n  ];\n\n  const availableIndustries = [\n    'Technology', 'Healthcare', 'Finance', 'Legal', 'Education',\n    'Manufacturing', 'Retail', 'Media', 'Government', 'Non-profit'\n  ];\n\n  const steps = userType === 'translator' ? [\n    {\n      title: \"Basic Information\",\n      subtitle: \"Tell us about yourself\",\n      fields: ['fullName', 'bio', 'location', 'phone']\n    },\n    {\n      title: \"Professional Details\",\n      subtitle: \"Your expertise and experience\",\n      fields: ['languages', 'specializations', 'yearsOfExperience', 'hourlyRate', 'certifications']\n    },\n    {\n      title: \"Preferences & Privacy\",\n      subtitle: \"Customize your experience\",\n      fields: ['notifications', 'privacy']\n    }\n  ] : [\n    {\n      title: \"Basic Information\",\n      subtitle: \"Tell us about yourself\",\n      fields: ['fullName', 'bio', 'location', 'phone']\n    },\n    {\n      title: \"Business Details\",\n      subtitle: \"Your company and projects\",\n      fields: ['companyName', 'industry', 'projectTypes']\n    },\n    {\n      title: \"Preferences & Privacy\",\n      subtitle: \"Customize your experience\",\n      fields: ['notifications', 'privacy']\n    }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleNestedChange = (parent, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [parent]: {\n        ...prev[parent],\n        [field]: value\n      }\n    }));\n  };\n\n  const handleArrayChange = (field, value, isAdd = true) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: isAdd \n        ? [...prev[field], value]\n        : prev[field].filter(item => item !== value)\n    }));\n  };\n\n  const validateStep = (stepIndex) => {\n    const stepFields = steps[stepIndex].fields;\n    const stepErrors = {};\n\n    stepFields.forEach(field => {\n      if (field === 'fullName' && !formData.fullName.trim()) {\n        stepErrors.fullName = 'Full name is required';\n      }\n      if (field === 'languages' && userType === 'translator' && formData.languages.length === 0) {\n        stepErrors.languages = 'Please select at least one language';\n      }\n      if (field === 'specializations' && userType === 'translator' && formData.specializations.length === 0) {\n        stepErrors.specializations = 'Please select at least one specialization';\n      }\n    });\n\n    setErrors(stepErrors);\n    return Object.keys(stepErrors).length === 0;\n  };\n\n  const nextStep = () => {\n    if (validateStep(currentStep)) {\n      if (currentStep < steps.length - 1) {\n        setCurrentStep(currentStep + 1);\n      } else {\n        handleSave();\n      }\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      await onSave(formData);\n      onComplete();\n    } catch (error) {\n      console.error('Error saving profile:', error);\n      setErrors({ general: 'Failed to save profile. Please try again.' });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleImageUpload = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        handleInputChange('profileImage', e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  if (!isVisible) return null;\n\n  const currentStepData = steps[currentStep];\n\n  return (\n    <div className=\"profile-customization-overlay\">\n      <div className=\"profile-customization-popup\">\n        <div className=\"customization-header\">\n          <div className=\"customization-progress\">\n            <div className=\"progress-bar\">\n              <div \n                className=\"progress-fill\" \n                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}\n              ></div>\n            </div>\n            <span className=\"progress-text\">\n              Step {currentStep + 1} of {steps.length}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"customization-content\">\n          <div className=\"step-header\">\n            <h2 className=\"step-title\">{currentStepData.title}</h2>\n            <p className=\"step-subtitle\">{currentStepData.subtitle}</p>\n          </div>\n\n          <div className=\"step-form\">\n            {currentStep === 0 && (\n              <div className=\"form-section\">\n                <div className=\"profile-image-upload\">\n                  <div className=\"image-preview\">\n                    {formData.profileImage ? (\n                      <img src={formData.profileImage} alt=\"Profile\" />\n                    ) : (\n                      <div className=\"image-placeholder\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                          <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n                          <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n                        </svg>\n                      </div>\n                    )}\n                  </div>\n                  <input\n                    type=\"file\"\n                    id=\"profileImage\"\n                    accept=\"image/*\"\n                    onChange={handleImageUpload}\n                    style={{ display: 'none' }}\n                  />\n                  <label htmlFor=\"profileImage\" className=\"upload-btn\">\n                    Upload Photo\n                  </label>\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Full Name *</label>\n                  <input\n                    type=\"text\"\n                    value={formData.fullName}\n                    onChange={(e) => handleInputChange('fullName', e.target.value)}\n                    placeholder=\"Enter your full name\"\n                    className={errors.fullName ? 'error' : ''}\n                  />\n                  {errors.fullName && <span className=\"error-text\">{errors.fullName}</span>}\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Bio</label>\n                  <textarea\n                    value={formData.bio}\n                    onChange={(e) => handleInputChange('bio', e.target.value)}\n                    placeholder=\"Tell us about yourself...\"\n                    rows=\"4\"\n                  />\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      value={formData.location}\n                      onChange={(e) => handleInputChange('location', e.target.value)}\n                      placeholder=\"City, Country\"\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Phone</label>\n                    <input\n                      type=\"tel\"\n                      value={formData.phone}\n                      onChange={(e) => handleInputChange('phone', e.target.value)}\n                      placeholder=\"+****************\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {currentStep === 1 && userType === 'translator' && (\n              <div className=\"form-section\">\n                <div className=\"form-group\">\n                  <label>Languages *</label>\n                  <div className=\"checkbox-grid\">\n                    {availableLanguages.map(lang => (\n                      <label key={lang} className=\"checkbox-item\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.languages.includes(lang)}\n                          onChange={(e) => handleArrayChange('languages', lang, e.target.checked)}\n                        />\n                        <span>{lang}</span>\n                      </label>\n                    ))}\n                  </div>\n                  {errors.languages && <span className=\"error-text\">{errors.languages}</span>}\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Specializations *</label>\n                  <div className=\"checkbox-grid\">\n                    {availableSpecializations.map(spec => (\n                      <label key={spec} className=\"checkbox-item\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.specializations.includes(spec)}\n                          onChange={(e) => handleArrayChange('specializations', spec, e.target.checked)}\n                        />\n                        <span>{spec}</span>\n                      </label>\n                    ))}\n                  </div>\n                  {errors.specializations && <span className=\"error-text\">{errors.specializations}</span>}\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label>Years of Experience</label>\n                    <select\n                      value={formData.yearsOfExperience}\n                      onChange={(e) => handleInputChange('yearsOfExperience', e.target.value)}\n                    >\n                      <option value=\"\">Select experience</option>\n                      <option value=\"0-1\">0-1 years</option>\n                      <option value=\"2-5\">2-5 years</option>\n                      <option value=\"6-10\">6-10 years</option>\n                      <option value=\"10+\">10+ years</option>\n                    </select>\n                  </div>\n                  <div className=\"form-group\">\n                    <label>Hourly Rate (USD)</label>\n                    <input\n                      type=\"number\"\n                      value={formData.hourlyRate}\n                      onChange={(e) => handleInputChange('hourlyRate', e.target.value)}\n                      placeholder=\"25\"\n                      min=\"0\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Certifications</label>\n                  <input\n                    type=\"text\"\n                    value={formData.certifications}\n                    onChange={(e) => handleInputChange('certifications', e.target.value)}\n                    placeholder=\"e.g., ATA Certified, CAT Tools Expert\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {currentStep === 1 && userType === 'client' && (\n              <div className=\"form-section\">\n                <div className=\"form-group\">\n                  <label>Company Name</label>\n                  <input\n                    type=\"text\"\n                    value={formData.companyName}\n                    onChange={(e) => handleInputChange('companyName', e.target.value)}\n                    placeholder=\"Your company name\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Industry</label>\n                  <select\n                    value={formData.industry}\n                    onChange={(e) => handleInputChange('industry', e.target.value)}\n                  >\n                    <option value=\"\">Select industry</option>\n                    {availableIndustries.map(industry => (\n                      <option key={industry} value={industry}>{industry}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Typical Project Types</label>\n                  <div className=\"checkbox-grid\">\n                    {['Documents', 'Websites', 'Marketing Materials', 'Legal Contracts', 'Technical Manuals', 'Academic Papers'].map(type => (\n                      <label key={type} className=\"checkbox-item\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.projectTypes.includes(type)}\n                          onChange={(e) => handleArrayChange('projectTypes', type, e.target.checked)}\n                        />\n                        <span>{type}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {currentStep === 2 && (\n              <div className=\"form-section\">\n                <div className=\"form-group\">\n                  <label>Notification Preferences</label>\n                  <div className=\"preference-options\">\n                    <label className=\"preference-item\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.notifications.email}\n                        onChange={(e) => handleNestedChange('notifications', 'email', e.target.checked)}\n                      />\n                      <span>Email notifications</span>\n                    </label>\n                    <label className=\"preference-item\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.notifications.browser}\n                        onChange={(e) => handleNestedChange('notifications', 'browser', e.target.checked)}\n                      />\n                      <span>Browser notifications</span>\n                    </label>\n                    <label className=\"preference-item\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.notifications.sms}\n                        onChange={(e) => handleNestedChange('notifications', 'sms', e.target.checked)}\n                      />\n                      <span>SMS notifications</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label>Privacy Settings</label>\n                  <div className=\"preference-options\">\n                    <label className=\"preference-item\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.privacy.showEmail}\n                        onChange={(e) => handleNestedChange('privacy', 'showEmail', e.target.checked)}\n                      />\n                      <span>Show email in profile</span>\n                    </label>\n                    <label className=\"preference-item\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.privacy.showPhone}\n                        onChange={(e) => handleNestedChange('privacy', 'showPhone', e.target.checked)}\n                      />\n                      <span>Show phone in profile</span>\n                    </label>\n                  </div>\n                  \n                  <div className=\"form-group\">\n                    <label>Profile Visibility</label>\n                    <select\n                      value={formData.privacy.profileVisibility}\n                      onChange={(e) => handleNestedChange('privacy', 'profileVisibility', e.target.value)}\n                    >\n                      <option value=\"public\">Public - Visible to everyone</option>\n                      <option value=\"registered\">Registered users only</option>\n                      <option value=\"private\">Private - Only you can see</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {errors.general && (\n              <div className=\"error-message\">\n                {errors.general}\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"customization-actions\">\n          <button \n            className=\"customization-btn secondary\" \n            onClick={prevStep}\n            disabled={currentStep === 0}\n          >\n            Previous\n          </button>\n          <button \n            className=\"customization-btn primary\" \n            onClick={nextStep}\n            disabled={isLoading}\n          >\n            {isLoading ? 'Saving...' : (currentStep === steps.length - 1 ? 'Complete Setup' : 'Next')}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfileCustomizationPopup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,yBAAyB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,UAAU;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,WAAW,GAAG,CAAC;AAAE,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACnG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC;IACvC;IACA0B,QAAQ,EAAEjB,WAAW,CAACiB,QAAQ,IAAI,EAAE;IACpCC,GAAG,EAAElB,WAAW,CAACkB,GAAG,IAAI,EAAE;IAC1BC,QAAQ,EAAEnB,WAAW,CAACmB,QAAQ,IAAI,EAAE;IACpCC,KAAK,EAAEpB,WAAW,CAACoB,KAAK,IAAI,EAAE;IAC9BC,YAAY,EAAErB,WAAW,CAACqB,YAAY,IAAI,IAAI;IAE9C;IACAC,SAAS,EAAEtB,WAAW,CAACsB,SAAS,IAAI,EAAE;IACtCC,eAAe,EAAEvB,WAAW,CAACuB,eAAe,IAAI,EAAE;IAClDC,iBAAiB,EAAExB,WAAW,CAACwB,iBAAiB,IAAI,EAAE;IACtDC,UAAU,EAAEzB,WAAW,CAACyB,UAAU,IAAI,EAAE;IACxCC,cAAc,EAAE1B,WAAW,CAAC0B,cAAc,IAAI,EAAE;IAEhD;IACAC,WAAW,EAAE3B,WAAW,CAAC2B,WAAW,IAAI,EAAE;IAC1CC,QAAQ,EAAE5B,WAAW,CAAC4B,QAAQ,IAAI,EAAE;IACpCC,YAAY,EAAE7B,WAAW,CAAC6B,YAAY,IAAI,EAAE;IAE5C;IACAC,aAAa,EAAE;MACbC,KAAK,GAAA7B,qBAAA,IAAAC,sBAAA,GAAEH,WAAW,CAAC8B,aAAa,cAAA3B,sBAAA,uBAAzBA,sBAAA,CAA2B4B,KAAK,cAAA7B,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MAC/C8B,OAAO,GAAA5B,sBAAA,IAAAC,sBAAA,GAAEL,WAAW,CAAC8B,aAAa,cAAAzB,sBAAA,uBAAzBA,sBAAA,CAA2B2B,OAAO,cAAA5B,sBAAA,cAAAA,sBAAA,GAAI,IAAI;MACnD6B,GAAG,GAAA3B,sBAAA,IAAAC,sBAAA,GAAEP,WAAW,CAAC8B,aAAa,cAAAvB,sBAAA,uBAAzBA,sBAAA,CAA2B0B,GAAG,cAAA3B,sBAAA,cAAAA,sBAAA,GAAI;IACzC,CAAC;IACD4B,OAAO,EAAE;MACPC,SAAS,GAAA3B,qBAAA,IAAAC,oBAAA,GAAET,WAAW,CAACkC,OAAO,cAAAzB,oBAAA,uBAAnBA,oBAAA,CAAqB0B,SAAS,cAAA3B,qBAAA,cAAAA,qBAAA,GAAI,KAAK;MAClD4B,SAAS,GAAA1B,sBAAA,IAAAC,qBAAA,GAAEX,WAAW,CAACkC,OAAO,cAAAvB,qBAAA,uBAAnBA,qBAAA,CAAqByB,SAAS,cAAA1B,sBAAA,cAAAA,sBAAA,GAAI,KAAK;MAClD2B,iBAAiB,EAAE,EAAAzB,qBAAA,GAAAZ,WAAW,CAACkC,OAAO,cAAAtB,qBAAA,uBAAnBA,qBAAA,CAAqByB,iBAAiB,KAAI;IAC/D;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC9C,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMmD,kBAAkB,GAAG,CACzB,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EACjE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAC9D;EAED,MAAMC,wBAAwB,GAAG,CAC/B,uBAAuB,EAAE,iBAAiB,EAAE,qBAAqB,EACjE,wBAAwB,EAAE,mBAAmB,EAAE,iBAAiB,EAChE,sBAAsB,EAAE,sBAAsB,EAAE,uBAAuB,CACxE;EAED,MAAMC,mBAAmB,GAAG,CAC1B,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAC3D,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,CAC/D;EAED,MAAMC,KAAK,GAAG9C,QAAQ,KAAK,YAAY,GAAG,CACxC;IACE+C,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,wBAAwB;IAClCC,MAAM,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO;EACjD,CAAC,EACD;IACEF,KAAK,EAAE,sBAAsB;IAC7BC,QAAQ,EAAE,+BAA+B;IACzCC,MAAM,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,YAAY,EAAE,gBAAgB;EAC9F,CAAC,EACD;IACEF,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,2BAA2B;IACrCC,MAAM,EAAE,CAAC,eAAe,EAAE,SAAS;EACrC,CAAC,CACF,GAAG,CACF;IACEF,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,wBAAwB;IAClCC,MAAM,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO;EACjD,CAAC,EACD;IACEF,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,2BAA2B;IACrCC,MAAM,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,cAAc;EACpD,CAAC,EACD;IACEF,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,2BAA2B;IACrCC,MAAM,EAAE,CAAC,eAAe,EAAE,SAAS;EACrC,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CnC,WAAW,CAACoC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIX,MAAM,CAACU,KAAK,CAAC,EAAE;MACjBT,SAAS,CAACW,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACF,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAACC,MAAM,EAAEJ,KAAK,EAAEC,KAAK,KAAK;IACnDnC,WAAW,CAACoC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACE,MAAM,GAAG;QACR,GAAGF,IAAI,CAACE,MAAM,CAAC;QACf,CAACJ,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAACL,KAAK,EAAEC,KAAK,EAAEK,KAAK,GAAG,IAAI,KAAK;IACxDxC,WAAW,CAACoC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGM,KAAK,GACV,CAAC,GAAGJ,IAAI,CAACF,KAAK,CAAC,EAAEC,KAAK,CAAC,GACvBC,IAAI,CAACF,KAAK,CAAC,CAACO,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKP,KAAK;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMQ,YAAY,GAAIC,SAAS,IAAK;IAClC,MAAMC,UAAU,GAAGhB,KAAK,CAACe,SAAS,CAAC,CAACZ,MAAM;IAC1C,MAAMc,UAAU,GAAG,CAAC,CAAC;IAErBD,UAAU,CAACE,OAAO,CAACb,KAAK,IAAI;MAC1B,IAAIA,KAAK,KAAK,UAAU,IAAI,CAACnC,QAAQ,CAACE,QAAQ,CAAC+C,IAAI,CAAC,CAAC,EAAE;QACrDF,UAAU,CAAC7C,QAAQ,GAAG,uBAAuB;MAC/C;MACA,IAAIiC,KAAK,KAAK,WAAW,IAAInD,QAAQ,KAAK,YAAY,IAAIgB,QAAQ,CAACO,SAAS,CAAC2C,MAAM,KAAK,CAAC,EAAE;QACzFH,UAAU,CAACxC,SAAS,GAAG,qCAAqC;MAC9D;MACA,IAAI4B,KAAK,KAAK,iBAAiB,IAAInD,QAAQ,KAAK,YAAY,IAAIgB,QAAQ,CAACQ,eAAe,CAAC0C,MAAM,KAAK,CAAC,EAAE;QACrGH,UAAU,CAACvC,eAAe,GAAG,2CAA2C;MAC1E;IACF,CAAC,CAAC;IAEFkB,SAAS,CAACqB,UAAU,CAAC;IACrB,OAAOI,MAAM,CAACC,IAAI,CAACL,UAAU,CAAC,CAACG,MAAM,KAAK,CAAC;EAC7C,CAAC;EAED,MAAMG,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIT,YAAY,CAAC9C,WAAW,CAAC,EAAE;MAC7B,IAAIA,WAAW,GAAGgC,KAAK,CAACoB,MAAM,GAAG,CAAC,EAAE;QAClCnD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACLwD,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIzD,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMwD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B9B,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,MAAMzC,MAAM,CAACiB,QAAQ,CAAC;MACtBlB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAO0E,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C9B,SAAS,CAAC;QAAEgC,OAAO,EAAE;MAA4C,CAAC,CAAC;IACrE,CAAC,SAAS;MACRlC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMmC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QACrBjC,iBAAiB,CAAC,cAAc,EAAEiC,CAAC,CAACL,MAAM,CAACM,MAAM,CAAC;MACpD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACR,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,IAAI,CAAChF,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMyF,eAAe,GAAGxC,KAAK,CAAChC,WAAW,CAAC;EAE1C,oBACEnB,OAAA;IAAK4F,SAAS,EAAC,+BAA+B;IAAAC,QAAA,eAC5C7F,OAAA;MAAK4F,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1C7F,OAAA;QAAK4F,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnC7F,OAAA;UAAK4F,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7F,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B7F,OAAA;cACE4F,SAAS,EAAC,eAAe;cACzBE,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAI,CAAC5E,WAAW,GAAG,CAAC,IAAIgC,KAAK,CAACoB,MAAM,GAAI,GAAG;cAAI;YAAE;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnG,OAAA;YAAM4F,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,OACzB,EAAC1E,WAAW,GAAG,CAAC,EAAC,MAAI,EAACgC,KAAK,CAACoB,MAAM;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnG,OAAA;QAAK4F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC7F,OAAA;UAAK4F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7F,OAAA;YAAI4F,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEF,eAAe,CAACvC;UAAK;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvDnG,OAAA;YAAG4F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEF,eAAe,CAACtC;UAAQ;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAENnG,OAAA;UAAK4F,SAAS,EAAC,WAAW;UAAAC,QAAA,GACvB1E,WAAW,KAAK,CAAC,iBAChBnB,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAK4F,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC7F,OAAA;gBAAK4F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BxE,QAAQ,CAACM,YAAY,gBACpB3B,OAAA;kBAAKoG,GAAG,EAAE/E,QAAQ,CAACM,YAAa;kBAAC0E,GAAG,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEjDnG,OAAA;kBAAK4F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChC7F,OAAA;oBAAKsG,KAAK,EAAC,4BAA4B;oBAACP,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAAAhB,QAAA,gBAC/K7F,OAAA;sBAAM8G,CAAC,EAAC;oBAA2C;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC3DnG,OAAA;sBAAQ+G,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACC,CAAC,EAAC;oBAAG;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNnG,OAAA;gBACEkH,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,cAAc;gBACjBC,MAAM,EAAC,SAAS;gBAChBC,QAAQ,EAAErC,iBAAkB;gBAC5Bc,KAAK,EAAE;kBAAEwB,OAAO,EAAE;gBAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFnG,OAAA;gBAAOuH,OAAO,EAAC,cAAc;gBAAC3B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAErD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BnG,OAAA;gBACEkH,IAAI,EAAC,MAAM;gBACXzD,KAAK,EAAEpC,QAAQ,CAACE,QAAS;gBACzB8F,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,UAAU,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;gBAC/D+D,WAAW,EAAC,sBAAsB;gBAClC5B,SAAS,EAAE9C,MAAM,CAACvB,QAAQ,GAAG,OAAO,GAAG;cAAG;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACDrD,MAAM,CAACvB,QAAQ,iBAAIvB,OAAA;gBAAM4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/C,MAAM,CAACvB;cAAQ;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBnG,OAAA;gBACEyD,KAAK,EAAEpC,QAAQ,CAACG,GAAI;gBACpB6F,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,KAAK,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;gBAC1D+D,WAAW,EAAC,2BAA2B;gBACvCC,IAAI,EAAC;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB7F,OAAA;gBAAK4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7F,OAAA;kBAAA6F,QAAA,EAAO;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvBnG,OAAA;kBACEkH,IAAI,EAAC,MAAM;kBACXzD,KAAK,EAAEpC,QAAQ,CAACI,QAAS;kBACzB4F,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,UAAU,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;kBAC/D+D,WAAW,EAAC;gBAAe;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnG,OAAA;gBAAK4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7F,OAAA;kBAAA6F,QAAA,EAAO;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpBnG,OAAA;kBACEkH,IAAI,EAAC,KAAK;kBACVzD,KAAK,EAAEpC,QAAQ,CAACK,KAAM;kBACtB2F,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,OAAO,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;kBAC5D+D,WAAW,EAAC;gBAAmB;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAhF,WAAW,KAAK,CAAC,IAAId,QAAQ,KAAK,YAAY,iBAC7CL,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BnG,OAAA;gBAAK4F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3B7C,kBAAkB,CAAC0E,GAAG,CAACC,IAAI,iBAC1B3H,OAAA;kBAAkB4F,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACzC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACO,SAAS,CAACiG,QAAQ,CAACF,IAAI,CAAE;oBAC3CN,QAAQ,EAAG7B,CAAC,IAAK3B,iBAAiB,CAAC,WAAW,EAAE8D,IAAI,EAAEnC,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAO8B;kBAAI;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GANTwB,IAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOT,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACLrD,MAAM,CAAClB,SAAS,iBAAI5B,OAAA;gBAAM4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/C,MAAM,CAAClB;cAAS;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCnG,OAAA;gBAAK4F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3B5C,wBAAwB,CAACyE,GAAG,CAACI,IAAI,iBAChC9H,OAAA;kBAAkB4F,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACzC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACQ,eAAe,CAACgG,QAAQ,CAACC,IAAI,CAAE;oBACjDT,QAAQ,EAAG7B,CAAC,IAAK3B,iBAAiB,CAAC,iBAAiB,EAAEiE,IAAI,EAAEtC,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAOiC;kBAAI;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GANT2B,IAAI;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOT,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACLrD,MAAM,CAACjB,eAAe,iBAAI7B,OAAA;gBAAM4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/C,MAAM,CAACjB;cAAe;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB7F,OAAA;gBAAK4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7F,OAAA;kBAAA6F,QAAA,EAAO;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCnG,OAAA;kBACEyD,KAAK,EAAEpC,QAAQ,CAACS,iBAAkB;kBAClCuF,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,mBAAmB,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;kBAAAoC,QAAA,gBAExE7F,OAAA;oBAAQyD,KAAK,EAAC,EAAE;oBAAAoC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3CnG,OAAA;oBAAQyD,KAAK,EAAC,KAAK;oBAAAoC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCnG,OAAA;oBAAQyD,KAAK,EAAC,KAAK;oBAAAoC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCnG,OAAA;oBAAQyD,KAAK,EAAC,MAAM;oBAAAoC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCnG,OAAA;oBAAQyD,KAAK,EAAC,KAAK;oBAAAoC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNnG,OAAA;gBAAK4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7F,OAAA;kBAAA6F,QAAA,EAAO;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChCnG,OAAA;kBACEkH,IAAI,EAAC,QAAQ;kBACbzD,KAAK,EAAEpC,QAAQ,CAACU,UAAW;kBAC3BsF,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,YAAY,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;kBACjE+D,WAAW,EAAC,IAAI;kBAChBO,GAAG,EAAC;gBAAG;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7BnG,OAAA;gBACEkH,IAAI,EAAC,MAAM;gBACXzD,KAAK,EAAEpC,QAAQ,CAACW,cAAe;gBAC/BqF,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,gBAAgB,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;gBACrE+D,WAAW,EAAC;cAAuC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAhF,WAAW,KAAK,CAAC,IAAId,QAAQ,KAAK,QAAQ,iBACzCL,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BnG,OAAA;gBACEkH,IAAI,EAAC,MAAM;gBACXzD,KAAK,EAAEpC,QAAQ,CAACY,WAAY;gBAC5BoF,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,aAAa,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;gBAClE+D,WAAW,EAAC;cAAmB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBnG,OAAA;gBACEyD,KAAK,EAAEpC,QAAQ,CAACa,QAAS;gBACzBmF,QAAQ,EAAG7B,CAAC,IAAKjC,iBAAiB,CAAC,UAAU,EAAEiC,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;gBAAAoC,QAAA,gBAE/D7F,OAAA;kBAAQyD,KAAK,EAAC,EAAE;kBAAAoC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCjD,mBAAmB,CAACwE,GAAG,CAACxF,QAAQ,iBAC/BlC,OAAA;kBAAuByD,KAAK,EAAEvB,QAAS;kBAAA2D,QAAA,EAAE3D;gBAAQ,GAApCA,QAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCnG,OAAA;gBAAK4F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3B,CAAC,WAAW,EAAE,UAAU,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC6B,GAAG,CAACR,IAAI,iBACnHlH,OAAA;kBAAkB4F,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACzC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACc,YAAY,CAAC0F,QAAQ,CAACX,IAAI,CAAE;oBAC9CG,QAAQ,EAAG7B,CAAC,IAAK3B,iBAAiB,CAAC,cAAc,EAAEqD,IAAI,EAAE1B,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAOqB;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GANTe,IAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOT,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAhF,WAAW,KAAK,CAAC,iBAChBnB,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvCnG,OAAA;gBAAK4F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC7F,OAAA;kBAAO4F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAChC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACe,aAAa,CAACC,KAAM;oBACtCgF,QAAQ,EAAG7B,CAAC,IAAK7B,kBAAkB,CAAC,eAAe,EAAE,OAAO,EAAE6B,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAM;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACRnG,OAAA;kBAAO4F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAChC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACe,aAAa,CAACE,OAAQ;oBACxC+E,QAAQ,EAAG7B,CAAC,IAAK7B,kBAAkB,CAAC,eAAe,EAAE,SAAS,EAAE6B,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAM;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACRnG,OAAA;kBAAO4F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAChC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACe,aAAa,CAACG,GAAI;oBACpC8E,QAAQ,EAAG7B,CAAC,IAAK7B,kBAAkB,CAAC,eAAe,EAAE,KAAK,EAAE6B,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAM;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnG,OAAA;cAAK4F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7F,OAAA;gBAAA6F,QAAA,EAAO;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BnG,OAAA;gBAAK4F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC7F,OAAA;kBAAO4F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAChC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACmB,OAAO,CAACC,SAAU;oBACpC4E,QAAQ,EAAG7B,CAAC,IAAK7B,kBAAkB,CAAC,SAAS,EAAE,WAAW,EAAE6B,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAM;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACRnG,OAAA;kBAAO4F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAChC7F,OAAA;oBACEkH,IAAI,EAAC,UAAU;oBACfU,OAAO,EAAEvG,QAAQ,CAACmB,OAAO,CAACE,SAAU;oBACpC2E,QAAQ,EAAG7B,CAAC,IAAK7B,kBAAkB,CAAC,SAAS,EAAE,WAAW,EAAE6B,CAAC,CAACL,MAAM,CAACyC,OAAO;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eACFnG,OAAA;oBAAA6F,QAAA,EAAM;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENnG,OAAA;gBAAK4F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7F,OAAA;kBAAA6F,QAAA,EAAO;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCnG,OAAA;kBACEyD,KAAK,EAAEpC,QAAQ,CAACmB,OAAO,CAACG,iBAAkB;kBAC1C0E,QAAQ,EAAG7B,CAAC,IAAK7B,kBAAkB,CAAC,SAAS,EAAE,mBAAmB,EAAE6B,CAAC,CAACL,MAAM,CAAC1B,KAAK,CAAE;kBAAAoC,QAAA,gBAEpF7F,OAAA;oBAAQyD,KAAK,EAAC,QAAQ;oBAAAoC,QAAA,EAAC;kBAA4B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5DnG,OAAA;oBAAQyD,KAAK,EAAC,YAAY;oBAAAoC,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzDnG,OAAA;oBAAQyD,KAAK,EAAC,SAAS;oBAAAoC,QAAA,EAAC;kBAA0B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEArD,MAAM,CAACiC,OAAO,iBACb/E,OAAA;YAAK4F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B/C,MAAM,CAACiC;UAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnG,OAAA;QAAK4F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC7F,OAAA;UACE4F,SAAS,EAAC,6BAA6B;UACvCoC,OAAO,EAAEpD,QAAS;UAClBqD,QAAQ,EAAE9G,WAAW,KAAK,CAAE;UAAA0E,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnG,OAAA;UACE4F,SAAS,EAAC,2BAA2B;UACrCoC,OAAO,EAAEtD,QAAS;UAClBuD,QAAQ,EAAErF,SAAU;UAAAiD,QAAA,EAEnBjD,SAAS,GAAG,WAAW,GAAIzB,WAAW,KAAKgC,KAAK,CAACoB,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG;QAAO;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5F,EAAA,CA9eIN,yBAAyB;AAAAiI,EAAA,GAAzBjI,yBAAyB;AAgf/B,eAAeA,yBAAyB;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}