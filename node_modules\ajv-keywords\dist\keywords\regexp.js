"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const regexp_1 = __importDefault(require("../definitions/regexp"));
const regexp = (ajv) => ajv.addKeyword((0, regexp_1.default)());
exports.default = regexp;
module.exports = regexp;
//# sourceMappingURL=regexp.js.map