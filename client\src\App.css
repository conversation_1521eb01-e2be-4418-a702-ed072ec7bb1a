:root {
  /* Light theme variables */
  --primary: #20B2AA;
  --primary-dark: #188F89;
  --primary-light: #48D1CB;
  --secondary: #006D6A;
  --text-primary: #000000;
  --text-secondary: #333333;
  --text-muted: #666666;
  --background: #FFFFFF;
  --bg-secondary: #F7FAFC;
  --white: #FFFFFF;
  --card-bg: #FFFFFF;
  --card-border: rgba(32, 178, 170, 0.2);
  --gradient-start: #FFFFFF;
  --gradient-end: #E0F7FA;
  --nav-bg: rgba(255, 255, 255, 0.9);
  --border-color: #E2E8F0;
  --hover-bg: #EDF2F7;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --success-color: #48BB78;
  --warning-color: #ED8936;
  --error-color: #F56565;
  --info-color: #4299E1;
  --accent-color: #20B2AA;
  --accent-color-light: rgba(32, 178, 170, 0.1);
  --accent-color-dark: #188F89;
  --transition-speed: 0.3s;
}

[data-theme="dark"] {
  --primary: #40E0D0;
  --primary-dark: #2cc4b4;
  --primary-light: #5ee7d9;
  --secondary: #1A2435;
  --text-primary: #000000;
  --text-secondary: #333333;
  --text-muted: #666666;
  --background: #171923;
  --bg-secondary: #1A202C;
  --white: #000000;
  --card-bg: #2D3748;
  --card-border: rgba(64, 224, 208, 0.2);
  --gradient-start: #171923;
  --gradient-end: #2D3748;
  --nav-bg: rgba(26, 32, 44, 0.9);
  --border-color: #4A5568;
  --hover-bg: #2D3748;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --success-color: #68D391;
  --warning-color: #F6AD55;
  --error-color: #FC8181;
  --info-color: #63B3ED;
  --accent-color: #40E0D0;
  --accent-color-light: rgba(64, 224, 208, 0.15);
  --accent-color-dark: #2cc4b4;
  --transition-speed: 0.3s;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
  color: var(--text-primary);
  background-color: var(--background);
}

.App {
  text-align: left;
  background-color: var(--background);
  position: relative;
  z-index: 1;
  min-height: 100vh;
  color: var(--text-primary);
}

/* Apply transitions to all elements for smooth theme switching */
* {
  transition: background-color var(--transition-speed) ease,
              color var(--transition-speed) ease,
              border-color var(--transition-speed) ease,
              box-shadow var(--transition-speed) ease;
}

/* Global smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Smooth transitions for all interactive elements */
a, button, input, select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Navbar styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(32, 178, 170, 0.15);
  box-shadow: 0 4px 30px rgba(32, 178, 170, 0.1);
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  border-radius: 32px;
  width: calc(100% - 4rem);
  max-width: 1200px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateX(-50%) translateY(-2px);
}

.brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 0;
  background: transparent;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text);
  letter-spacing: 0.5px;
}

.nav-center {
  display: flex;
  gap: 2rem;
}

.nav-link {
  position: relative;
  color: var(--text);
  font-weight: 600;
  padding: 0.5rem 1.25rem;
  border-radius: 24px;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
 text-decoration: none;
}

.nav-link:hover {
  color: var(--primary-dark);
  background: rgba(32, 178, 170, 0.08);
  transform: translateY(-2px);
}

.nav-link.active {
  color: var(--primary-dark);
  background: rgba(32, 178, 170, 0.12);
}

.nav-link.highlight {
  color: var(--white);
  background: var(--primary);
  border: none;
  box-shadow: 0 4px 15px rgba(32, 178, 170, 0.2);
}

.nav-link.highlight:hover {
  background: var(--primary-dark);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(32, 178, 170, 0.3);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Language selector styles */
.language-selector-container {
  position: relative;
}

.language-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--card-border);
  border-radius: 20px;
  color: var(--text);
  font-size: 0.9375rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.language-selector:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
}

.language-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  padding: 0.5rem;
  min-width: 160px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: scaleIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top right;
  z-index: 100;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--text);
  font-size: 0.9375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  text-align: left;
}

.language-option:hover {
  background: rgba(32, 178, 170, 0.1);
}

.language-option.active {
  background: var(--primary);
  color: black;
}

/* RTL Support */
[dir="rtl"] .navbar {
  direction: rtl;
}

[dir="rtl"] .nav-right {
  flex-direction: row-reverse;
}

[dir="rtl"] .language-menu {
  left: 0;
  right: auto;
}

[dir="rtl"] .language-option {
  text-align: right;
}

.theme-toggle {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid var(--card-border);
  color: var(--text);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transform: translateY(0);
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) rotate(45deg);
  box-shadow: 0 4px 15px rgba(32, 178, 170, 0.15);
}

.theme-toggle.dark {
  background: var(--primary);
  color: var(--white);
}

.login-button {
  color: var(--white);
  background: var(--primary);
  border: none;
  padding: 0.6rem 1.5rem;
  border-radius: 24px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(32, 178, 170, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
}

.login-button:hover {
  background: var(--primary-dark);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(32, 178, 170, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

/* Hero section */
.hero-section {
  display: flex;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem;
  gap: 4rem;
  min-height: 500px;
}

.hero-left {
  flex: 1;
  padding-top: 2rem;
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(64, 224, 208, 0.1);
  border-radius: 20px;
  color: var(--primary);
  font-weight: 500;
  margin-bottom: 2rem;
}

.hero-title {
  font-size: 3.5rem;
  line-height: 1.2;
  color: var(--text);
  margin-bottom: 1.5rem;
  text-shadow: none;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  max-width: 600px;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.get-started-btn,
.view-pricing-btn {
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.get-started-btn {
  background: #40E0D0;
  color: black;
  border: none;
  backdrop-filter: blur(5px);
  text-decoration: none;
  display: inline-block;
}

.get-started-btn:hover {
  background: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
}

.view-pricing-btn {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.view-pricing-btn:hover {
  background: rgba(64, 224, 208, 0.1);
  transform: translateY(-2px);
}

/* Features section */
.features-section {
  padding: 6rem 4rem;
  background: transparent;
  position: relative;
}

.features-section h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #40E0D0;
  margin-bottom: 3rem;
  text-shadow: none;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border-radius: 16px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(32, 178, 170, 0.15);
  box-shadow: 0 8px 32px rgba(32, 178, 170, 0.1);
}

.feature-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(32, 178, 170, 0.15);
  border-color: var(--primary);
}

.feature-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg);
  border-radius: 16px;
  color: var(--primary);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .feature-icon {
  background: rgba(64, 224, 208, 0.1);
  color: var(--primary);
}

.feature-card h3 {
  color: #40E0D0;
  margin-bottom: 1rem;
}

.feature-card p {
  color: var(--text-light);
}

/* How it works section */
.how-it-works {
  padding: 6rem 4rem;
  background: transparent;
  position: relative;
}

.how-it-works h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #40E0D0;
  margin-bottom: 3rem;
  text-shadow: none;
}

/* We're using features-section and feature-card styles for the How It Works section */

/* Testimonials section */
.testimonials {
  padding: 6rem 4rem;
  background: transparent;
  position: relative;
}

.testimonials h2 {
  text-align: center;
  font-size: 2.5rem;
  color: var(--text);
  margin-bottom: 3rem;
  text-shadow: none;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.testimonial-card {
  background: var(--card-bg);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(32, 178, 170, 0.1);
  border: 1px solid var(--card-border);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-content {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 1.5rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-info strong {
  display: block;
  color: black;
}

.author-info span {
  color: rgba(0, 0, 0, 0.8);
  font-size: 0.875rem;
}

/* Footer */
.footer {
  background: rgba(42, 57, 80, 0.8);
  color: black;
  padding: 4rem 4rem 2rem;
  backdrop-filter: blur(10px);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section h4 {
  color: black;
  margin-bottom: 1.5rem;
}

.footer-section a {
  color: rgba(0, 0, 0, 0.8);
  text-decoration: none;
  display: block;
  margin-bottom: 0.75rem;
  transition: color 0.2s;
}

.footer-section a:hover {
  color: black;
}

.footer-bottom {
  text-align: center;
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

/* Platform preview */
.platform-preview {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.platform-preview:hover {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

.responsive-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  background-color: white;
  padding: 15px;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.responsive-image:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(48, 213, 200, 0.1);
}

.preview-header {
  background: var(--background);
  padding: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.window-controls {
  display: flex;
  gap: 0.5rem;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ff5f57; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #28c840; }

.preview-content {
  padding: 1rem;
}

.preview-content img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 1.5rem;
    width: calc(100% - 2rem);
    top: 0.5rem;
  }

  .nav-center {
    display: none;
  }

  .hero-section {
    flex-direction: column;
    padding: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .features-section,
  .testimonials {
    padding: 4rem 2rem;
  }

  .footer {
    padding: 4rem 2rem 2rem;
  }
}

/* Auth container frame styles */
.auth-container-frame {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  background: var(--card-bg);
  border: 1px solid rgba(32, 178, 170, 0.3);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 1rem;
  position: relative;
}

.auth-frame-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 1rem;
}

.auth-frame-header h2 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin: 0;
}

.close-auth-btn {
  background: rgba(32, 178, 170, 0.2);
  border: none;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-auth-btn:hover {
  background: rgba(32, 178, 170, 0.3);
  transform: scale(1.1);
}

.interactive-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(224, 247, 250, 0.1) 100%);
  overflow: hidden;
  z-index: -1;
}
body.modal-open {
  overflow: hidden;
  padding-right: 15px; /* Prevent layout shift when scrollbar disappears */
  height: 100%;
  position: fixed;
  width: 100%;
}

.modal-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(32, 178, 170, 0.2);
  border: none;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10001;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.modal-close:hover {
  background: rgba(32, 178, 170, 0.2);
  transform: scale(1.1);
  color: var(--primary);
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Smooth theme transition */
* {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

/* Card hover effects */
.card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* Input field animations */
input:focus, select:focus {
  transform: scale(1.01);
}

/* Button press effect */
button:active {
  transform: scale(0.98);
}

/* Loading states */
.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}



/* Spline Background */
.spline-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
  pointer-events: none;
}

.spline-background iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Fallback background */
.fallback-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(
    45deg,
    #1a3f3d 0%,
    #2a5754 50%,
    #40E0D0 100%
  );
  animation: gradientAnimation 15s ease infinite;
  background-size: 400% 400%;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.main-content {
  position: relative;
  z-index: 1;
}

/* Interactive Background */
.interactive-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
  overflow: hidden;
  z-index: -1;
}

.gradient-orb {
  position: absolute;
  width: 100vh;
  height: 100vh;
  background: radial-gradient(circle, rgba(64, 224, 208, 0.4) 0%, transparent 70%);
  transform: translate(calc(var(--mouse-x) - 50%), calc(var(--mouse-y) - 50%));
  transition: transform 0.2s ease-out;
  pointer-events: none;
}

.content-wrapper {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  backdrop-filter: blur(10px);
  padding-top: 5rem;
}

.theme-toggle:active {
  transform: translateY(0);
}

/* Add click animation for navbar elements */
@keyframes clickEffect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.nav-link:active,
.login-button:active,
.language-selector:active,
.theme-toggle:active {
  animation: clickEffect 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Update text colors for better contrast */
.hero-title,
.features-section h2,
.testimonials h2 {
  color: #40E0D0;
  text-shadow: none;
}

.hero-description,
.feature-card p,
.testimonial-content {
  color: var(--text-light);
}

/* Content animations */
.content-section {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: var(--background);
}

.loading-screen::after {
  content: '';
  width: 50px;
  height: 50px;
  border: 4px solid rgba(32, 178, 170, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}
