/* Dashboard Animations CSS */

/* Base animation properties */
:root {
  --animation-speed-slow: 3s;
  --animation-speed-medium: 1.5s;
  --animation-speed-fast: 0.5s;
  --animation-delay-step: 0.1s;
  --transition-bounce: cubic-bezier(0.68, -0.55, 0.27, 1.55);
  --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Entrance animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Floating animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes floatShadow {
  0% {
    box-shadow: 0 5px 15px 0px rgba(0, 0, 0, 0.05);
  }
  50% {
    box-shadow: 0 25px 15px 0px rgba(0, 0, 0, 0.02);
  }
  100% {
    box-shadow: 0 5px 15px 0px rgba(0, 0, 0, 0.05);
  }
}

/* Pulse animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(32, 178, 170, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(32, 178, 170, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(32, 178, 170, 0);
  }
}

/* Rotate animation */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Shimmer loading effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Bubble animation */
@keyframes bubble {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100vh) scale(1.5);
    opacity: 0;
  }
}

/* Apply animations to dashboard elements */
.dashboard-container {
  overflow-x: hidden;
  max-width: 100%;
}

.dashboard-section {
  max-width: 100%;
  overflow-x: hidden;
}

/* Welcome banner animation */
.welcome-banner {
  animation: scaleIn var(--animation-speed-fast) var(--transition-smooth) forwards;
}

.welcome-banner .decoration::before {
  animation: rotate 15s infinite linear;
}

/* Stats cards animations */
.stats-card {
  animation: fadeInUp var(--animation-speed-fast) var(--transition-smooth) forwards;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay-step) * var(--i, 1));
}

.stats-card:hover {
  animation: pulse 2s infinite;
}

/* Quick widgets animations */
.quick-widget {
  animation: fadeInUp var(--animation-speed-fast) var(--transition-smooth) forwards;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay-step) * var(--i, 1));
  transition: transform 0.3s var(--transition-bounce), box-shadow 0.3s ease;
}

.quick-widget:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Widget icon animation */
.widget-icon, .stats-card-icon, .metric-icon {
  transition: transform 0.3s var(--transition-bounce);
}

.quick-widget:hover .widget-icon,
.stats-card:hover .stats-card-icon,
.summary-metric:hover .metric-icon {
  transform: scale(1.15) rotate(10deg);
}

/* Progress bar animation */
.progress-bar {
  transition: width 1s var(--transition-smooth);
  animation: shimmer 2s infinite linear;
  background: linear-gradient(90deg,
    var(--accent-color) 0%,
    var(--accent-color-light) 50%,
    var(--accent-color) 100%
  );
  background-size: 200% 100%;
}

/* Floating bubbles for background */
.dashboard-bubbles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  pointer-events: none;
}

.bubble {
  position: absolute;
  bottom: -50px;
  background-color: var(--accent-color-light);
  border-radius: 50%;
  opacity: 0.3;
  animation: bubble var(--animation-speed-slow) ease-in infinite;
}

/* Loading animation */
.loading-spinner {
  animation: rotate 1s linear infinite;
}

/* Responsive animations */
@media (max-width: 768px) {
  .stats-card, .quick-widget {
    animation-name: fadeInUp;
  }

  .welcome-banner {
    animation-name: fadeInUp;
  }
}

/* Animation classes that can be added with JavaScript */
.animate-fadeInUp {
  animation: fadeInUp var(--animation-speed-fast) var(--transition-smooth) forwards;
}

.animate-fadeInRight {
  animation: fadeInRight var(--animation-speed-fast) var(--transition-smooth) forwards;
}

.animate-fadeInLeft {
  animation: fadeInLeft var(--animation-speed-fast) var(--transition-smooth) forwards;
}

.animate-scaleIn {
  animation: scaleIn var(--animation-speed-fast) var(--transition-smooth) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg,
    var(--bg-secondary) 0%,
    var(--card-bg) 50%,
    var(--bg-secondary) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite linear;
}
