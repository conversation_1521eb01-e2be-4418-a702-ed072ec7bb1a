.auth-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
}

.auth-container h2 {
  font-size: 2rem;
  color: var(--text);
  margin-bottom: 0.5rem;
  text-align: center;
}

.auth-subtitle {
  color: var(--text-light);
  text-align: center;
  margin-bottom: 2rem;
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid var(--card-border);
}

.divider span {
  padding: 0 1rem;
  color: var(--text-light);
  font-size: 0.875rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text);
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--card-border);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.5);
  color: var(--text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 4px rgba(32, 178, 170, 0.1);
}

.forgot-password {
  text-align: right;
  margin-bottom: 1.5rem;
}

.forgot-password a {
  color: var(--primary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.forgot-password a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.submit-button {
  width: 100%;
  padding: 0.875rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.2);
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  color: var(--text-light);
}

.auth-switch button {
  background: none;
  border: none;
  color: var(--primary);
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  margin-left: 0.25rem;
  transition: color 0.3s ease;
}

.auth-switch button:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.auth-box {
  background: linear-gradient(125deg, #1a3f3d 0%, #2a5754 100%);
  border-radius: 24px;
  width: 100%;
  padding: 2.5rem;
  position: relative;
  z-index: 1;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.auth-box::before {
  content: '';
  position: absolute;
  width: 150%;
  height: 150%;
  background: radial-gradient(circle, rgba(64, 224, 208, 0.1) 0%, transparent 70%);
  top: -25%;
  left: -25%;
  animation: rotate 20s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.auth-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.auth-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

.auth-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

.auth-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 1rem;
}

.auth-tab {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.auth-tab:hover {
  color: rgba(255, 255, 255, 0.9);
}

.auth-tab.active {
  color: #fff;
}

.auth-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #40E0D0, #48D1C5);
  box-shadow: 0 0 20px rgba(64, 224, 208, 0.5);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.social-auth {
  display: flex;
  justify-content: center;
}

.social-btn {
  width: 100%;
  max-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem;
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-btn:hover {
  background: rgba(64, 224, 208, 0.1);
  transform: translateY(-2px);
  border-color: rgba(64, 224, 208, 0.5);
}

.social-btn img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.auth-modal {
  width: 100%;
  max-width: 440px;
  background: linear-gradient(145deg, #ffffff, #f5f7fa);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 16px 32px rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  display: block;
}

/* Dark mode styles for auth modal */
[data-theme="dark"] .auth-modal {
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.2),
    0 16px 32px rgba(0, 0, 0, 0.3);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(0) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* New modern styles */
.auth-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.auth-logo .logo {
  height: 48px;
  width: auto;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #20B2AA, #00CED1);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.auth-subtitle {
  color: #6b7280;
  font-size: 0.95rem;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  width: 20px;
  height: 20px;
  opacity: 0.5;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.user-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' /%3E%3C/svg%3E");
}

.email-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' /%3E%3C/svg%3E");
}

.password-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z' /%3E%3C/svg%3E");
}

.role-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' /%3E%3C/svg%3E");
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  color: #4b5563;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background-color: #f9fafb;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  color: #1f2937;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #20B2AA;
  box-shadow: 0 0 0 3px rgba(32, 178, 170, 0.15);
  background-color: #fff;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #20B2AA;
}

.forgot-link {
  color: #20B2AA;
  font-weight: 500;
  transition: color 0.2s;
}

.forgot-link:hover {
  color: #008B8B;
  text-decoration: underline;
}

.auth-submit {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #20B2AA, #00CED1);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.auth-submit:hover {
  background: linear-gradient(135deg, #199692, #00B5B8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.25);
}

.auth-submit:active {
  transform: translateY(0);
}

.submit-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14 5l7 7m0 0l-7 7m7-7H3' /%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
}

.auth-submit:hover .submit-icon {
  transform: translateX(3px);
}

.auth-switch {
  margin-top: 1.5rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.95rem;
}

.switch-mode-btn {
  background: none;
  border: none;
  color: #20B2AA;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  margin-left: 0.25rem;
  transition: color 0.2s;
}

.switch-mode-btn:hover {
  color: #008B8B;
  text-decoration: underline;
}

.auth-error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  text-align: center;
}

.auth-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

.input-error {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
}

.field-error {
  color: #b91c1c;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.checking-indicator {
  position: absolute;
  right: 10px;
  font-size: 0.8rem;
  color: #6b7280;
  font-style: italic;
}

.password-strength {
  font-size: 0.8rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.password-strength.weak {
  color: #f59e0b;
}

.password-strength.strong {
  color: #10b981;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}