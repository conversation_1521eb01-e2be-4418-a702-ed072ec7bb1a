'use strict';

module.exports = {
  'instanceof': require('./instanceof'),
  range: require('./range'),
  regexp: require('./regexp'),
  'typeof': require('./typeof'),
  dynamicDefaults: require('./dynamicDefaults'),
  allRequired: require('./allRequired'),
  anyRequired: require('./anyRequired'),
  oneRequired: require('./oneRequired'),
  prohibited: require('./prohibited'),
  uniqueItemProperties: require('./uniqueItemProperties'),
  deepProperties: require('./deepProperties'),
  deepRequired: require('./deepRequired'),
  formatMinimum: require('./formatMinimum'),
  formatMaximum: require('./formatMaximum'),
  patternRequired: require('./patternRequired'),
  'switch': require('./switch'),
  select: require('./select'),
  transform: require('./transform')
};
