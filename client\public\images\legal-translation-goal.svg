<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Base styles */
    .document { transition: transform 0.3s ease, filter 0.3s ease; }
    .center-icon { transition: transform 0.5s ease, filter 0.5s ease; }
    .connection-line { transition: stroke-dashoffset 2s linear infinite, stroke 0.3s ease; }
    .goal-element { transition: transform 0.5s ease, opacity 0.5s ease; }
    
    /* Interactive animations */
    .document:hover { transform: translateY(-5px); filter: drop-shadow(0 10px 10px rgba(0,0,0,0.15)); }
    .center-icon:hover { transform: scale(1.05); filter: drop-shadow(0 5px 10px rgba(0,0,0,0.2)); }
    .goal-element:hover { transform: scale(1.1); }
    
    /* Continuous animations */
    @keyframes dash {
      to { stroke-dashoffset: -20; }
    }
    .connection-line {
      stroke-dasharray: 5 3;
      animation: dash 2s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }
    .pulse-animation {
      animation: pulse 4s ease-in-out infinite;
    }
    
    @keyframes checkmark {
      0% { stroke-dashoffset: 100; }
      100% { stroke-dashoffset: 0; }
    }
    .checkmark-animation {
      stroke-dasharray: 100;
      stroke-dashoffset: 100;
      animation: checkmark 1.5s ease-in-out forwards;
    }
  </style>

  <!-- Definitions for effects -->
  <defs>
    <linearGradient id="documentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff"/>
      <stop offset="100%" stop-color="#f0f0f0"/>
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#40E0D0"/>
      <stop offset="100%" stop-color="#20B2AA"/>
    </linearGradient>
    
    <linearGradient id="goalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4CAF50"/>
      <stop offset="100%" stop-color="#388E3C"/>
    </linearGradient>
    
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000" flood-opacity="0.15"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="#f8f9fa"/>
  
  <!-- Progress path to goal -->
  <path d="M100,200 C150,100 250,300 300,200 C350,100 450,300 500,200" stroke="#e9ecef" stroke-width="8" fill="none" stroke-linecap="round"/>
  
  <!-- Main content -->
  <g transform="translate(300, 200)">
    <!-- Connection lines showing progress -->
    <path class="connection-line" d="M-50,0 C-80,-30 -120,-30 -150,0" stroke="#30D5C8" stroke-width="2" fill="none"/>
    <path class="connection-line" d="M50,0 C80,-30 120,-30 150,0" stroke="#30D5C8" stroke-width="2" fill="none" style="animation-delay: -1s"/>
    
    <!-- Center translation service -->
    <g class="center-icon" filter="url(#shadow)">
      <circle cx="0" cy="0" r="50" fill="white" stroke="#30D5C8" stroke-width="2"/>
      
      <!-- Translation icon -->
      <g transform="translate(0, 0)">
        <rect x="-20" y="-20" width="40" height="40" rx="5" fill="#f8f9fa" stroke="#30D5C8" stroke-width="2"/>
        <line x1="-12" y1="0" x2="12" y2="0" stroke="#30D5C8" stroke-width="2"/>
        <line x1="-12" y1="-8" x2="12" y2="-8" stroke="#30D5C8" stroke-width="1.5"/>
        <line x1="-12" y1="8" x2="12" y2="8" stroke="#30D5C8" stroke-width="1.5"/>
      </g>
      
      <!-- Progress indicator -->
      <circle cx="0" cy="0" r="35" fill="none" stroke="#30D5C8" stroke-width="1" class="pulse-animation"/>
    </g>
    
    <!-- Source document (starting point) -->
    <g class="document" transform="translate(-200, 0)" filter="url(#shadow)">
      <!-- Document container -->
      <rect x="-50" y="-70" width="100" height="140" rx="5" fill="white" stroke="#ddd" stroke-width="1"/>
      
      <!-- Document content -->
      <rect x="-35" y="-55" width="70" height="110" fill="#f8f9fa" stroke="#eaeaea" stroke-width="0.5"/>
      
      <!-- Document header -->
      <rect x="-35" y="-55" width="70" height="15" fill="#f0f0f0" stroke="#eaeaea" stroke-width="0.5"/>
      
      <!-- Document lines -->
      <line x1="-25" y1="-45" x2="25" y2="-45" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-30" x2="25" y2="-30" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-15" x2="25" y2="-15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="0" x2="25" y2="0" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="15" x2="25" y2="15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="30" x2="15" y2="30" stroke="#ccc" stroke-width="1"/>
      
      <!-- Start flag -->
      <g transform="translate(-35, -70)">
        <rect x="-10" y="-15" width="20" height="15" fill="#30D5C8"/>
        <line x1="0" y1="-15" x2="0" y2="15" stroke="#30D5C8" stroke-width="2"/>
      </g>
    </g>
    
    <!-- Translated document (goal achieved) -->
    <g class="document" transform="translate(200, 0)" filter="url(#shadow)">
      <!-- Document container -->
      <rect x="-50" y="-70" width="100" height="140" rx="5" fill="white" stroke="#ddd" stroke-width="1"/>
      
      <!-- Document content -->
      <rect x="-35" y="-55" width="70" height="110" fill="#f8f9fa" stroke="#eaeaea" stroke-width="0.5"/>
      
      <!-- Document header -->
      <rect x="-35" y="-55" width="70" height="15" fill="#f0f0f0" stroke="#eaeaea" stroke-width="0.5"/>
      
      <!-- Document lines -->
      <line x1="-25" y1="-45" x2="25" y2="-45" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-30" x2="25" y2="-30" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-15" x2="25" y2="-15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="0" x2="25" y2="0" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="15" x2="25" y2="15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="30" x2="15" y2="30" stroke="#ccc" stroke-width="1"/>
      
      <!-- Goal flag/checkmark -->
      <g transform="translate(35, -70)" class="goal-element">
        <circle cx="0" cy="0" r="15" fill="url(#goalGradient)"/>
        <path d="M-5,0 L-1,4 L5,-3" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="checkmark-animation"/>
      </g>
    </g>
  </g>
  
  <!-- Goal-oriented elements -->
  <g class="goal-element" transform="translate(500, 100)" filter="url(#shadow)">
    <circle cx="0" cy="0" r="30" fill="white" stroke="#30D5C8" stroke-width="1"/>
    <circle cx="0" cy="0" r="20" fill="white" stroke="#30D5C8" stroke-width="1"/>
    <circle cx="0" cy="0" r="10" fill="#30D5C8"/>
    <circle cx="0" cy="0" r="5" fill="white"/>
  </g>
  
  <g class="goal-element" transform="translate(100, 100)" filter="url(#shadow)">
    <rect x="-20" y="-20" width="40" height="40" rx="5" fill="white" stroke="#30D5C8" stroke-width="1"/>
    <line x1="-10" y1="-10" x2="10" y2="10" stroke="#30D5C8" stroke-width="1.5"/>
    <line x1="-10" y1="10" x2="10" y2="-10" stroke="#30D5C8" stroke-width="1.5"/>
  </g>
  
  <!-- Progress indicators -->
  <g transform="translate(150, 300)">
    <circle cx="0" cy="0" r="10" fill="#e9ecef"/>
    <circle cx="0" cy="0" r="5" fill="#30D5C8"/>
  </g>
  
  <g transform="translate(300, 300)">
    <circle cx="0" cy="0" r="10" fill="#e9ecef"/>
    <circle cx="0" cy="0" r="5" fill="#30D5C8"/>
  </g>
  
  <g transform="translate(450, 300)">
    <circle cx="0" cy="0" r="10" fill="#e9ecef"/>
    <circle cx="0" cy="0" r="5" fill="url(#goalGradient)"/>
  </g>
  
  <!-- Progress bar at bottom -->
  <rect x="100" y="350" width="400" height="10" rx="5" fill="#e9ecef"/>
  <rect x="100" y="350" width="400" height="10" rx="5" fill="url(#accentGradient)" opacity="0.3"/>
  <rect x="100" y="350" width="300" height="10" rx="5" fill="url(#accentGradient)"/>
  <rect x="400" y="350" width="100" height="10" rx="5" fill="url(#goalGradient)"/>
</svg>
