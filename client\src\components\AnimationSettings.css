.animation-settings {
  margin-top: 16px;
}

.animation-section {
  margin-bottom: 16px;
}

.animation-section h4 {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-color);
}

.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.toggle-label {
  font-size: 14px;
  color: var(--text-color);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: .3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

.transition-speed-options {
  display: flex;
  gap: 8px;
}

.animation-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color);
}

.animation-option:hover {
  background-color: var(--hover-bg);
}

.animation-option.active {
  background-color: var(--accent-color-light);
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.animation-option-name {
  font-size: 14px;
}
