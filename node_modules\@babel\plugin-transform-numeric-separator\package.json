{"name": "@babel/plugin-transform-numeric-separator", "version": "7.25.9", "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-numeric-separator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-numeric-separator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}