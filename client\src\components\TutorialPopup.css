/* Tutorial Popup Styles */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  animation: tutorialOverlayFadeIn 0.4s ease-out;
}

@keyframes tutorialOverlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

.tutorial-popup {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2rem;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  max-width: 600px;
  width: 100%;
  padding: 3rem;
  position: relative;
  animation: tutorialSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

@keyframes tutorialSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.tutorial-popup::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  animation: tutorialBackgroundFloat 8s ease-in-out infinite;
  pointer-events: none;
}

@keyframes tutorialBackgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.tutorial-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  border-radius: 3px;
  transition: width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 600;
  min-width: 60px;
}

.tutorial-skip {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  padding: 0.75rem 1.5rem;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tutorial-skip:hover {
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.tutorial-content {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.3s ease;
}

.tutorial-content.animating {
  opacity: 0.5;
  transform: scale(0.95);
}

.tutorial-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: inline-block;
}

.tutorial-icon.fadeIn {
  animation: fadeIn 0.8s ease-out;
}

.tutorial-icon.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

.tutorial-icon.slideInRight {
  animation: slideInRight 0.8s ease-out;
}

.tutorial-icon.slideInUp {
  animation: slideInUp 0.8s ease-out;
}

.tutorial-icon.pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.tutorial-icon.bounce {
  animation: bounce 1s ease-in-out infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(50px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.tutorial-title {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1f2937, #4f46e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.tutorial-description {
  font-size: 1.1rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.tutorial-dots {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.tutorial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: white;
}

.tutorial-dot.active {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: scale(1.3);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.tutorial-dot.completed {
  background: #10b981;
  transform: scale(1.1);
}

.tutorial-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.tutorial-btn {
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: none;
  position: relative;
  overflow: hidden;
  min-width: 120px;
}

.tutorial-btn.primary {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 
    0 10px 25px rgba(79, 70, 229, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tutorial-btn.primary:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 15px 35px rgba(79, 70, 229, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.tutorial-btn.secondary {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  color: #4b5563;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.tutorial-btn.secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.tutorial-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.tutorial-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tutorial-btn:hover::before {
  left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tutorial-popup {
    padding: 2rem;
    margin: 1rem;
    border-radius: 1.5rem;
  }
  
  .tutorial-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .tutorial-title {
    font-size: 1.5rem;
  }
  
  .tutorial-description {
    font-size: 1rem;
  }
  
  .tutorial-actions {
    flex-direction: column;
  }
  
  .tutorial-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .tutorial-overlay {
    padding: 1rem;
  }
  
  .tutorial-popup {
    padding: 1.5rem;
  }
  
  .tutorial-content {
    min-height: 250px;
  }
  
  .tutorial-icon {
    font-size: 3rem;
  }
}
