/* Settings Tab Styles */
.settings-container {
  display: flex;
  gap: 2rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* Settings Sidebar */
.settings-sidebar {
  width: 250px;
  flex-shrink: 0;
  background-color: var(--card-bg);
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  overflow: hidden;
  height: fit-content;
}

.settings-sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.settings-sidebar-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.settings-nav {
  padding: 1rem 0;
}

.settings-nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.settings-nav-item:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.settings-nav-item.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-left-color: var(--accent-color);
  font-weight: 500;
}

.settings-nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

/* Settings Content */
.settings-content {
  flex: 1;
  max-width: calc(100% - 250px - 2rem);
}

.settings-panel {
  background-color: var(--card-bg);
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  padding: 2rem;
  margin-bottom: 1.5rem;
}

.settings-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.settings-panel-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settings-panel-title h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.settings-panel-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 8px;
  flex-shrink: 0;
}

.settings-panel-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

/* Form Elements */
.settings-form-group {
  margin-bottom: 1.5rem;
}

.settings-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.settings-form-group input[type="text"],
.settings-form-group input[type="email"],
.settings-form-group input[type="password"],
.settings-form-group select,
.settings-form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s ease;
}

.settings-form-group input:focus,
.settings-form-group select:focus,
.settings-form-group textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.settings-form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* Toggle Switch */
.settings-toggle {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.settings-toggle-label {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Button Styles */
.settings-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.settings-btn-primary {
  background-color: var(--accent-color);
  color: white;
  border: none;
}

.settings-btn-primary:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-2px);
}

.settings-btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.settings-btn-secondary:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.settings-btn-danger {
  background-color: #fee2e2;
  color: #b91c1c;
  border: 1px solid #fecaca;
}

.settings-btn-danger:hover {
  background-color: #fecaca;
  color: #991b1b;
  transform: translateY(-2px);
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
  }
  
  .settings-content {
    max-width: 100%;
  }
}
