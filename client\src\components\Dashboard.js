import React, { useState, useEffect, useRef } from 'react';
import './Dashboard.css';
import './Dashboard-modern.css';
import './Dashboard-sections.css';
import './Dashboard-animations.css';
import './Settings.css';
import './SettingsTabs.css';
import EditProfile from './EditProfile';

import Modal from './Modal';
import NotificationManager, { notify } from './NotificationManager';
import VerificationPage from './VerificationPage';
import OnboardingTutorial from './OnboardingTutorial';
import ThemeSelector from './ThemeSelector';
import FontSettings from './FontSettings';
import AnimationSettings from './AnimationSettings';
import LayoutSettings from './LayoutSettings';
import TranslatorChart from './TranslatorChart';
import BannerCustomizer from './BannerCustomizer';
import BannerDecorations from './BannerDecorations';
import ColorChangingLogo from './ColorChangingLogo';
import { getAllTranslators } from '../services/translatorService';

// Language translations
const translations = {
  en: {
    dashboard: 'Dashboard',
    myTranslations: 'My Translations',
    myWork: 'My Work',
    newTranslation: 'New Translation',
    findTranslator: 'Find Translator',
    priceCalculator: 'Price Calculator',
    translationRequests: 'Translation Requests',
    clientMessages: 'Client Messages',
    settings: 'Settings',
    verification: 'Verification',
    verifyNow: 'Verify Now',
    notVerified: 'Not Verified',
    verified: 'Verified',
    welcomeBack: 'Welcome back',
    clientDescription: 'As a client, you can request translations, track their progress, and manage your translation projects.',
    translatorDescription: 'As a translator, you can view available translation jobs, submit your work, and track your translation history.',
    newTranslationRequest: 'New Translation Request',
    viewTranslationRequests: 'View Translation Requests',
    yourProfile: 'Your Profile',
    editProfile: 'Edit Profile',
    activeRequests: 'Active Requests',
    completed: 'Completed',
    totalWords: 'Total Words',
    availableRequests: 'Available Requests',
    completedJobs: 'Completed Jobs',
    earnings: 'Earnings',
    recentActivity: 'Recent Activity',
    accountInformation: 'Account Information',
    name: 'Name',
    email: 'Email',
    accountType: 'Account Type',
    preferences: 'Preferences',
    appearance: 'Appearance',
    language: 'Language',
    theme: 'Theme',
    notifications: 'Notifications',
    security: 'Security',
    lightTheme: 'Light Theme',
    darkTheme: 'Dark Theme',
    systemTheme: 'System Theme',
    emailNotifications: 'Email Notifications',
    browserNotifications: 'Browser Notifications',
    saveChanges: 'Save Changes',
    english: 'English',
    french: 'French',
    spanish: 'Spanish',
    arabic: 'Arabic',
    german: 'German',
    changed: 'changed',
    personalInformation: 'Personal Information',
    professionalInformation: 'Professional Information',
    bio: 'Bio',
    phone: 'Phone',
    location: 'Location',
    languages: 'Languages',
    specializations: 'Specializations',
    cancel: 'Cancel',
    saving: 'Saving...',
    clickToUpload: 'Click to upload profile image',
    recommendedSize: 'Recommended size: 300x300 pixels',
    appearance: 'Appearance',
    chooseTheme: 'Choose Theme',
    accentColor: 'Accent Color',
    lightTheme: 'Light Theme',
    darkTheme: 'Dark Theme',
    systemTheme: 'System Theme',
    turquoise: 'Turquoise',
    blue: 'Blue',
    purple: 'Purple',
    pink: 'Pink',
    red: 'Red',
    orange: 'Orange',
    yellow: 'Yellow',
    green: 'Green',
    welcomeToLingoLink: 'Welcome to LingoLink',
    clientWelcomeMessage: 'Your gateway to professional legal document translation services.',
    findTranslators: 'Find Translators',
    clientFindTranslatorsMessage: 'Browse our network of professional translators specializing in legal documents.',
    manageRequests: 'Manage Requests',
    clientManageRequestsMessage: 'Track and manage your translation requests from submission to completion.',
    communicateEffectively: 'Communicate Effectively',
    clientCommunicateMessage: 'Direct messaging with your translator ensures clear communication throughout the process.',
    customizeYourExperience: 'Customize Your Experience',
    clientCustomizeMessage: 'Personalize your dashboard, set preferences, and choose your theme.',
    translatorWelcomeMessage: 'Welcome to your professional translation workspace.',
    findJobs: 'Find Jobs',
    translatorFindJobsMessage: 'Browse available translation requests that match your expertise.',
    manageTranslations: 'Manage Translations',
    translatorManageMessage: 'Track your active and completed translation projects in one place.',
    communicateWithClients: 'Communicate With Clients',
    translatorCommunicateMessage: 'Direct messaging with clients ensures clear communication throughout the process.',
    customizeYourProfile: 'Customize Your Profile',
    translatorCustomizeMessage: 'Showcase your expertise, languages, and specializations to attract more clients.',
    previous: 'Previous',
    next: 'Next',
    skipTutorial: 'Skip Tutorial',
    getStarted: 'Get Started',
    personalization: 'Personalization',
    client: 'Client',
    translator: 'Translator',
    logout: 'Logout',
    advancedAppearanceSettings: 'Advanced Appearance Settings',
    done: 'Done',
    themeChanged: 'Theme changed successfully',
    tutorialCompleted: 'Tutorial completed',
    fontSettings: 'Font Settings',
    fontSize: 'Font Size',
    fontFamily: 'Font Family',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
    extraLarge: 'Extra Large',
    systemDefault: 'System Default',
    serif: 'Serif',
    sansSerif: 'Sans Serif',
    monospace: 'Monospace',
    animationSettings: 'Animation Settings',
    enableAnimations: 'Enable Animations',
    animationSpeed: 'Animation Speed',
    fast: 'Fast',
    normal: 'Normal',
    slow: 'Slow',
    layoutSettings: 'Layout Settings',
    compactMode: 'Compact Mode',
    sidebarPosition: 'Sidebar Position',
    left: 'Left',
    right: 'Right',
    bannerCustomization: 'Banner Customization',
    bannerStyle: 'Banner Style',
    bannerPattern: 'Banner Pattern',
    bannerAnimation: 'Banner Animation',
    defaultStyle: 'Default',
    gradientStyle: 'Gradient',
    solidStyle: 'Solid',
    minimalStyle: 'Minimal',
    noPattern: 'No Pattern',
    dotsPattern: 'Dots',
    linesPattern: 'Lines',
    wavesPattern: 'Waves',
    geometricPattern: 'Geometric',
    noAnimation: 'No Animation',
    fadeAnimation: 'Fade',
    slideAnimation: 'Slide',
    pulseAnimation: 'Pulse',
    particlesAnimation: 'Particles',
    showDecorations: 'Show Decorations'
  },
  fr: {
    dashboard: 'Tableau de bord',
    myTranslations: 'Mes traductions',
    myWork: 'Mon travail',
    newTranslation: 'Nouvelle traduction',
    findTranslator: 'Trouver un traducteur',
    priceCalculator: 'Calculateur de prix',
    translationRequests: 'Demandes de traduction',
    clientMessages: 'Messages clients',
    settings: 'Paramètres',
    welcomeBack: 'Bon retour',
    clientDescription: 'En tant que client, vous pouvez demander des traductions, suivre leur progression et gérer vos projets de traduction.',
    translatorDescription: 'En tant que traducteur, vous pouvez consulter les travaux de traduction disponibles, soumettre votre travail et suivre votre historique de traduction.',
    newTranslationRequest: 'Nouvelle demande de traduction',
    viewTranslationRequests: 'Voir les demandes de traduction',
    yourProfile: 'Votre profil',
    editProfile: 'Modifier le profil',
    activeRequests: 'Demandes actives',
    completed: 'Terminé',
    totalWords: 'Total des mots',
    availableRequests: 'Demandes disponibles',
    completedJobs: 'Travaux terminés',
    earnings: 'Gains',
    recentActivity: 'Activité récente',
    accountInformation: 'Informations du compte',
    name: 'Nom',
    email: 'Email',
    accountType: 'Type de compte',
    preferences: 'Préférences',
    appearance: 'Apparence',
    language: 'Langue',
    theme: 'Thème',
    notifications: 'Notifications',
    security: 'Sécurité',
    lightTheme: 'Thème clair',
    darkTheme: 'Thème sombre',
    systemTheme: 'Thème système',
    emailNotifications: 'Notifications par email',
    browserNotifications: 'Notifications du navigateur',
    saveChanges: 'Enregistrer les modifications',
    english: 'Anglais',
    french: 'Français',
    spanish: 'Espagnol',
    arabic: 'Arabe',
    german: 'Allemand',
    personalInformation: 'Informations personnelles',
    professionalInformation: 'Informations professionnelles',
    bio: 'Biographie',
    phone: 'Téléphone',
    location: 'Emplacement',
    languages: 'Langues',
    specializations: 'Spécialisations',
    cancel: 'Annuler',
    saving: 'Enregistrement...',
    clickToUpload: 'Cliquez pour télécharger une image de profil',
    recommendedSize: 'Taille recommandée: 300x300 pixels',
    personalization: 'Personnalisation',
    client: 'Client',
    translator: 'Traducteur',
    logout: 'Déconnexion',
    changed: 'modifiée'
  },
  es: {
    dashboard: 'Panel de control',
    myTranslations: 'Mis traducciones',
    myWork: 'Mi trabajo',
    newTranslation: 'Nueva traducción',
    findTranslator: 'Encontrar traductor',
    priceCalculator: 'Calculadora de precios',
    translationRequests: 'Solicitudes de traducción',
    clientMessages: 'Mensajes de clientes',
    settings: 'Configuración',
    welcomeBack: 'Bienvenido de nuevo',
    clientDescription: 'Como cliente, puede solicitar traducciones, seguir su progreso y gestionar sus proyectos de traducción.',
    translatorDescription: 'Como traductor, puede ver los trabajos de traducción disponibles, enviar su trabajo y seguir su historial de traducción.',
    newTranslationRequest: 'Nueva solicitud de traducción',
    viewTranslationRequests: 'Ver solicitudes de traducción',
    yourProfile: 'Tu perfil',
    editProfile: 'Editar perfil',
    activeRequests: 'Solicitudes activas',
    completed: 'Completado',
    totalWords: 'Total de palabras',
    availableRequests: 'Solicitudes disponibles',
    completedJobs: 'Trabajos completados',
    earnings: 'Ganancias',
    recentActivity: 'Actividad reciente',
    accountInformation: 'Información de la cuenta',
    name: 'Nombre',
    email: 'Correo electrónico',
    accountType: 'Tipo de cuenta',
    preferences: 'Preferencias',
    appearance: 'Apariencia',
    language: 'Idioma',
    theme: 'Tema',
    notifications: 'Notificaciones',
    security: 'Seguridad',
    lightTheme: 'Tema claro',
    darkTheme: 'Tema oscuro',
    systemTheme: 'Tema del sistema',
    emailNotifications: 'Notificaciones por correo electrónico',
    browserNotifications: 'Notificaciones del navegador',
    saveChanges: 'Guardar cambios',
    english: 'Inglés',
    french: 'Francés',
    spanish: 'Español',
    arabic: 'Árabe',
    german: 'Alemán',
    personalization: 'Personalización',
    client: 'Cliente',
    translator: 'Traductor',
    logout: 'Cerrar sesión',
    changed: 'cambiado'
  },
  ar: {
    dashboard: 'لوحة التحكم',
    myTranslations: 'ترجماتي',
    myWork: 'عملي',
    newTranslation: 'ترجمة جديدة',
    findTranslator: 'البحث عن مترجم',
    priceCalculator: 'حاسبة الأسعار',
    translationRequests: 'طلبات الترجمة',
    clientMessages: 'رسائل العملاء',
    settings: 'الإعدادات',
    welcomeBack: 'مرحبًا بعودتك',
    clientDescription: 'كعميل، يمكنك طلب الترجمات وتتبع تقدمها وإدارة مشاريع الترجمة الخاصة بك.',
    translatorDescription: 'كمترجم، يمكنك عرض وظائف الترجمة المتاحة وتقديم عملك وتتبع تاريخ الترجمة الخاص بك.',
    newTranslationRequest: 'طلب ترجمة جديد',
    viewTranslationRequests: 'عرض طلبات الترجمة',
    yourProfile: 'ملفك الشخصي',
    editProfile: 'تعديل الملف الشخصي',
    activeRequests: 'الطلبات النشطة',
    completed: 'مكتمل',
    totalWords: 'إجمالي الكلمات',
    availableRequests: 'الطلبات المتاحة',
    completedJobs: 'الوظائف المكتملة',
    earnings: 'الأرباح',
    recentActivity: 'النشاط الأخير',
    accountInformation: 'معلومات الحساب',
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    accountType: 'نوع الحساب',
    preferences: 'التفضيلات',
    appearance: 'المظهر',
    language: 'اللغة',
    theme: 'السمة',
    notifications: 'الإشعارات',
    security: 'الأمان',
    lightTheme: 'السمة الفاتحة',
    darkTheme: 'السمة الداكنة',
    systemTheme: 'سمة النظام',
    emailNotifications: 'إشعارات البريد الإلكتروني',
    browserNotifications: 'إشعارات المتصفح',
    saveChanges: 'حفظ التغييرات',
    english: 'الإنجليزية',
    french: 'الفرنسية',
    spanish: 'الإسبانية',
    arabic: 'العربية',
    german: 'الألمانية',
    personalization: 'التخصيص',
    client: 'عميل',
    translator: 'مترجم',
    logout: 'تسجيل الخروج',
    changed: 'تم التغيير'
  }
};

// SVG Icons for the dashboard
const DocumentIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
    <polyline points="14 2 14 8 20 8"></polyline>
    <line x1="16" y1="13" x2="8" y2="13"></line>
    <line x1="16" y1="17" x2="8" y2="17"></line>
    <polyline points="10 9 9 9 8 9"></polyline>
  </svg>
);

const VerificationIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
    <polyline points="22 4 12 14.01 9 11.01"></polyline>
  </svg>
);

const CheckIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polyline points="20 6 9 17 4 12"></polyline>
  </svg>
);

const WordsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
);



const Dashboard = ({ user, setUser, onLogout }) => {
  // All useState hooks must be at the top, before any return or conditional
  const [activeView, setActiveView] = useState('dashboard');
  const [translationItems, setTranslationItems] = useState([]);
  const [stats, setStats] = useState({ active: 0, completed: 0, totalWords: 0 });
  const [chartData, setChartData] = useState({ months: [], tasks: [], pages: [] });
  const [activePeriod, setActivePeriod] = useState('year');
  const [translators, setTranslators] = useState([]);
  const [filteredTranslators, setFilteredTranslators] = useState([]);
  const [refreshTranslators, setRefreshTranslators] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Find Translator filter states
  const [sourceLang, setSourceLang] = useState('');
  const [targetLang, setTargetLang] = useState('');
  const [specialty, setSpecialty] = useState('');
  const [minRating, setMinRating] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 0.25]);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState('rating'); // 'rating', 'price', 'projects'
  const [searchQuery, setSearchQuery] = useState('');
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileUpdateSuccess, setProfileUpdateSuccess] = useState(false);
  const [showTutorial, setShowTutorial] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState('appearance');

  // Settings tab state
  const [settingsTab, setSettingsTab] = useState('account');
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');
  const [settingsChanged, setSettingsChanged] = useState(false);

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    browserNotifications: true,
    marketingEmails: false,
    newRequestNotifications: true,
    messageNotifications: true,
    updateNotifications: true
  });

  // Privacy settings
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'public',
    showEmail: false,
    showPhone: false,
    allowDataCollection: true
  });

  // Accessibility settings
  const [accessibilitySettings, setAccessibilitySettings] = useState({
    highContrast: false,
    largerText: false,
    reducedMotion: false
  });
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('preferredLanguage');
    return savedLanguage || 'en';
  });
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme === 'dark';
  });

  // Banner customization state
  const [bannerStyle, setBannerStyle] = useState(localStorage.getItem('bannerStyle') || 'default');
  const [bannerPattern, setBannerPattern] = useState(localStorage.getItem('bannerPattern') || 'none');
  const [bannerAnimation, setBannerAnimation] = useState(localStorage.getItem('bannerAnimation') || 'fade');
  const [showDecorations, setShowDecorations] = useState(localStorage.getItem('showDecorations') !== 'false');
  // Calculator states (move here)
  const [calcSourceLang, setCalcSourceLang] = useState('');
  const [calcTargetLang, setCalcTargetLang] = useState('');
  const [calcDocType, setCalcDocType] = useState('general');
  const [calcPages, setCalcPages] = useState(1);
  const [calcPricePerPage, setCalcPricePerPage] = useState(15);
  const [calcDelivery, setCalcDelivery] = useState('standard');
  const [calcServices, setCalcServices] = useState({ proofreading: false, formatting: false, certified: false });
  const [calcResult, setCalcResult] = useState(null);
  const [calcError, setCalcError] = useState('');

  const servicePercentages = {
    proofreading: 0.1, // 10%
    formatting: 0.08,  // 8%
    certified: 0.2     // 20%
  };
  const deliveryPercentages = {
    standard: 0,
    express: 0.15, // 15%
    urgent: 0.3    // 30%
  };

  const handleCalcServiceChange = (service) => {
    setCalcServices(s => ({ ...s, [service]: !s[service] }));
  };

  const handleCalculatePrice = (e) => {
    e.preventDefault();
    setCalcError('');
    if (!calcSourceLang || !calcTargetLang) {
      setCalcError('Please select both source and target languages.');
      return;
    }
    if (calcPages < 1) {
      setCalcError('Number of pages must be at least 1.');
      return;
    }
    if (calcPricePerPage < 15) {
      setCalcError('Minimum price per page is 15 TND.');
      return;
    }
    // Base price
    let base = calcPages * calcPricePerPage;
    // Delivery markup
    let deliveryMarkup = base * deliveryPercentages[calcDelivery];
    // Services markup
    let servicesMarkup = 0;
    Object.keys(calcServices).forEach(s => {
      if (calcServices[s]) servicesMarkup += base * servicePercentages[s];
    });
    // Total
    let total = base + deliveryMarkup + servicesMarkup;
    setCalcResult({
      base,
      deliveryMarkup,
      servicesMarkup,
      total
    });
  };

  // Apply filters to translators
  const applyFilters = () => {
    if (!translators || translators.length === 0) return;

    let filtered = [...translators];

    // Filter by source language
    if (sourceLang) {
      filtered = filtered.filter(t =>
        t.languages && t.languages.some(lang =>
          lang.toLowerCase().includes(sourceLang.toLowerCase())
        )
      );
    }

    // Filter by target language
    if (targetLang) {
      filtered = filtered.filter(t =>
        t.languages && t.languages.some(lang =>
          lang.toLowerCase().includes(targetLang.toLowerCase())
        )
      );
    }

    // Filter by specialty
    if (specialty) {
      filtered = filtered.filter(t =>
        t.specialties && t.specialties.some(spec =>
          spec.toLowerCase().includes(specialty.toLowerCase())
        )
      );
    }

    // Filter by minimum rating
    if (minRating !== 'all') {
      const minRatingValue = parseFloat(minRating);
      filtered = filtered.filter(t => (t.rating || 0) >= minRatingValue);
    }

    // Filter by price range
    if (priceRange && priceRange.length === 2) {
      filtered = filtered.filter(t => {
        const rate = parseFloat(t.ratePerWord) || 0;
        return rate >= priceRange[0] && rate <= priceRange[1];
      });
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t =>
        (t.name && t.name.toLowerCase().includes(query)) ||
        (t.description && t.description.toLowerCase().includes(query)) ||
        (t.specialties && t.specialties.some(s => s.toLowerCase().includes(query))) ||
        (t.languages && t.languages.some(l => l.toLowerCase().includes(query)))
      );
    }

    // Sort translators
    if (sortBy === 'rating') {
      filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    } else if (sortBy === 'price') {
      filtered.sort((a, b) => (parseFloat(a.ratePerWord) || 0) - (parseFloat(b.ratePerWord) || 0));
    } else if (sortBy === 'projects') {
      filtered.sort((a, b) => (b.completedProjects || 0) - (a.completedProjects || 0));
    }

    setFilteredTranslators(filtered);
  };

  // Language and theme settings
  const t = (key) => {
    try {
      // First check if the language exists in translations
      if (translations[currentLanguage]) {
        // Then check if the key exists in that language
        if (translations[currentLanguage][key]) {
          return translations[currentLanguage][key];
        }
      }

      // Fallback to English
      if (translations['en'] && translations['en'][key]) {
        return translations['en'][key];
      }

      // If all else fails, return the key itself
      console.warn(`Translation missing for key: ${key} in language: ${currentLanguage}`);
      return key;
    } catch (error) {
      console.error(`Error in translation function for key: ${key}`, error);
      return key;
    }
  };

  // Apply filters whenever filter states change
  useEffect(() => {
    applyFilters();
  }, [translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery]);

  // Initialize data and fetch real translators
  useEffect(() => {
    console.log('Setting up dashboard data');
    setLoading(true);

    // Add a sample bio if none exists (for demo purposes)
    if (!user.bio && user.userType === 'translator') {
      user.bio = "Professional translator with expertise in technical and business content. Fluent in multiple languages with a focus on delivering high-quality translations that maintain the original meaning and context.";
    } else if (!user.bio && user.userType === 'client') {
      user.bio = "Business professional seeking quality translation services for international expansion. Looking for reliable translators who can help with technical documentation and marketing materials.";
    }

    // Initialize with empty data
    setTranslationItems([]);
    setStats({ active: 0, completed: 0, totalWords: 0 });

    // Apply theme
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

    // Apply accent color to welcome banner
    const accentColor = localStorage.getItem('accentColor') || 'turquoise';
    const accentColors = {
      'turquoise': { color: '#20B2AA', rgb: '32, 178, 170' },
      'blue': { color: '#3182CE', rgb: '49, 130, 206' },
      'purple': { color: '#805AD5', rgb: '128, 90, 213' },
      'pink': { color: '#D53F8C', rgb: '213, 63, 140' },
      'red': { color: '#E53E3E', rgb: '229, 62, 62' },
      'orange': { color: '#DD6B20', rgb: '221, 107, 32' },
      'yellow': { color: '#D69E2E', rgb: '214, 158, 46' },
      'green': { color: '#38A169', rgb: '56, 161, 105' }
    };

    const selectedColor = accentColors[accentColor] || accentColors['turquoise'];
    const root = document.documentElement;
    root.style.setProperty('--accent-color-rgb', selectedColor.rgb);

    // Apply language direction
    if (currentLanguage === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
    }

    // Check if tutorial should be shown (first login)
    const tutorialCompleted = localStorage.getItem('tutorialCompleted');
    if (!tutorialCompleted) {
      setShowTutorial(true);
    }

    // Initialize chart data for translator dashboard
    if (user && user.userType === 'translator') {
      updateChartData('year');
    }



    // Fetch real translators if user is a client
    const fetchTranslators = async () => {
      if (user && user.userType === 'client') {
        try {
          setLoading(true);
          setError('');

          // Always show Ouday Kefi as a guaranteed translator
          const guaranteedTranslator = {
            id: 'ouday-kefi',
            name: 'Ouday Kefi',
            email: '<EMAIL>',
            userType: 'translator',
            languages: ['English', 'Arabic', 'French'],
            specialties: ['Technical', 'IT', 'Business'],
            rating: 4.9,
            reviewCount: 42,
            completedProjects: 87,
            onTimePercentage: 100,
            ratePerWord: 0.12,
            description: 'Professional translator specializing in Technical and IT translations. Fluent in English, Arabic, and French with extensive experience in software localization and technical documentation.'
          };

          // Start with at least Ouday Kefi
          let allTranslators = [guaranteedTranslator];

          // Set up a timeout for the API request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

          try {
            // Fetch translators from the API
            console.log('Fetching translators from API...');
            const fetchedTranslators = await getAllTranslators();
            console.log('Fetched translators from API:', fetchedTranslators);

            if (fetchedTranslators && fetchedTranslators.length > 0) {
              // Add API translators to our list, but keep Ouday Kefi
              const apiTranslators = fetchedTranslators.filter(t => t.id !== 'ouday-kefi');
              allTranslators = [...allTranslators, ...apiTranslators];
              console.log(`Successfully loaded ${fetchedTranslators.length} translators from Supabase`);
            } else {
              console.log('No additional translators found in the database');
            }

            clearTimeout(timeoutId);
          } catch (apiError) {
            clearTimeout(timeoutId);
            console.error('Error fetching translators from API:', apiError);

            // Don't show error message, just use fallback data
            console.log('Using fallback data for translators');
          }

          // Check if there are any registered translators in localStorage
          try {
            const registeredUsers = localStorage.getItem('registeredUsers');

            if (registeredUsers) {
              const parsedUsers = JSON.parse(registeredUsers);
              const translatorUsers = parsedUsers.filter(u =>
                u.userType === 'translator' &&
                u.id !== 'ouday-kefi' &&
                !allTranslators.some(t => t.id === u.id)
              );

              if (translatorUsers.length > 0) {
                console.log('Found registered translators in localStorage:', translatorUsers);

                // Convert registered users to translator format
                const localTranslators = translatorUsers.map(user => {
                  // Use existing languages if available, otherwise generate
                  const languages = user.languages || ['English'];

                  // Use existing specialties if available, otherwise generate
                  let specialties = user.specializations || user.specialties;

                  if (!specialties || specialties.length === 0) {
                    const allSpecialties = [
                      'Legal', 'Medical', 'Technical', 'Financial', 'Marketing',
                      'Literary', 'Academic', 'Scientific', 'Business', 'IT'
                    ];

                    const numSpecialties = 1 + Math.floor(Math.random() * 2);
                    specialties = [];

                    // For other translators, pick random specialties
                    while (specialties.length < numSpecialties) {
                      const specialty = allSpecialties[Math.floor(Math.random() * allSpecialties.length)];
                      if (!specialties.includes(specialty)) {
                        specialties.push(specialty);
                      }
                    }
                  }

                  return {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    userType: 'translator',
                    languages: languages,
                    specialties: specialties,
                    rating: 4.0 + Math.random(),
                    reviewCount: Math.floor(Math.random() * 50),
                    completedProjects: Math.floor(Math.random() * 100),
                    onTimePercentage: 90 + Math.floor(Math.random() * 10),
                    ratePerWord: (0.08 + Math.random() * 0.07).toFixed(2),
                    description: `Professional translator specializing in ${specialties.join(' and ')} translations. Fluent in ${languages.join(', ')}.`,
                    profileImage: user.profileImage
                  };
                });

                // Add local translators to our list
                allTranslators = [...allTranslators, ...localTranslators];
              }
            }
          } catch (e) {
            console.error('Error processing registered users:', e);
          }

          // Set all translators
          setTranslators(allTranslators);
          setFilteredTranslators(allTranslators);
          console.log(`Showing ${allTranslators.length} translators in total`);

        } catch (error) {
          console.error('Error in translator fetching process:', error);
          // Don't show error, just use Ouday Kefi
          const fallbackTranslator = {
            id: 'ouday-kefi',
            name: 'Ouday Kefi',
            email: '<EMAIL>',
            userType: 'translator',
            languages: ['English', 'Arabic', 'French'],
            specialties: ['Technical', 'IT', 'Business'],
            rating: 4.9,
            reviewCount: 42,
            completedProjects: 87,
            onTimePercentage: 100,
            ratePerWord: 0.12,
            description: 'Professional translator specializing in Technical and IT translations. Fluent in English, Arabic, and French with extensive experience in software localization and technical documentation.'
          };
          setTranslators([fallbackTranslator]);
          setFilteredTranslators([fallbackTranslator]);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    fetchTranslators();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDarkMode, currentLanguage, user, refreshTranslators]);



  // Handle theme toggle
  const toggleTheme = () => {
    const newThemeValue = !isDarkMode;
    setIsDarkMode(newThemeValue);
    localStorage.setItem('theme', newThemeValue ? 'dark' : 'light');
    document.documentElement.setAttribute('data-theme', newThemeValue ? 'dark' : 'light');
  };

  // Handle language change
  const changeLanguage = (langCode) => {
    // Add animation class to language button
    const langButton = document.querySelector('.language-dropdown-btn');
    if (langButton) {
      // Add the changed class
      langButton.classList.add('changed');

      // Add rotation effect
      langButton.style.transform = 'rotate(360deg)';

      // Remove the class and reset transform after animation completes
      setTimeout(() => {
        langButton.classList.remove('changed');
        langButton.style.transform = '';
      }, 1000);
    }

    setCurrentLanguage(langCode);
    localStorage.setItem('preferredLanguage', langCode);

    // Set RTL for Arabic
    if (langCode === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
    }

    // Show notification
    notify(`${t('language')} ${t('changed')}`, 'success', 2000);
  };



  const handleSaveProfile = async (formData) => {
    try {
      console.log('Saving profile data:', formData);

      // Create a new updated user object
      const updatedUser = {
        ...user,
        name: formData.get('name'),
        email: formData.get('email'),
        bio: formData.get('bio'),
        phone: formData.get('phone'),
        location: formData.get('location'),
        languages: JSON.parse(formData.get('languages')),
        specializations: JSON.parse(formData.get('specializations'))
      };

      // Handle profile image
      if (formData.get('profileImage')) {
        const profileImage = formData.get('profileImage');

        try {
          // Create a persistent URL for the image
          const imageUrl = URL.createObjectURL(profileImage);
          updatedUser.profileImage = imageUrl;

          // Store the image in localStorage as base64 for persistence
          const reader = new FileReader();
          reader.readAsDataURL(profileImage);
          reader.onloadend = () => {
            const base64data = reader.result;
            localStorage.setItem('profileImageData', base64data);
          };
        } catch (imageError) {
          console.error('Error processing profile image:', imageError);
        }
      }

      // Try to update the user in the API
      try {
        // Simulate API call with a timeout
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real app, you would send this data to your API
        // const response = await fetch(`${API_URL}/api/users/profile`, {
        //   method: 'PUT',
        //   headers: {
        //     'Authorization': `Bearer ${localStorage.getItem('token')}`,
        //     'Content-Type': 'application/json'
        //   },
        //   body: JSON.stringify(updatedUser)
        // });

        // if (!response.ok) {
        //   throw new Error('Failed to update profile on server');
        // }
      } catch (apiError) {
        console.warn('Could not update profile on server, saving locally only:', apiError);
      }

      // Update localStorage
      localStorage.setItem('user', JSON.stringify(updatedUser));

      // Update the user state in the current component
      setUser(updatedUser);

      // Update registered users list if this is a translator
      if (updatedUser.userType === 'translator') {
        try {
          const existingUsers = localStorage.getItem('registeredUsers');
          if (existingUsers) {
            const users = JSON.parse(existingUsers);
            const updatedUsers = users.map(u =>
              u.id === updatedUser.id ? updatedUser : u
            );
            localStorage.setItem('registeredUsers', JSON.stringify(updatedUsers));

            // Trigger a refresh of the translator list
            setRefreshTranslators(prev => prev + 1);
            console.log('Triggered translator list refresh after profile update');
          }
        } catch (e) {
          console.error('Error updating registered users:', e);
        }
      }

      // Show success message
      setProfileUpdateSuccess(true);
      setTimeout(() => setProfileUpdateSuccess(false), 3000);

      // Exit edit mode
      setIsEditingProfile(false);

      // Return to profile view
      setActiveView('profile');

      return true;
    } catch (error) {
      console.error('Error saving profile:', error);
      return false;
    }
  };

  const handleCancelEditProfile = () => {
    setIsEditingProfile(false);
  };

  const handleThemeChange = (newTheme) => {
    // Apply the theme
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    notify(t('themeChanged'), 'success', 3000);
  };

  const handleTutorialComplete = () => {
    setShowTutorial(false);
    localStorage.setItem('tutorialCompleted', 'true');
    notify(t('tutorialCompleted'), 'success', 3000);
  };

  // Settings handlers
  const handleSettingsTabChange = (tab) => {
    setSettingsTab(tab);
  };

  const handleTogglePasswordChange = () => {
    setShowPasswordChange(!showPasswordChange);
    // Reset password form when toggling
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setPasswordError('');
    setPasswordSuccess('');
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    setPasswordError('');
    setPasswordSuccess('');

    // Validate password
    if (!passwordData.currentPassword) {
      setPasswordError('Current password is required');
      return;
    }

    if (!passwordData.newPassword) {
      setPasswordError('New password is required');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('Passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }

    // Simulate password change
    setTimeout(() => {
      setPasswordSuccess('Password changed successfully');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setShowPasswordChange(false);
    }, 1000);
  };

  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
    setSettingsChanged(true);
  };

  const handlePrivacyChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPrivacySettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setSettingsChanged(true);
  };

  const handleAccessibilityChange = (e) => {
    const { name, checked } = e.target;
    setAccessibilitySettings(prev => ({
      ...prev,
      [name]: checked
    }));
    setSettingsChanged(true);
  };

  const handleSaveSettings = () => {
    // Save settings to localStorage
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
    localStorage.setItem('privacySettings', JSON.stringify(privacySettings));
    localStorage.setItem('accessibilitySettings', JSON.stringify(accessibilitySettings));

    // Apply accessibility settings
    if (accessibilitySettings.largerText) {
      document.documentElement.style.fontSize = '18px';
    } else {
      document.documentElement.style.fontSize = '16px';
    }

    if (accessibilitySettings.highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }

    if (accessibilitySettings.reducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }

    // Show success message
    notify('Settings saved successfully', 'success', 3000);
    setSettingsChanged(false);
  };

  const openSettingsModal = () => {
    setShowSettingsModal(true);
  };

  const closeSettingsModal = () => {
    setShowSettingsModal(false);
    // Reset to appearance tab when modal is closed
    setActiveSettingsTab('appearance');
  };

  // Define additional icons for new features
  const SearchIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <circle cx="11" cy="11" r="8"></circle>
      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
    </svg>
  );

  const CalculatorIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect>
      <line x1="8" y1="6" x2="16" y2="6"></line>
      <line x1="8" y1="10" x2="16" y2="10"></line>
      <line x1="8" y1="14" x2="16" y2="14"></line>
      <line x1="8" y1="18" x2="16" y2="18"></line>
    </svg>
  );

  const RequestsIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
      <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
      <path d="M9 14l2 2 4-4"></path>
    </svg>
  );

  const MessagesIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
    </svg>
  );

  const ProfileIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
      <circle cx="12" cy="7" r="4"></circle>
    </svg>
  );

  // Log the user object to check its structure
  console.log('User object:', user);

  // Add safety check for user object
  if (!user) {
    console.error('User object is undefined or null');
    // You could redirect to login or show an error message here
    return (
      <div className="error-container">
        <h2>Error: User information not available</h2>
        <p>Please try logging in again.</p>
        <button onClick={() => window.location.href = '/login'}>Go to Login</button>
      </div>
    );
  }





  // Restore updateChartData function
  const updateChartData = (period) => {
    let newData = {};
    switch(period) {
      case 'week':
        newData = {
          months: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          tasks: [0, 0, 0, 0, 0, 0, 0],
          pages: [0, 0, 0, 0, 0, 0, 0]
        };
        break;
      case 'month':
        newData = {
          months: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          tasks: [0, 0, 0, 0],
          pages: [0, 0, 0, 0]
        };
        break;
      case 'year':
        newData = {
          months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          tasks: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          pages: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        };
        break;
      default:
        newData = {
          months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          tasks: [0, 0, 0, 0, 0, 0],
          pages: [0, 0, 0, 0, 0, 0]
        };
    }
    setChartData(newData);
    setActivePeriod(period);
  };

  // Create bubbles for the background
  const createBubbles = () => {
    const bubbles = [];
    const bubbleCount = 15;

    for (let i = 0; i < bubbleCount; i++) {
      const size = Math.random() * 60 + 20; // Random size between 20px and 80px
      const left = Math.random() * 100; // Random position from 0% to 100%
      const animationDuration = Math.random() * 10 + 10; // Random duration between 10s and 20s
      const animationDelay = Math.random() * 5; // Random delay between 0s and 5s

      bubbles.push(
        <div
          key={i}
          className="bubble"
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${left}%`,
            animationDuration: `${animationDuration}s`,
            animationDelay: `${animationDelay}s`
          }}
        />
      );
    }

    return bubbles;
  };

  return (
    <div className="dashboard-container">
      {/* Background Bubbles */}
      <div className="dashboard-bubbles">
        {createBubbles()}
      </div>

      {/* Notification Manager */}
      <NotificationManager />

      {/* Onboarding Tutorial */}
      {showTutorial && (
        <OnboardingTutorial
          isOpen={showTutorial}
          userType={user?.userType || 'client'}
          onComplete={handleTutorialComplete}
          translations={translations}
          currentLanguage={currentLanguage}
        />
      )}

      {/* Settings Modal */}
      <Modal
        isOpen={showSettingsModal}
        onClose={closeSettingsModal}
        title={t('personalization')}
        size="medium"
        footer={
          <button className="btn-primary" onClick={closeSettingsModal}>
            {t('done')}
          </button>
        }
      >
        <div className="settings-content" style={{ maxHeight: 'none' }}>
          <div className="settings-tabs">
            <div className="settings-tab-list">
              <button
                className={`settings-tab ${activeSettingsTab === 'appearance' ? 'active' : ''}`}
                onClick={() => setActiveSettingsTab('appearance')}
              >
                {t('appearance')}
              </button>
              <button
                className={`settings-tab ${activeSettingsTab === 'fontSettings' ? 'active' : ''}`}
                onClick={() => setActiveSettingsTab('fontSettings')}
              >
                {t('fontSettings')}
              </button>
              <button
                className={`settings-tab ${activeSettingsTab === 'animationSettings' ? 'active' : ''}`}
                onClick={() => setActiveSettingsTab('animationSettings')}
              >
                {t('animationSettings')}
              </button>
              <button
                className={`settings-tab ${activeSettingsTab === 'layoutSettings' ? 'active' : ''}`}
                onClick={() => setActiveSettingsTab('layoutSettings')}
              >
                {t('layoutSettings')}
              </button>
              <button
                className={`settings-tab ${activeSettingsTab === 'bannerCustomization' ? 'active' : ''}`}
                onClick={() => setActiveSettingsTab('bannerCustomization')}
              >
                {t('bannerCustomization')}
              </button>
            </div>

            <div className="settings-tab-content">
              {activeSettingsTab === 'appearance' && (
                <div className="settings-section">
                  <h3>{t('appearance')}</h3>
                  <ThemeSelector
                    onThemeChange={handleThemeChange}
                    translations={translations}
                    currentLanguage={currentLanguage}
                  />
                </div>
              )}

              {activeSettingsTab === 'fontSettings' && (
                <div className="settings-section">
                  <h3>{t('fontSettings')}</h3>
                  <FontSettings
                    translations={translations}
                    currentLanguage={currentLanguage}
                  />
                </div>
              )}

              {activeSettingsTab === 'animationSettings' && (
                <div className="settings-section">
                  <h3>{t('animationSettings')}</h3>
                  <AnimationSettings
                    translations={translations}
                    currentLanguage={currentLanguage}
                  />
                </div>
              )}

              {activeSettingsTab === 'layoutSettings' && (
                <div className="settings-section">
                  <h3>{t('layoutSettings')}</h3>
                  <LayoutSettings
                    translations={translations}
                    currentLanguage={currentLanguage}
                  />
                </div>
              )}

              {activeSettingsTab === 'bannerCustomization' && (
                <div className="settings-section">
                  <h3>{t('bannerCustomization')}</h3>
                  <BannerCustomizer
                    translations={translations}
                    currentLanguage={currentLanguage}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </Modal>

      <header className="dashboard-header">
        <div className="dashboard-logo">
          <ColorChangingLogo className="logo" alt="LingoLink" size="medium" />
          <h1>LingoLink</h1>
        </div>
        <div className="dashboard-user">
          <span>{t('welcomeBack')}, {user.name}</span>
          <div className="user-type-badge">{user.userType === 'client' ? t('client') : t('translator')}</div>

          <div className="dashboard-controls">
            <div className="language-dropdown">
              <button className="language-dropdown-btn" title={t('language')}>
                {currentLanguage === 'en' && '🇺🇸'}
                {currentLanguage === 'fr' && '🇫🇷'}
                {currentLanguage === 'es' && '🇪🇸'}
                {currentLanguage === 'ar' && '🇸🇦'}
              </button>
              <div className="language-dropdown-content">
                <button
                  className={`language-item ${currentLanguage === 'en' ? 'active' : ''}`}
                  onClick={() => changeLanguage('en')}
                >
                  <span>🇺🇸</span> {t('english')}
                </button>
                <button
                  className={`language-item ${currentLanguage === 'fr' ? 'active' : ''}`}
                  onClick={() => changeLanguage('fr')}
                >
                  <span>🇫🇷</span> {t('french')}
                </button>
                <button
                  className={`language-item ${currentLanguage === 'es' ? 'active' : ''}`}
                  onClick={() => changeLanguage('es')}
                >
                  <span>🇪🇸</span> {t('spanish')}
                </button>
                <button
                  className={`language-item ${currentLanguage === 'ar' ? 'active' : ''}`}
                  onClick={() => changeLanguage('ar')}
                >
                  <span>🇸🇦</span> {t('arabic')}
                </button>
              </div>
            </div>

            <button className="theme-toggle" onClick={toggleTheme}>
              {isDarkMode ? '☀️' : '🌙'}
            </button>
          </div>

          <button onClick={onLogout} className="logout-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            {t('logout') || 'Logout'}
          </button>
        </div>
      </header>

      <div className="dashboard-content">
        <div className="dashboard-sidebar">
          <nav className="dashboard-nav">
            <ul>
              <li
                className={activeView === 'dashboard' ? 'active' : ''}
                onClick={() => setActiveView('dashboard')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="7" height="9"></rect>
                  <rect x="14" y="3" width="7" height="5"></rect>
                  <rect x="14" y="12" width="7" height="9"></rect>
                  <rect x="3" y="16" width="7" height="5"></rect>
                </svg>
                <span>{t('dashboard')}</span>
              </li>

              {/* Common menu items for both user types */}
              <li
                className={activeView === 'translations' ? 'active' : ''}
                onClick={() => setActiveView('translations')}
              >
                <DocumentIcon />
                <span>{user.userType === 'client' ? t('myTranslations') : t('myWork')}</span>
              </li>

              {/* Client-specific menu items */}
              {user.userType === 'client' && (
                <>
                  <li
                    className={activeView === 'find-translator' ? 'active' : ''}
                    onClick={() => setActiveView('find-translator')}
                  >
                    <SearchIcon />
                    <span>{t('findTranslator')}</span>
                  </li>
                  <li
                    className={activeView === 'price-calculator' ? 'active' : ''}
                    onClick={() => setActiveView('price-calculator')}
                  >
                    <CalculatorIcon />
                    <span>{t('priceCalculator')}</span>
                  </li>
                </>
              )}

              {/* Translator-specific menu items */}
              {user.userType === 'translator' && (
                <>
                  <li
                    className={activeView === 'requests' ? 'active' : ''}
                    onClick={() => setActiveView('requests')}
                  >
                    <RequestsIcon />
                    <span>{t('translationRequests')}</span>
                  </li>
                  <li
                    className={activeView === 'messages' ? 'active' : ''}
                    onClick={() => setActiveView('messages')}
                  >
                    <MessagesIcon />
                    <span>{t('clientMessages')}</span>
                  </li>
                </>
              )}

              <li
                className={activeView === 'profile' ? 'active' : ''}
                onClick={() => setActiveView('profile')}
              >
                <ProfileIcon />
                <span>{t('yourProfile')}</span>
              </li>

              <li
                className={activeView === 'verification' ? 'active' : ''}
                onClick={() => setActiveView('verification')}
              >
                <VerificationIcon />
                <span>{t('verification')}</span>
              </li>

              <li
                className={activeView === 'settings' ? 'active' : ''}
                onClick={() => setActiveView('settings')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="3"></circle>
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
                <span>{t('settings')}</span>
              </li>
            </ul>
          </nav>
        </div>

        <main className="dashboard-main">
          {activeView === 'dashboard' && (
            <>
              {error && <div className="error-message">{error}</div>}

              <div className={`welcome-banner banner-style-${bannerStyle}`}>
                {showDecorations && <BannerDecorations pattern={bannerPattern} animation={bannerAnimation} />}
                <div className="decoration"></div>
                <div className="content">
                  <div className="welcome-header">
                    <div className="welcome-header-left">
                      <div className="welcome-avatar">
                        {user.profileImage ? (
                          <img src={user.profileImage} alt={user.name} />
                        ) : (
                          <div className="avatar-placeholder">
                            {user.name.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="welcome-user-info">
                        <h2>{t('welcomeBack')}, {user.name}!</h2>
                        <div className="user-role-indicator">
                          <span className="user-role-icon">
                            {user.userType === 'client' ? (
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                              </svg>
                            )}
                          </span>
                          <span className="user-role-text">You are logged in as a <strong>{user.userType === 'client' ? t('client') : t('translator')}</strong></span>
                        </div>
                      </div>
                    </div>
                    <div className="date-time">
                      {new Date().toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                    </div>
                  </div>

                  <div className="welcome-description">
                    {user.bio ? (
                      <div className="user-bio">
                        <p className="bio-text">{user.bio}</p>
                        <div className="bio-label">Your Bio</div>
                      </div>
                    ) : (
                      <p>{user.userType === 'client' ? t('clientDescription') : t('translatorDescription')}</p>
                    )}
                  </div>

                  {/* Dashboard summary metrics */}
                  <div className="dashboard-summary">
                    <div className="summary-metric" style={{"--i": 1}}>
                      <div className="metric-icon">
                        {user.userType === 'client' ? (
                          <DocumentIcon />
                        ) : (
                          <RequestsIcon />
                  )}
                </div>
                      <div className="metric-content">
                        <div className="metric-value">{stats.active}</div>
                        <div className="metric-label">
                          {user.userType === 'client' ? 'Active Requests' : 'Available Jobs'}
                        </div>
                      </div>
              </div>

                    <div className="summary-metric" style={{"--i": 2}}>
                      <div className="metric-icon">
                        <CheckIcon />
                      </div>
                      <div className="metric-content">
                        <div className="metric-value">{stats.completed}</div>
                        <div className="metric-label">
                          {user.userType === 'client' ? 'Completed' : 'Completed Jobs'}
                        </div>
                      </div>
                    </div>

                    <div className="summary-metric" style={{"--i": 3}}>
                      <div className="metric-icon">
                {user.userType === 'client' ? (
                          <WordsIcon />
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="12" y1="1" x2="12" y2="23"></line>
                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                          </svg>
                        )}
                      </div>
                      <div className="metric-content">
                        <div className="metric-value">
                          {user.userType === 'client' ? stats.totalWords : '$0'}
                        </div>
                        <div className="metric-label">
                          {user.userType === 'client' ? 'Total Words' : 'Earnings'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Primary action buttons */}
                  <div className="welcome-actions">
                    {user.userType === 'client' ? (
                    <button
                        className="action-button action-button-primary"
                      onClick={() => setActiveView('find-translator')}
                    >
                      <SearchIcon /> Find Translator
                    </button>
                    ) : (
                    <button
                      className="action-button action-button-primary"
                      onClick={() => setActiveView('requests')}
                    >
                        <RequestsIcon /> {t('viewTranslationRequests')}
                    </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Quick access widgets */}
              <div className="quick-access-widgets">
                <div className="widget-header">
                  <h3>Quick Access</h3>
                </div>

                <div className="widgets-container">
                  {user.userType === 'client' ? (
                    <>
                      <div className="quick-widget" onClick={() => setActiveView('find-translator')} style={{"--i": 1}}>
                        <div className="widget-icon">
                          <SearchIcon />
                        </div>
                        <div className="widget-title">Find Translator</div>
                        <div className="widget-description">Search for qualified translators</div>
                      </div>

                      <div className="quick-widget" onClick={() => setActiveView('price-calculator')} style={{"--i": 2}}>
                        <div className="widget-icon">
                          <CalculatorIcon />
                        </div>
                        <div className="widget-title">Calculate Price</div>
                        <div className="widget-description">Estimate translation costs</div>
                      </div>

                      <div className="quick-widget" onClick={() => setActiveView('translations')} style={{"--i": 3}}>
                        <div className="widget-icon">
                          <DocumentIcon />
                        </div>
                        <div className="widget-title">My Translations</div>
                        <div className="widget-description">View your translation projects</div>
                      </div>

                      <div className="quick-widget" onClick={() => setActiveView('profile')} style={{"--i": 4}}>
                        <div className="widget-icon">
                          <ProfileIcon />
                        </div>
                        <div className="widget-title">Profile</div>
                        <div className="widget-description">Manage your account</div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="quick-widget" onClick={() => setActiveView('requests')} style={{"--i": 1}}>
                        <div className="widget-icon">
                          <RequestsIcon />
                        </div>
                        <div className="widget-title">View Requests</div>
                        <div className="widget-description">Browse available translation jobs</div>
                      </div>

                      <div className="quick-widget" onClick={() => setActiveView('translations')} style={{"--i": 2}}>
                        <div className="widget-icon">
                          <DocumentIcon />
                        </div>
                        <div className="widget-title">My Translations</div>
                        <div className="widget-description">View your translation work</div>
                      </div>

                      <div className="quick-widget" onClick={() => setActiveView('messages')} style={{"--i": 3}}>
                        <div className="widget-icon">
                          <MessagesIcon />
                        </div>
                        <div className="widget-title">Messages</div>
                        <div className="widget-description">Communicate with clients</div>
                      </div>

                      <div className="quick-widget" onClick={() => setActiveView('profile')} style={{"--i": 4}}>
                        <div className="widget-icon">
                          <ProfileIcon />
                        </div>
                        <div className="widget-title">Profile</div>
                        <div className="widget-description">Manage your account</div>
                      </div>
                  </>
                )}
                </div>
              </div>

              <div className="dashboard-sections">
                {/* Profile and Verification Section */}
                <div className="dashboard-section">
                  <div className="section-header">
                    <h3>Profile & Verification</h3>
                    <button className="section-action" onClick={() => setActiveView('profile')}>
                      View Profile
                    </button>
                  </div>

                <div className="dashboard-card user-profile-card">
                    <div className="user-profile-header">
                      <div className="user-avatar">
                        {user.profileImage ? (
                          <img src={user.profileImage} alt={user.name} />
                        ) : (
                          <div className="avatar-placeholder">
                            {user.name.charAt(0).toUpperCase()}
                    </div>
                        )}
                  </div>
                      <div className="user-info">
                    <div className="user-profile-name">{user.name}</div>
                    <div className="user-profile-email">{user.email}</div>
                    <div className={`user-profile-type ${user.userType}`}>
                      <span className="user-profile-type-icon">
                        {user.userType === 'client' ? (
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                          </svg>
                        )}
                      </span>
                      <span className="user-profile-type-text">{user.userType === 'client' ? 'Client Account' : 'Translator Account'}</span>
                        </div>
                      </div>
                    </div>

                    <div className="profile-completion">
                      <div className="completion-header">
                        <span>Profile Completion</span>
                        <span className="completion-percentage">70%</span>
                      </div>
                      <div className="completion-bar">
                        <div className="completion-progress" style={{ width: '70%' }}></div>
                      </div>
                      <div className="completion-tips">
                        Complete your profile to increase visibility and trust.
                      </div>
                    </div>

                    <div className="user-profile-verification">
                      <div className="verification-header">
                      <span className="verification-status not-verified">
                        <VerificationIcon />
                        {t('notVerified')}
                      </span>
                      <button
                        className="verification-button"
                        onClick={() => setActiveView('verification')}
                      >
                        {t('verifyNow')}
                      </button>
                      </div>
                      <div className="verification-description">
                        Verify your identity to unlock all platform features and build trust with clients.
                      </div>
                    </div>

                    <div className="user-profile-actions">
                      <button className="user-profile-button" onClick={() => setActiveView('profile')}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                        {t('editProfile')}
                      </button>
                      <button className="user-profile-button" onClick={() => setActiveView('settings')}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="3"></circle>
                          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        Settings
                      </button>
                    </div>
                  </div>
                </div>

                {/* Stats Section */}
                <div className="dashboard-section">
                  <div className="section-header">
                    <h3>Statistics</h3>
                    <button className="section-action" onClick={() => setActiveView('translations')}>
                      View All
                    </button>
                  </div>

                  <div className="stats-cards">
                {user.userType === 'client' ? (
                  <>
                        <div className="stats-card" style={{"--i": 1}}>
                          <div className="stats-card-icon">
                          <DocumentIcon />
                        </div>
                          <div className="stats-card-content">
                            <div className="stats-card-title">{t('activeRequests')}</div>
                            <div className="stats-card-value">{stats.active}</div>
                            <div className="stats-card-description">
                        {stats.active === 0 ? 'No active translation requests' :
                         stats.active === 1 ? '1 translation request in progress' :
                         `${stats.active} translation requests in progress`}
                            </div>
                            <div className="stats-card-trend">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                <polyline points="17 6 23 6 23 12"></polyline>
                              </svg>
                              <span>+5% from last month</span>
                            </div>
                      </div>
                    </div>

                        <div className="stats-card" style={{"--i": 2}}>
                          <div className="stats-card-icon">
                          <CheckIcon />
                        </div>
                          <div className="stats-card-content">
                            <div className="stats-card-title">{t('completed')}</div>
                            <div className="stats-card-value">{stats.completed}</div>
                            <div className="stats-card-description">
                        {stats.completed === 0 ? 'No completed translations' :
                         stats.completed === 1 ? '1 translation completed' :
                         `${stats.completed} translations completed`}
                            </div>
                            <div className="stats-card-trend">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                                <polyline points="17 18 23 18 23 12"></polyline>
                              </svg>
                              <span>+12% from last month</span>
                            </div>
                      </div>
                    </div>

                        <div className="stats-card" style={{"--i": 3}}>
                          <div className="stats-card-icon">
                          <WordsIcon />
                        </div>
                          <div className="stats-card-content">
                            <div className="stats-card-title">{t('totalWords')}</div>
                            <div className="stats-card-value">{stats.totalWords}</div>
                            <div className="stats-card-description">
                        {stats.totalWords === 0 ? 'No words translated yet' :
                         stats.totalWords === 1 ? '1 word translated' :
                         `${stats.totalWords} words translated`}
                      </div>
                      {stats.totalWords > 0 && (
                        <div className="progress-container">
                                <div className="progress-label">
                                  <span>Progress to next tier</span>
                                  <span>{Math.min(100, Math.floor((stats.totalWords / 1000) * 100))}%</span>
                                </div>
                                <div className="progress-bar-container">
                          <div
                            className="progress-bar"
                            style={{ width: `${Math.min(100, (stats.totalWords / 1000) * 100)}%` }}
                          ></div>
                                </div>
                        </div>
                      )}
                          </div>
                    </div>
                  </>
                ) : (
                  <>
                        <div className="stats-card" style={{"--i": 1}}>
                          <div className="stats-card-icon">
                          <RequestsIcon />
                        </div>
                          <div className="stats-card-content">
                            <div className="stats-card-title">{t('availableRequests')}</div>
                            <div className="stats-card-value">{stats.active}</div>
                            <div className="stats-card-description">
                        {stats.active === 0 ? 'No available translation requests' :
                         stats.active === 1 ? '1 translation request available' :
                         `${stats.active} translation requests available`}
                            </div>
                            <div className="stats-card-trend">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                <polyline points="17 6 23 6 23 12"></polyline>
                              </svg>
                              <span>+8% from last week</span>
                            </div>
                      </div>
                    </div>

                        <div className="stats-card" style={{"--i": 2}}>
                          <div className="stats-card-icon">
                          <CheckIcon />
                        </div>
                          <div className="stats-card-content">
                            <div className="stats-card-title">{t('completedJobs')}</div>
                            <div className="stats-card-value">{stats.completed}</div>
                            <div className="stats-card-description">
                        {stats.completed === 0 ? 'No completed jobs' :
                         stats.completed === 1 ? '1 job completed' :
                         `${stats.completed} jobs completed`}
                            </div>
                            <div className="stats-card-trend">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                <polyline points="17 6 23 6 23 12"></polyline>
                              </svg>
                              <span>+15% from last month</span>
                            </div>
                      </div>
                    </div>

                        <div className="stats-card" style={{"--i": 3}}>
                          <div className="stats-card-icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="12" y1="1" x2="12" y2="23"></line>
                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                          </svg>
                        </div>
                          <div className="stats-card-content">
                            <div className="stats-card-title">{t('earnings')}</div>
                            <div className="stats-card-value">$0</div>
                            <div className="stats-card-description">Total earnings from completed translations</div>
                            <div className="stats-card-trend">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                <polyline points="17 6 23 6 23 12"></polyline>
                              </svg>
                              <span>+0% from last month</span>
                      </div>
                          </div>
                    </div>
                  </>
                )}
                  </div>
                </div>
              </div>

              {/* Translator Chart - Only show for translators */}
              {user.userType === 'translator' && (
                <div className="dashboard-section">
                  <div className="section-header">
                    <h3>Translation Analytics</h3>
                    <div className="chart-period-selector">
                      <button
                        className={`chart-period-btn ${activePeriod === 'week' ? 'active' : ''}`}
                        onClick={() => updateChartData('week')}
                      >
                        Week
                      </button>
                      <button
                        className={`chart-period-btn ${activePeriod === 'month' ? 'active' : ''}`}
                        onClick={() => updateChartData('month')}
                      >
                        Month
                      </button>
                      <button
                        className={`chart-period-btn ${activePeriod === 'year' ? 'active' : ''}`}
                        onClick={() => updateChartData('year')}
                      >
                        Year
                      </button>
                    </div>
                  </div>

                  <div className="analytics-card">
                    <div className="chart-container">
                  <TranslatorChart data={chartData} />
                    </div>

                  <div className="chart-summary">
                    <div className="chart-summary-item">
                        <div className="summary-icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                          </svg>
                    </div>
                        <div className="summary-content">
                          <div className="summary-value">{chartData.tasks.reduce((a, b) => a + b, 0)}</div>
                          <div className="summary-label">Total Tasks</div>
                        </div>
                      </div>

                    <div className="chart-summary-item">
                        <div className="summary-icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                            <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                          </svg>
                    </div>
                        <div className="summary-content">
                          <div className="summary-value">{chartData.pages.reduce((a, b) => a + b, 0)}</div>
                          <div className="summary-label">Total Pages</div>
                        </div>
                      </div>

                    <div className="chart-summary-item">
                        <div className="summary-icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                          </svg>
                        </div>
                        <div className="summary-content">
                          <div className="summary-value">
                        {(chartData.pages.reduce((a, b) => a + b, 0) / chartData.tasks.reduce((a, b) => a + b, 0)).toFixed(1)}
                          </div>
                          <div className="summary-label">Avg. Pages per Task</div>
                        </div>
                      </div>

                      <div className="chart-summary-item">
                        <div className="summary-icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                          </svg>
                        </div>
                        <div className="summary-content">
                          <div className="summary-value">98%</div>
                          <div className="summary-label">Completion Rate</div>
                        </div>
                      </div>
                    </div>

                    <div className="chart-insights">
                      <div className="insights-header">
                        <h4>Insights</h4>
                      </div>
                      <div className="insights-content">
                        <div className="insight-item">
                          <div className="insight-icon positive">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                              <polyline points="17 6 23 6 23 12"></polyline>
                            </svg>
                          </div>
                          <div className="insight-text">Your task completion rate has increased by 15% compared to last month.</div>
                        </div>

                        <div className="insight-item">
                          <div className="insight-icon neutral">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                          </div>
                          <div className="insight-text">Your average pages per task remains steady at {(chartData.pages.reduce((a, b) => a + b, 0) / chartData.tasks.reduce((a, b) => a + b, 0)).toFixed(1)} pages.</div>
                        </div>

                        <div className="insight-item">
                          <div className="insight-icon tip">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <line x1="12" y1="16" x2="12" y2="12"></line>
                              <line x1="12" y1="8" x2="12.01" y2="8"></line>
                            </svg>
                          </div>
                          <div className="insight-text">Tip: Completing tasks faster can increase your visibility in search results and lead to more job opportunities.</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Activity Timeline Section */}
              <div className="dashboard-section">
                <div className="section-header">
                  <h3>Recent Activity</h3>
                  <button className="section-action" onClick={() => setActiveView('translations')}>
                    View All
                  </button>
                </div>

                <div className="activity-timeline">
                {loading ? (
                    <div className="loading">
                      <div className="loading-spinner"></div>
                      <p>Loading activity...</p>
                    </div>
                ) : translationItems.length > 0 ? (
                    <>
                    {translationItems.slice(0, 5).map(translation => (
                      <div key={translation.id} className="activity-item">
                          <div className={`activity-icon ${translation.status}`}>
                            {translation.status === 'completed' ? (
                              <CheckIcon />
                            ) : translation.status === 'in-progress' ? (
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                              </svg>
                            ) : (
                              <DocumentIcon />
                            )}
                        </div>
                        <div className="activity-content">
                            <div className="activity-header">
                          <div className="activity-title">
                            Translation from {translation.sourceLanguage.toUpperCase()} to {translation.targetLanguage.toUpperCase()}
                          </div>
                          <div className="activity-time">{new Date(translation.createdAt).toLocaleString()}</div>
                            </div>
                            <div className="activity-description">
                              <span className={`activity-status status-${translation.status}`}>{translation.status}</span>
                              <span className="activity-words">{translation.wordCount} words</span>
                            </div>
                            <div className="activity-text">
                              {translation.originalText.substring(0, 80)}...
                            </div>
                            <div className="activity-actions">
                              <button
                                className="activity-action-btn"
                                onClick={() => {
                                  // View translation details
                                  console.log(`View translation ${translation.id}`);
                                }}
                              >
                                View Details
                              </button>
                              {translation.status === 'completed' && (
                                <button
                                  className="activity-action-btn"
                                  onClick={() => {
                                    // Download translation
                                    console.log(`Download translation ${translation.id}`);
                                  }}
                                >
                                  Download
                                </button>
                              )}
                            </div>
                        </div>
                      </div>
                    ))}
                      <div className="view-more-container">
                    <button
                          className="view-more-btn"
                      onClick={() => setActiveView('translations')}
                    >
                      View All Translations
                    </button>
                  </div>
                    </>
                  ) : (
                    <div className="empty-activity">
                      <div className="empty-activity-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                          <circle cx="9" cy="7" r="4"></circle>
                          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                  </div>
                      <h3>No Recent Activity</h3>
                      <p>Your recent activities will appear here once you start using the platform.</p>
                      {user.userType === 'client' ? (
                        <button
                          className="start-action-btn"
                          onClick={() => setActiveView('find-translator')}
                        >
                          Find a Translator
                        </button>
                      ) : (
                        <button
                          className="start-action-btn"
                          onClick={() => setActiveView('requests')}
                        >
                          Browse Translation Requests
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {activeView === 'translations' && (
            <>
              <h2>{user.userType === 'client' ? 'My Translations' : 'My Translation Work'}</h2>

              {error && <div className="error-message">{error}</div>}

              {loading ? (
                <div className="loading">Loading...</div>
              ) : translationItems.length > 0 ? (
                <div className="translations-list full-list">
                  {translationItems.map(translation => (
                    <div key={translation.id} className="translation-item">
                      <div className="translation-header">
                        <span className="translation-languages">{translation.sourceLanguage} → {translation.targetLanguage}</span>
                        <span className={`translation-status status-${translation.status}`}>{translation.status}</span>
                      </div>
                      <div className="translation-text">{translation.originalText.substring(0, 100)}...</div>
                      <div className="translation-meta">
                        <span>{translation.wordCount} words</span>
                        <span>{new Date(translation.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <p>You don't have any translations yet.</p>
                </div>
              )}
            </>
          )}



          {activeView === 'verification' && (
            <div className="verification-container-wrapper">
              <h2>{t('verification')}</h2>
              <VerificationPage user={user} />
            </div>
          )}

          {activeView === 'find-translator' && (
            <>
              <h2>Find a Translator</h2>

              <div className="find-translator-container">
                {/* Search bar at the top */}
                <div className="search-bar">
                  <div className="search-input-container">
                    <SearchIcon />
                    <input
                      type="text"
                      placeholder="Search by name, language, or specialty..."
                      className="search-input"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    {searchQuery && (
                      <button
                        className="clear-search"
                        onClick={() => setSearchQuery('')}
                        title="Clear search"
                      >
                        ×
                      </button>
                    )}
                  </div>
                </div>

                <div className="search-filters">
                  <div className="filter-group">
                    <label>Language Pair</label>
                    <div className="language-pair-selector">
                      <select
                        className="language-select"
                        value={sourceLang}
                        onChange={(e) => setSourceLang(e.target.value)}
                      >
                        <option value="">Source Language</option>
                        <option value="English">English</option>
                        <option value="French">French</option>
                        <option value="Spanish">Spanish</option>
                        <option value="German">German</option>
                        <option value="Italian">Italian</option>
                        <option value="Arabic">Arabic</option>
                      </select>
                      <div className="language-arrow">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                          <polyline points="12 5 19 12 12 19"></polyline>
                        </svg>
                      </div>
                      <select
                        className="language-select"
                        value={targetLang}
                        onChange={(e) => setTargetLang(e.target.value)}
                      >
                        <option value="">Target Language</option>
                        <option value="English">English</option>
                        <option value="French">French</option>
                        <option value="Spanish">Spanish</option>
                        <option value="German">German</option>
                        <option value="Italian">Italian</option>
                        <option value="Arabic">Arabic</option>
                      </select>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Specialization</label>
                    <select
                      className="filter-select"
                      value={specialty}
                      onChange={(e) => setSpecialty(e.target.value)}
                    >
                      <option value="">All Specializations</option>
                      <option value="Legal">Legal</option>
                      <option value="Medical">Medical</option>
                      <option value="Technical">Technical</option>
                      <option value="Financial">Financial</option>
                      <option value="Marketing">Marketing</option>
                      <option value="Literary">Literary</option>
                      <option value="IT">IT</option>
                      <option value="Business">Business</option>
                    </select>
                  </div>

                  <div className="filter-group">
                    <label>Rating</label>
                    <div className="rating-filter">
                      <label className="rating-option">
                        <input
                          type="radio"
                          name="rating"
                          value="all"
                          checked={minRating === 'all'}
                          onChange={() => setMinRating('all')}
                        />
                        <span>All</span>
                      </label>
                      <label className="rating-option">
                        <input
                          type="radio"
                          name="rating"
                          value="4"
                          checked={minRating === '4'}
                          onChange={() => setMinRating('4')}
                        />
                        <span>4+ Stars</span>
                      </label>
                      <label className="rating-option">
                        <input
                          type="radio"
                          name="rating"
                          value="5"
                          checked={minRating === '5'}
                          onChange={() => setMinRating('5')}
                        />
                        <span>5 Stars</span>
                      </label>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Price Range ($ per word)</label>
                    <div className="price-range-slider">
                      <input
                        type="range"
                        min="0"
                        max="0.25"
                        step="0.01"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([0, parseFloat(e.target.value)])}
                        className="price-slider"
                      />
                      <div className="price-range-values">
                        <span>$0.00</span>
                        <span>${priceRange[1].toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="filter-actions">
                    <div className="view-toggle">
                      <button
                        className={`view-toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
                        onClick={() => setViewMode('grid')}
                        title="Grid view"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="3" y="3" width="7" height="7"></rect>
                          <rect x="14" y="3" width="7" height="7"></rect>
                          <rect x="14" y="14" width="7" height="7"></rect>
                          <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                    </button>
                      <button
                        className={`view-toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
                        onClick={() => setViewMode('list')}
                        title="List view"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <line x1="8" y1="6" x2="21" y2="6"></line>
                          <line x1="8" y1="12" x2="21" y2="12"></line>
                          <line x1="8" y1="18" x2="21" y2="18"></line>
                          <line x1="3" y1="6" x2="3.01" y2="6"></line>
                          <line x1="3" y1="12" x2="3.01" y2="12"></line>
                          <line x1="3" y1="18" x2="3.01" y2="18"></line>
                        </svg>
                      </button>
                    </div>

                    <div className="sort-dropdown">
                      <label>Sort by:</label>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="sort-select"
                      >
                        <option value="rating">Highest Rating</option>
                        <option value="price">Lowest Price</option>
                        <option value="projects">Most Projects</option>
                      </select>
                    </div>

                    <button
                      className="refresh-button"
                      onClick={() => {
                        setRefreshTranslators(prev => prev + 1);
                        notify('Refreshing translator list...', 'info', 2000);
                      }}
                      title="Refresh translator list"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M23 4v6h-6"></path>
                        <path d="M1 20v-6h6"></path>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                      </svg>
                      Refresh
                    </button>
                  </div>
                </div>

                {/* Active filters display */}
                {(sourceLang || targetLang || specialty || minRating !== 'all' || searchQuery) && (
                  <div className="active-filters">
                    <span className="active-filters-label">Active filters:</span>
                    <div className="filter-tags">
                      {sourceLang && (
                        <div className="filter-tag">
                          <span>Source: {sourceLang}</span>
                          <button onClick={() => setSourceLang('')}>×</button>
                        </div>
                      )}
                      {targetLang && (
                        <div className="filter-tag">
                          <span>Target: {targetLang}</span>
                          <button onClick={() => setTargetLang('')}>×</button>
                        </div>
                      )}
                      {specialty && (
                        <div className="filter-tag">
                          <span>Specialty: {specialty}</span>
                          <button onClick={() => setSpecialty('')}>×</button>
                        </div>
                      )}
                      {minRating !== 'all' && (
                        <div className="filter-tag">
                          <span>Rating: {minRating}+ stars</span>
                          <button onClick={() => setMinRating('all')}>×</button>
                        </div>
                      )}
                      {searchQuery && (
                        <div className="filter-tag">
                          <span>Search: {searchQuery}</span>
                          <button onClick={() => setSearchQuery('')}>×</button>
                        </div>
                      )}
                      <button
                        className="clear-all-filters"
                        onClick={() => {
                          setSourceLang('');
                          setTargetLang('');
                          setSpecialty('');
                          setMinRating('all');
                          setSearchQuery('');
                          setPriceRange([0, 0.25]);
                        }}
                      >
                        Clear all
                      </button>
                    </div>
                  </div>
                )}

                <div className={`translator-results ${viewMode === 'list' ? 'list-view' : 'grid-view'}`}>
                  {error && (
                    <div className="error-message">
                      <div className="error-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                      </div>
                      <p>{error}</p>
                    </div>
                  )}
                  {loading ? (
                    <div className="loading">
                      <div className="loading-spinner"></div>
                      <p>Loading translators...</p>
                    </div>
                  ) : !error && filteredTranslators && filteredTranslators.length > 0 ? (
                    filteredTranslators.map(translator => (
                      <div key={translator.id} className="translator-card">
                        <div className="translator-header">
                          <div className="translator-avatar">
                            {translator.profileImage ? (
                              <img src={translator.profileImage} alt={translator.name} />
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                              </svg>
                            )}
                          </div>
                          <div className="translator-info">
                            <h3>{translator.name}</h3>
                            <div className="translator-languages">
                              {translator.languages ? translator.languages.join(' • ') : 'English'}
                            </div>
                            <div className="translator-rating">
                              <span className="stars" title={`${translator.rating || 0} out of 5 stars`}>
                                {'★'.repeat(Math.floor(translator.rating || 0))}
                                {'☆'.repeat(5 - Math.floor(translator.rating || 0))}
                              </span>
                              <span className="rating-count">({translator.reviewCount || 0} reviews)</span>
                            </div>
                          </div>
                          <div className="translator-verified-badge" title="Verified Translator">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                          </div>
                        </div>
                        <div className="translator-specialties">
                          {translator.specialties && translator.specialties.map((specialty, index) => (
                            <span
                              key={index}
                              className="specialty-tag"
                              onClick={() => setSpecialty(specialty)}
                            >
                              {specialty}
                            </span>
                          ))}
                          {(!translator.specialties || translator.specialties.length === 0) && (
                            <span className="specialty-tag">General</span>
                          )}
                        </div>
                        <div className="translator-description">
                          {translator.description || `${translator.name} is a professional translator registered on our platform.`}
                        </div>
                        <div className="translator-stats">
                          <div className="stat">
                            <div className="stat-value">{translator.completedProjects || 0}</div>
                            <div className="stat-label">Projects</div>
                          </div>
                          <div className="stat">
                            <div className="stat-value">{translator.onTimePercentage || 100}%</div>
                            <div className="stat-label">On Time</div>
                          </div>
                          <div className="stat">
                            <div className="stat-value">${typeof translator.ratePerWord === 'number' ? translator.ratePerWord.toFixed(2) : (translator.ratePerWord || '0.10')}</div>
                            <div className="stat-label">Per Word</div>
                          </div>
                        </div>
                        <div className="translator-actions">
                          <button
                            className="action-button action-button-primary"
                            onClick={() => window.location.href = '/apply-translation'}
                          >
                            Contact
                          </button>
                          <button className="action-button">View Profile</button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="empty-state">
                      <div className="empty-state-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="11" cy="11" r="8"></circle>
                          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                      </div>
                      {(sourceLang || targetLang || specialty || minRating !== 'all' || searchQuery) ? (
                        <>
                          <h3>No translators match your filters</h3>
                          <p>Try adjusting your search criteria or clearing some filters.</p>
                          <button
                            className="clear-filters-btn"
                            onClick={() => {
                              setSourceLang('');
                              setTargetLang('');
                              setSpecialty('');
                              setMinRating('all');
                              setSearchQuery('');
                              setPriceRange([0, 0.25]);
                            }}
                          >
                            Clear all filters
                          </button>
                        </>
                      ) : (
                        <>
                      <h3>No registered translators found</h3>
                      <p>There are currently no registered translators in the system. New translator accounts will appear here automatically when they register.</p>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {activeView === 'price-calculator' && (
            <>
              <h2>Translation Price Calculator</h2>
              <div className="calculator-container modern-calculator">
                <form className="calculator-form" onSubmit={handleCalculatePrice}>
                  <div className="form-group">
                    <label>Source Language</label>
                    <select className="form-control" value={calcSourceLang} onChange={e => setCalcSourceLang(e.target.value)}>
                      <option value="">Select source language</option>
                      <option value="en">English</option>
                      <option value="fr">French</option>
                      <option value="es">Spanish</option>
                      <option value="de">German</option>
                      <option value="it">Italian</option>
                      <option value="ar">Arabic</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Target Language</label>
                    <select className="form-control" value={calcTargetLang} onChange={e => setCalcTargetLang(e.target.value)}>
                      <option value="">Select target language</option>
                      <option value="en">English</option>
                      <option value="fr">French</option>
                      <option value="es">Spanish</option>
                      <option value="de">German</option>
                      <option value="it">Italian</option>
                      <option value="ar">Arabic</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Document Type</label>
                    <select className="form-control" value={calcDocType} onChange={e => setCalcDocType(e.target.value)}>
                      <option value="general">General</option>
                      <option value="legal">Legal</option>
                      <option value="medical">Medical</option>
                      <option value="technical">Technical</option>
                      <option value="financial">Financial</option>
                      <option value="marketing">Marketing</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Number of Pages</label>
                    <input type="number" className="form-control" min="1" value={calcPages} onChange={e => setCalcPages(Number(e.target.value))} />
                  </div>
                  <div className="form-group">
                    <label>Price per Page (TND)</label>
                    <input type="number" className="form-control" min="15" value={calcPricePerPage} onChange={e => setCalcPricePerPage(Number(e.target.value))} />
                    <small className="form-hint">Minimum 15 TND per page. You can increase for premium service.</small>
                  </div>
                  <div className="form-group">
                    <label>Delivery Time</label>
                    <select className="form-control" value={calcDelivery} onChange={e => setCalcDelivery(e.target.value)}>
                      <option value="standard">Standard (3-5 days)</option>
                      <option value="express">Express (1-2 days, +15%)</option>
                      <option value="urgent">Urgent (24 hours, +30%)</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Additional Services</label>
                    <div className="checkbox-group">
                      <label className="checkbox-label">
                        <input type="checkbox" checked={calcServices.proofreading} onChange={() => handleCalcServiceChange('proofreading')} /> Proofreading by second translator (+10%)
                      </label>
                      <label className="checkbox-label">
                        <input type="checkbox" checked={calcServices.formatting} onChange={() => handleCalcServiceChange('formatting')} /> Formatting and layout matching (+8%)
                      </label>
                      <label className="checkbox-label">
                        <input type="checkbox" checked={calcServices.certified} onChange={() => handleCalcServiceChange('certified')} /> Certified translation (+20%)
                      </label>
                    </div>
                  </div>
                  {calcError && <div className="verification-error" style={{marginBottom:8}}>{calcError}</div>}
                  <button className="calculate-button action-button action-button-primary" type="submit">
                    <CalculatorIcon /> Calculate Price
                  </button>
                </form>
                <div className="price-result">
                  <div className="price-card modern-price-card">
                    <h3>Estimated Price</h3>
                    <div className="price-value" style={{fontSize:'2.2rem',color:'#009688',fontWeight:700}}>
                      {calcResult ? `${calcResult.total.toFixed(2)} TND` : '--'}
                    </div>
                    <div className="price-breakdown">
                      <div className="breakdown-item">
                        <span>Base translation:</span>
                        <span>{calcResult ? `${calcResult.base.toFixed(2)} TND` : '--'}</span>
                      </div>
                      <div className="breakdown-item">
                        <span>Delivery markup:</span>
                        <span>{calcResult ? `${calcResult.deliveryMarkup.toFixed(2)} TND` : '--'}</span>
                      </div>
                      <div className="breakdown-item">
                        <span>Additional services:</span>
                        <span>{calcResult ? `${calcResult.servicesMarkup.toFixed(2)} TND` : '--'}</span>
                      </div>
                    </div>
                    <div className="price-info">
                      <p>Enter your translation details to get an estimate. Minimum price per page is 15 TND.</p>
                    </div>
                    <div className="price-actions">
                      <button className="action-button" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Save Quote as PDF
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {activeView === 'requests' && (
            <>
              <h2>{t('translationRequests')}</h2>

              <div className="requests-container">
                <div className="requests-filters">
                  <div className="filter-tabs">
                    <button className="filter-tab active">Available</button>
                    <button className="filter-tab">In Progress</button>
                    <button className="filter-tab">Completed</button>
                  </div>

                  <div className="filter-options">
                    <select className="filter-select">
                      <option value="all">All Languages</option>
                      <option value="en-fr">English to French</option>
                      <option value="en-es">English to Spanish</option>
                      <option value="fr-en">French to English</option>
                      <option value="es-en">Spanish to English</option>
                      <option value="ar-en">Arabic to English</option>
                    </select>

                    <select className="filter-select">
                      <option value="all">All Categories</option>
                      <option value="legal">Legal</option>
                      <option value="medical">Medical</option>
                      <option value="technical">Technical</option>
                      <option value="financial">Financial</option>
                      <option value="marketing">Marketing</option>
                    </select>

                    <select className="filter-select">
                      <option value="all">All Deadlines</option>
                      <option value="urgent">Urgent (24h)</option>
                      <option value="3days">3 Days</option>
                      <option value="week">1 Week</option>
                      <option value="twoweeks">2+ Weeks</option>
                    </select>
                  </div>
                </div>

                <div className="requests-list">
                  <div className="empty-requests">
                    <div className="empty-state-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                      </svg>
                    </div>
                    <h3>No translation requests available</h3>
                    <p>There are currently no translation requests matching your filters.</p>
                    <p>Try adjusting your filter settings or check back later.</p>
                  </div>
                </div>
              </div>
            </>
          )}

          {activeView === 'messages' && (
            <>
              <h2>{t('clientMessages')}</h2>

              <div className="messages-container">
                <div className="messages-sidebar">
                  <div className="messages-search">
                    <input type="text" placeholder="Search messages..." className="search-input" />
                  </div>

                  <div className="conversation-list">
                    <div className="empty-conversations">
                      <div className="empty-state-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                      </div>
                      <h3>No messages yet</h3>
                      <p>Your message history will appear here.</p>
                      <p>Start a conversation with a translator or client.</p>
                    </div>
                  </div>
                </div>

                <div className="messages-content">
                  <div className="messages-header">
                    <div className="contact-info">
                      <div className="contact-name">No conversation selected</div>
                      <div className="contact-status">-</div>
                    </div>
                    <div className="messages-actions">
                      <button className="message-action-button" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14"></path>
                          <circle cx="8" cy="9" r="2"></circle>
                          <rect x="3" y="3" width="18" height="18" rx="2"></rect>
                        </svg>
                      </button>
                      <button className="message-action-button" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M14.5 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V7.5L14.5 2z"></path>
                          <polyline points="14 2 14 8 20 8"></polyline>
                        </svg>
                      </button>
                      <button className="message-action-button" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="19" cy="12" r="1"></circle>
                          <circle cx="5" cy="12" r="1"></circle>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <div className="messages-body">
                    <div className="empty-messages">
                      <div className="empty-state-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                      </div>
                      <h3>Select a conversation</h3>
                      <p>Choose a conversation from the list or start a new one.</p>
                    </div>
                  </div>

                  <div className="messages-footer">
                    <button className="message-action-button">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                        <line x1="9" y1="9" x2="9.01" y2="9"></line>
                        <line x1="15" y1="9" x2="15.01" y2="9"></line>
                      </svg>
                    </button>
                    <button className="message-action-button">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      </svg>
                    </button>
                    <input type="text" placeholder="Type a message..." className="message-input" />
                    <button className="send-button">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}

          {activeView === 'profile' && !isEditingProfile && (
            <>
              <h2>{t('yourProfile')}</h2>

              {profileUpdateSuccess && (
                <div className="success-message">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Profile updated successfully!</span>
                </div>
              )}

              <div className="profile-container">
                <div className="profile-header">
                  <div className="profile-avatar" style={user?.profileImage ? {backgroundImage: `url(${user.profileImage})`, backgroundSize: 'cover'} : {}}>
                    {!user?.profileImage && <ProfileIcon />}
                  </div>
                  <div className="profile-title">
                    <h3>{user?.name || 'User'}</h3>
                    <div className="profile-subtitle">
                      <span className="profile-type-badge">{user?.userType === 'client' ? t('client') : t('translator')}</span>
                      <span className="profile-email">{user?.email || '<EMAIL>'}</span>
                    </div>
                  </div>
                </div>

                <div className="profile-section">
                  <h3>{t('accountInformation')}</h3>
                  <div className="profile-info-grid">
                    <div className="profile-info-item">
                      <div className="info-label">{t('name')}</div>
                      <div className="info-value">{user?.name || 'User'}</div>
                    </div>
                    <div className="profile-info-item">
                      <div className="info-label">{t('email')}</div>
                      <div className="info-value">{user?.email || '<EMAIL>'}</div>
                    </div>
                    <div className="profile-info-item">
                      <div className="info-label">{t('accountType')}</div>
                      <div className="info-value">{user?.userType === 'client' ? t('client') : t('translator')}</div>
                    </div>
                    {user?.bio && (
                      <div className="profile-info-item">
                        <div className="info-label">{t('bio')}</div>
                        <div className="info-value">{user.bio}</div>
                      </div>
                    )}
                    {user?.phone && (
                      <div className="profile-info-item">
                        <div className="info-label">{t('phone')}</div>
                        <div className="info-value">{user.phone}</div>
                      </div>
                    )}
                    {user?.location && (
                      <div className="profile-info-item">
                        <div className="info-label">{t('location')}</div>
                        <div className="info-value">{user.location}</div>
                      </div>
                    )}
                    <div className="profile-info-item">
                      <div className="info-label">{t('language')}</div>
                      <div className="info-value">
                        {currentLanguage === 'en' && t('english')}
                        {currentLanguage === 'fr' && t('french')}
                        {currentLanguage === 'es' && t('spanish')}
                        {currentLanguage === 'ar' && t('arabic')}
                      </div>
                    </div>
                  </div>
                </div>

                {user && user.userType === 'translator' && (
                  <div className="profile-section">
                    <h3>Translator Information</h3>
                    <div className="profile-info-grid">
                      <div className="profile-info-item">
                        <div className="info-label">Specializations</div>
                        <div className="info-value">
                          <div className="specialization-tags">
                            {user.specializations && user.specializations.length > 0 ? (
                              user.specializations.map((spec, index) => (
                                <span key={index} className="specialization-tag">{spec}</span>
                              ))
                            ) : (
                              <span className="no-data">No specializations added yet</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="profile-info-item">
                        <div className="info-label">Languages</div>
                        <div className="info-value">
                          <div className="language-pairs">
                            {user.languages && user.languages.length > 0 ? (
                              user.languages.map((lang, index) => {
                                const langName = {
                                  en: 'English',
                                  fr: 'French',
                                  es: 'Spanish',
                                  ar: 'Arabic',
                                  de: 'German',
                                  it: 'Italian',
                                  pt: 'Portuguese',
                                  ru: 'Russian',
                                  zh: 'Chinese'
                                }[lang] || lang;
                                return (
                                  <span key={index} className="language-pair">{langName}</span>
                                );
                              })
                            ) : (
                              <span className="no-data">No languages added yet</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="profile-section">
                  <h3>Account Statistics</h3>
                  <div className="profile-stats-grid">
                    <div className="profile-stat-card">
                      <div className="stat-icon">
                        <DocumentIcon />
                      </div>
                      <div className="stat-content">
                        <div className="stat-value">{stats.active || 0}</div>
                        <div className="stat-label">{user?.userType === 'client' ? t('activeRequests') : 'Active Jobs'}</div>
                      </div>
                    </div>
                    <div className="profile-stat-card">
                      <div className="stat-icon">
                        <CheckIcon />
                      </div>
                      <div className="stat-content">
                        <div className="stat-value">{stats.completed || 0}</div>
                        <div className="stat-label">{t('completed')}</div>
                      </div>
                    </div>
                    <div className="profile-stat-card">
                      <div className="stat-icon">
                        <WordsIcon />
                      </div>
                      <div className="stat-content">
                        <div className="stat-value">{stats.totalWords || 0}</div>
                        <div className="stat-label">{t('totalWords')}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="profile-actions">
                  <button className="profile-edit-button" onClick={() => setIsEditingProfile(true)}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                    {t('editProfile')}
                  </button>
                </div>
              </div>
            </>
          )}

          {activeView === 'profile' && isEditingProfile && (
            <EditProfile
              user={user}
              onSave={handleSaveProfile}
              onCancel={handleCancelEditProfile}
              translations={translations}
            />
          )}

          {activeView === 'settings' && (
            <>
              <h2>{t('settings')}</h2>

              <div className="settings-section">
                <h3>{t('accountInformation')}</h3>
                <div className="user-info">
                  <div className="info-row">
                    <span className="info-label">{t('name')}:</span>
                    <span className="info-value">{user.name}</span>
                  </div>
                  <div className="info-row">
                    <span className="info-label">{t('email')}:</span>
                    <span className="info-value">{user.email}</span>
                  </div>
                  <div className="info-row">
                    <span className="info-label">{t('accountType')}:</span>
                    <span className="info-value">{user.userType === 'client' ? t('client') : t('translator')}</span>
                  </div>
                </div>
              </div>

              <div className="settings-section">
                <h3>{t('personalization')}</h3>

                <div className="personalization-options">
                  {/* Language Settings */}
                  <div className="personalization-group">
                    <h4>{t('language')}</h4>
                    <div className="language-options">
                      <button
                        className={`language-option ${currentLanguage === 'en' ? 'active' : ''}`}
                        onClick={() => changeLanguage('en')}
                      >
                        <span className="language-flag">🇺🇸</span>
                        <span className="language-name">{t('english')}</span>
                      </button>

                      <button
                        className={`language-option ${currentLanguage === 'fr' ? 'active' : ''}`}
                        onClick={() => changeLanguage('fr')}
                      >
                        <span className="language-flag">🇫🇷</span>
                        <span className="language-name">{t('french')}</span>
                      </button>

                      <button
                        className={`language-option ${currentLanguage === 'es' ? 'active' : ''}`}
                        onClick={() => changeLanguage('es')}
                      >
                        <span className="language-flag">🇪🇸</span>
                        <span className="language-name">{t('spanish')}</span>
                      </button>

                      <button
                        className={`language-option ${currentLanguage === 'ar' ? 'active' : ''}`}
                        onClick={() => changeLanguage('ar')}
                      >
                        <span className="language-flag">🇸🇦</span>
                        <span className="language-name">{t('arabic')}</span>
                      </button>
                    </div>
                  </div>

                  {/* Theme Settings */}
                  <div className="personalization-group">
                    <h4>{t('theme')}</h4>
                    <div className="theme-options">
                      <button
                        className={`theme-option ${!isDarkMode ? 'active' : ''}`}
                        onClick={() => toggleTheme()}
                      >
                        <span className="theme-icon">☀️</span>
                        <span className="theme-name">{t('lightTheme')}</span>
                      </button>

                      <button
                        className={`theme-option ${isDarkMode ? 'active' : ''}`}
                        onClick={() => toggleTheme()}
                      >
                        <span className="theme-icon">🌙</span>
                        <span className="theme-name">{t('darkTheme')}</span>
                      </button>

                      <button className="advanced-settings-button" onClick={openSettingsModal}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="3"></circle>
                          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        {t('advancedAppearanceSettings')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="settings-section">
                <h3>{t('notifications')}</h3>
                <div className="notification-options">
                  <label className="notification-option">
                    <input type="checkbox" defaultChecked />
                    <span>{t('emailNotifications')}</span>
                  </label>

                  <label className="notification-option">
                    <input type="checkbox" defaultChecked />
                    <span>{t('browserNotifications')}</span>
                  </label>
                </div>
              </div>

              <div className="settings-section">
                <h3>{t('security')}</h3>
                <button className="change-password-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  Change Password
                </button>
              </div>

              <div className="settings-actions">
                <button className="save-settings-btn">{t('saveChanges')}</button>
              </div>
            </>
          )}
        </main>
      </div>
    </div>
  );
};

export default Dashboard;

