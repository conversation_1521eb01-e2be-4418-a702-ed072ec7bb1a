.banner-customizer {
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.customizer-section {
  margin-bottom: 1.5rem;
}

.customizer-section h4 {
  margin-bottom: 0.75rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.style-options,
.pattern-options,
.animation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.style-option,
.pattern-option,
.animation-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.style-option:hover,
.pattern-option:hover,
.animation-option:hover {
  background-color: var(--bg-hover);
  transform: translateY(-2px);
}

.style-option.active,
.pattern-option.active,
.animation-option.active {
  background-color: var(--accent-color-light);
  border-color: var(--accent-color);
  color: var(--accent-color);
  font-weight: 500;
}

.style-option-icon,
.pattern-option-icon,
.animation-option-icon {
  font-size: 1.25rem;
}

.toggle-option {
  margin-top: 0.5rem;
}

.toggle-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  color: var(--text-primary);
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 2px;
  background-color: var(--text-secondary);
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-color-light);
  border-color: var(--accent-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(22px);
  background-color: var(--accent-color);
}

@media (max-width: 768px) {
  .style-options,
  .pattern-options,
  .animation-options {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .style-option,
  .pattern-option,
  .animation-option {
    width: 100%;
  }
}
