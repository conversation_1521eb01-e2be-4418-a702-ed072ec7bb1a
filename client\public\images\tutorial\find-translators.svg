<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Base styles */
    .document { transition: transform 0.3s ease, filter 0.3s ease; }
    .center-icon { transition: transform 0.5s ease, filter 0.5s ease; }
    .connection-line { transition: stroke-dashoffset 2s linear infinite, stroke 0.3s ease; }
    .decorative-element { transition: transform 0.5s ease, opacity 0.5s ease; }
    
    /* Interactive animations */
    .document:hover { transform: translateY(-5px) scale(1.05); filter: drop-shadow(0 10px 10px rgba(0,0,0,0.15)); }
    .center-icon:hover { transform: rotate(10deg) scale(1.1); filter: drop-shadow(0 5px 10px rgba(0,0,0,0.2)); }
    
    /* Continuous animations */
    @keyframes dash {
      to { stroke-dashoffset: -20; }
    }
    .connection-line {
      stroke-dasharray: 5 3;
      animation: dash 2s linear infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    .float-animation {
      animation: float 3s ease-in-out infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }
    .pulse-animation {
      animation: pulse 4s ease-in-out infinite;
    }
  </style>

  <!-- Definitions for 3D effects -->
  <defs>
    <!-- 3D gradients -->
    <linearGradient id="document3dGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff"/>
      <stop offset="100%" stop-color="#f0f0f0"/>
    </linearGradient>
    
    <linearGradient id="accent3dGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#40E0D0"/>
      <stop offset="100%" stop-color="#20B2AA"/>
    </linearGradient>
    
    <!-- 3D shadows and effects -->
    <filter id="shadow3d" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000" flood-opacity="0.15"/>
    </filter>
    
    <filter id="innerShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur"/>
      <feOffset in="blur" dx="1" dy="1" result="offsetBlur"/>
      <feComposite in="SourceGraphic" in2="offsetBlur" operator="over"/>
    </filter>
    
    <!-- 3D bevels -->
    <filter id="bevel" x="-10%" y="-10%" width="120%" height="120%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="1" result="blur"/>
      <feOffset in="blur" dx="-1" dy="-1" result="offsetBlur1"/>
      <feOffset in="blur" dx="1" dy="1" result="offsetBlur2"/>
      <feComposite in="SourceGraphic" in2="offsetBlur1" operator="over" result="comp1"/>
      <feComposite in="comp1" in2="offsetBlur2" operator="over"/>
    </filter>
  </defs>
  
  <!-- Background with 3D perspective -->
  <rect width="600" height="400" fill="#f8f9fa"/>
  <path d="M0,400 L600,350 L600,400 Z" fill="#e9ecef" opacity="0.5"/>
  <path d="M0,0 L600,0 L600,50 L0,100 Z" fill="#e9ecef" opacity="0.5"/>
  
  <!-- 3D decorative elements -->
  <g class="decorative-element float-animation" transform="translate(80, 80)">
    <rect x="-15" y="-15" width="30" height="30" rx="5" fill="#30D5C8" opacity="0.1" filter="url(#bevel)"/>
    <path d="M-5,-5 L5,5 M-5,5 L5,-5" stroke="#30D5C8" stroke-width="1.5"/>
  </g>
  
  <g class="decorative-element float-animation" transform="translate(520, 80)" style="animation-delay: -1s">
    <rect x="-15" y="-15" width="30" height="30" rx="5" fill="#30D5C8" opacity="0.1" filter="url(#bevel)"/>
    <path d="M-5,0 L5,0 M0,-5 L0,5" stroke="#30D5C8" stroke-width="1.5"/>
  </g>
  
  <g class="decorative-element float-animation" transform="translate(80, 320)" style="animation-delay: -1.5s">
    <rect x="-15" y="-15" width="30" height="30" rx="5" fill="#30D5C8" opacity="0.1" filter="url(#bevel)"/>
    <circle cx="0" cy="0" r="5" fill="none" stroke="#30D5C8" stroke-width="1.5"/>
  </g>
  
  <g class="decorative-element float-animation" transform="translate(520, 320)" style="animation-delay: -2s">
    <rect x="-15" y="-15" width="30" height="30" rx="5" fill="#30D5C8" opacity="0.1" filter="url(#bevel)"/>
    <rect x="-5" y="-5" width="10" height="10" fill="none" stroke="#30D5C8" stroke-width="1.5"/>
  </g>
  
  <!-- Main 3D content -->
  <g transform="translate(300, 200)">
    <!-- 3D connection lines with animation -->
    <path class="connection-line" d="M-50,0 C-80,-30 -120,-30 -150,0" stroke="#30D5C8" stroke-width="2" fill="none"/>
    <path class="connection-line" d="M50,0 C80,-30 120,-30 150,0" stroke="#30D5C8" stroke-width="2" fill="none" style="animation-delay: -1s"/>
    
    <!-- 3D center icon with interactive hover -->
    <g class="center-icon" filter="url(#shadow3d)">
      <circle cx="0" cy="0" r="50" fill="url(#accent3dGradient)" opacity="0.9"/>
      <circle cx="0" cy="0" r="45" fill="white"/>
      
      <!-- 3D icon -->
      <g transform="translate(0, 0)">
        <rect x="-20" y="-20" width="40" height="40" rx="5" fill="#f8f9fa" stroke="#30D5C8" stroke-width="2" filter="url(#bevel)"/>
        <line x1="-12" y1="0" x2="12" y2="0" stroke="#30D5C8" stroke-width="2"/>
        <line x1="-12" y1="-8" x2="12" y2="-8" stroke="#30D5C8" stroke-width="1.5"/>
        <line x1="-12" y1="8" x2="12" y2="8" stroke="#30D5C8" stroke-width="1.5"/>
      </g>
      
      <!-- 3D accent ring -->
      <circle cx="0" cy="0" r="35" fill="none" stroke="#30D5C8" stroke-width="1" class="pulse-animation"/>
    </g>
    
    <!-- 3D source document with interactive hover -->
    <g class="document" transform="translate(-200, 0)" filter="url(#shadow3d)">
      <!-- 3D document container -->
      <rect x="-50" y="-70" width="100" height="140" rx="5" fill="white" stroke="#ddd" stroke-width="1"/>
      <rect x="-45" y="-65" width="90" height="130" rx="3" fill="url(#document3dGradient)" filter="url(#bevel)"/>
      
      <!-- 3D document content -->
      <rect x="-35" y="-55" width="70" height="110" fill="#f8f9fa" stroke="#eaeaea" stroke-width="0.5"/>
      
      <!-- Document header with 3D effect -->
      <rect x="-35" y="-55" width="70" height="15" fill="#f0f0f0" stroke="#eaeaea" stroke-width="0.5" filter="url(#bevel)"/>
      
      <!-- Document lines with 3D effect -->
      <line x1="-25" y1="-45" x2="25" y2="-45" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-30" x2="25" y2="-30" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-15" x2="25" y2="-15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="0" x2="25" y2="0" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="15" x2="25" y2="15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="30" x2="15" y2="30" stroke="#ccc" stroke-width="1"/>
      
      <!-- 3D legal seal -->
      <circle cx="20" cy="35" r="12" fill="#f8f9fa" stroke="#30D5C8" stroke-width="1" filter="url(#bevel)"/>
      <path d="M15,35 L25,35 M20,30 L20,40" stroke="#30D5C8" stroke-width="1"/>
      <circle cx="20" cy="35" r="6" fill="none" stroke="#30D5C8" stroke-width="0.5" class="pulse-animation"/>
    </g>
    
    <!-- 3D translated document with interactive hover -->
    <g class="document" transform="translate(200, 0)" filter="url(#shadow3d)">
      <!-- 3D document container -->
      <rect x="-50" y="-70" width="100" height="140" rx="5" fill="white" stroke="#ddd" stroke-width="1"/>
      <rect x="-45" y="-65" width="90" height="130" rx="3" fill="url(#document3dGradient)" filter="url(#bevel)"/>
      
      <!-- 3D document content -->
      <rect x="-35" y="-55" width="70" height="110" fill="#f8f9fa" stroke="#eaeaea" stroke-width="0.5"/>
      
      <!-- Document header with 3D effect -->
      <rect x="-35" y="-55" width="70" height="15" fill="#f0f0f0" stroke="#eaeaea" stroke-width="0.5" filter="url(#bevel)"/>
      
      <!-- Document lines with 3D effect -->
      <line x1="-25" y1="-45" x2="25" y2="-45" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-30" x2="25" y2="-30" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="-15" x2="25" y2="-15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="0" x2="25" y2="0" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="15" x2="25" y2="15" stroke="#ccc" stroke-width="1"/>
      <line x1="-25" y1="30" x2="15" y2="30" stroke="#ccc" stroke-width="1"/>
      
      <!-- 3D certification mark -->
      <g transform="translate(20, 35)">
        <circle cx="0" cy="0" r="12" fill="#f8f9fa" stroke="#30D5C8" stroke-width="1" filter="url(#bevel)"/>
        <path d="M-5,-5 L0,5 L5,-5" fill="none" stroke="#30D5C8" stroke-width="1"/>
        <circle cx="0" cy="0" r="6" fill="none" stroke="#30D5C8" stroke-width="0.5" class="pulse-animation"/>
      </g>
    </g>
  </g>
  
  <!-- 3D bottom accent -->
  <rect x="150" y="370" width="300" height="4" rx="2" fill="url(#accent3dGradient)" opacity="0.3" filter="url(#bevel)"/>
</svg>
