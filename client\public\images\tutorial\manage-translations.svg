<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions for gradients and filters -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8f9fa"/>
      <stop offset="100%" stop-color="#e9ecef"/>
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#30D5C8"/>
      <stop offset="100%" stop-color="#20B2AA"/>
    </linearGradient>
    
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.15"/>
    </filter>
    
    <filter id="softShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="white"/>
  
  <!-- Professional abstract background elements -->
  <rect x="0" y="0" width="600" height="400" fill="url(#bgGradient)" opacity="0.7"/>
  <path d="M0,400 L600,200 L600,400 Z" fill="#f8f9fa" opacity="0.5"/>
  <path d="M0,0 L300,100 L600,0 L600,50 L300,150 L0,50 Z" fill="url(#accentGradient)" opacity="0.1"/>
  
  <!-- Main content area -->
  <g transform="translate(300, 200)">
    <!-- Central connecting element -->
    <g filter="url(#dropShadow)">
      <circle cx="0" cy="0" r="60" fill="white"/>
      <circle cx="0" cy="0" r="55" fill="white" stroke="#30D5C8" stroke-width="2"/>
      
      <!-- LingoLink logo/icon -->
      <g transform="translate(0, -10)">
        <path d="M-20,-15 L-20,15 L20,15 L20,-15 Z" fill="none" stroke="#30D5C8" stroke-width="2.5" stroke-linejoin="round"/>
        <line x1="-15" y1="0" x2="15" y2="0" stroke="#30D5C8" stroke-width="2.5"/>
        <line x1="-15" y1="-7" x2="15" y2="-7" stroke="#30D5C8" stroke-width="1.5"/>
        <line x1="-15" y1="7" x2="15" y2="7" stroke="#30D5C8" stroke-width="1.5"/>
      </g>
      
      <text x="0" y="25" font-family="Arial" font-size="12" font-weight="bold" fill="#333" text-anchor="middle">LINGOLINK</text>
      <text x="0" y="40" font-family="Arial" font-size="8" fill="#666" text-anchor="middle">LEGAL TRANSLATION BROKER</text>
    </g>
    
    <!-- Connection lines -->
    <path d="M-55,0 L-140,0" stroke="#30D5C8" stroke-width="2" stroke-dasharray="4,2"/>
    <path d="M55,0 L140,0" stroke="#30D5C8" stroke-width="2" stroke-dasharray="4,2"/>
    
    <!-- Left side: Client with legal document -->
    <g transform="translate(-200, 0)" filter="url(#dropShadow)">
      <rect x="-70" y="-80" width="140" height="160" rx="5" ry="5" fill="white"/>
      
      <!-- Client icon -->
      <g transform="translate(0, -40)">
        <circle cx="0" cy="0" r="25" fill="#f8f9fa" stroke="#ddd"/>
        <path d="M-8,-5 A8,8 0 0,1 8,-5 A8,8 0 0,1 0,5 A8,8 0 0,1 -8,-5" fill="#666"/>
        <circle cx="0" cy="-12" r="8" fill="#666"/>
        <text x="0" y="35" font-family="Arial" font-size="12" font-weight="bold" fill="#333" text-anchor="middle">CLIENT</text>
      </g>
      
      <!-- Legal document -->
      <g transform="translate(0, 30)">
        <rect x="-30" y="-30" width="60" height="80" fill="#f8f9fa" stroke="#ddd"/>
        <line x1="-20" y1="-20" x2="20" y2="-20" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="-10" x2="20" y2="-10" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="0" x2="20" y2="0" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="10" x2="20" y2="10" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="20" x2="10" y2="20" stroke="#666" stroke-width="1"/>
        
        <!-- Legal stamp/seal -->
        <circle cx="15" cy="30" r="10" fill="none" stroke="#30D5C8" stroke-width="1"/>
        <path d="M10,30 L20,30 M15,25 L15,35" stroke="#30D5C8" stroke-width="1"/>
      </g>
    </g>
    
    <!-- Right side: Professional translator -->
    <g transform="translate(200, 0)" filter="url(#dropShadow)">
      <rect x="-70" y="-80" width="140" height="160" rx="5" ry="5" fill="white"/>
      
      <!-- Translator icon -->
      <g transform="translate(0, -40)">
        <circle cx="0" cy="0" r="25" fill="#f8f9fa" stroke="#ddd"/>
        <rect x="-15" y="-15" width="30" height="30" fill="none" stroke="#666" stroke-width="2"/>
        <text x="0" y="5" font-family="Arial" font-size="16" font-weight="bold" fill="#666" text-anchor="middle">T</text>
        <text x="0" y="35" font-family="Arial" font-size="12" font-weight="bold" fill="#333" text-anchor="middle">TRANSLATOR</text>
      </g>
      
      <!-- Translated document -->
      <g transform="translate(0, 30)">
        <rect x="-30" y="-30" width="60" height="80" fill="#f8f9fa" stroke="#ddd"/>
        <line x1="-20" y1="-20" x2="20" y2="-20" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="-10" x2="20" y2="-10" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="0" x2="20" y2="0" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="10" x2="20" y2="10" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="20" x2="10" y2="20" stroke="#666" stroke-width="1"/>
        
        <!-- Certification mark -->
        <g transform="translate(15, 30)">
          <path d="M-5,-5 L0,5 L5,-5" fill="none" stroke="#30D5C8" stroke-width="1.5"/>
          <circle cx="0" cy="0" r="10" fill="none" stroke="#30D5C8" stroke-width="1"/>
        </g>
      </g>
    </g>
  </g>
  
  <!-- Professional decorative elements -->
  <g transform="translate(50, 50)" filter="url(#softShadow)">
    <rect x="0" y="0" width="40" height="40" rx="5" ry="5" fill="white" stroke="#ddd"/>
    <path d="M10,10 L30,10 L30,30 L10,30 Z" fill="none" stroke="#30D5C8" stroke-width="1.5"/>
    <path d="M15,15 L25,15 M15,20 L25,20 M15,25 L25,25" stroke="#30D5C8" stroke-width="1"/>
  </g>
  
  <g transform="translate(550, 50)" filter="url(#softShadow)">
    <rect x="-40" y="0" width="40" height="40" rx="5" ry="5" fill="white" stroke="#ddd"/>
    <circle cx="-20" cy="20" r="12" fill="none" stroke="#30D5C8" stroke-width="1.5"/>
    <text x="-20" y="24" font-family="Arial" font-size="14" font-weight="bold" fill="#30D5C8" text-anchor="middle">§</text>
  </g>
  
  <g transform="translate(50, 350)" filter="url(#softShadow)">
    <rect x="0" y="-40" width="40" height="40" rx="5" ry="5" fill="white" stroke="#ddd"/>
    <path d="M10,-30 L30,-10 M10,-10 L30,-30" stroke="#30D5C8" stroke-width="1.5"/>
    <rect x="10" y="-30" width="20" height="20" fill="none" stroke="#30D5C8" stroke-width="1"/>
  </g>
  
  <g transform="translate(550, 350)" filter="url(#softShadow)">
    <rect x="-40" y="-40" width="40" height="40" rx="5" ry="5" fill="white" stroke="#ddd"/>
    <path d="M-30,-20 L-10,-20 M-20,-30 L-20,-10" stroke="#30D5C8" stroke-width="1.5"/>
    <circle cx="-20" cy="-20" r="12" fill="none" stroke="#30D5C8" stroke-width="1"/>
  </g>
  
  <!-- Professional bottom banner -->
  <g transform="translate(300, 380)">
    <rect x="-250" y="-15" width="500" height="30" fill="url(#accentGradient)" opacity="0.1"/>
    <text x="0" y="5" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CERTIFIED LEGAL TRANSLATION SERVICES</text>
  </g>
</svg>
