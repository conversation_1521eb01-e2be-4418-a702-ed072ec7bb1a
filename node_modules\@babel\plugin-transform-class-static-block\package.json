{"name": "@babel/plugin-transform-class-static-block", "version": "7.26.0", "description": "Transform class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-class-static-block"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.12.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/plugin-external-helpers": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.26.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-class-static-block", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}