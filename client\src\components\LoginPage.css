.login-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-primary, #f9fafb);
  padding: 2rem;
}

.login-container {
  width: 100%;
  max-width: 480px;
  background-color: var(--bg-secondary, #ffffff);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  justify-content: center;
}

.login-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--text-primary, #111827);
}

.login-logo img {
  height: 32px;
}

.login-logo h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.login-content {
  padding: 0;
}

/* Dark mode support */
[data-theme="dark"] .login-page {
  background-color: var(--bg-primary, #121212);
}

[data-theme="dark"] .login-container {
  background-color: var(--bg-secondary, #1e1e1e);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .login-page {
    padding: 1rem;
  }
  
  .login-container {
    max-width: 100%;
  }
}
