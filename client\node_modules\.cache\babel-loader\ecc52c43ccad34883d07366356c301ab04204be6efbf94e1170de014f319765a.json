{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lingolink\\\\client\\\\src\\\\components\\\\ColorChangingLogo.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ColorChangingLogo = ({\n  className,\n  alt = \"LingoLink\",\n  size = 'medium'\n}) => {\n  _s();\n  const [accentColor, setAccentColor] = useState('#20B2AA'); // Default turquoise color\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [textColor, setTextColor] = useState('#000000'); // Default text color\n\n  // Set size based on prop\n  const sizeMap = {\n    small: {\n      width: '32px',\n      height: '32px'\n    },\n    medium: {\n      width: '60px',\n      height: '60px'\n    },\n    large: {\n      width: '100px',\n      height: '100px'\n    }\n  };\n  const dimensions = sizeMap[size] || sizeMap.medium;\n\n  // Listen for theme changes\n  useEffect(() => {\n    // Get initial theme\n    const theme = document.documentElement.getAttribute('data-theme') || 'light';\n    setIsDarkMode(theme === 'dark');\n    setTextColor('#000000');\n\n    // Get initial accent color\n    const rootStyles = getComputedStyle(document.documentElement);\n    const currentAccentColor = rootStyles.getPropertyValue('--accent-color').trim();\n    if (currentAccentColor) {\n      setAccentColor(currentAccentColor);\n    }\n\n    // Create a MutationObserver to watch for theme changes\n    const observer = new MutationObserver(mutations => {\n      mutations.forEach(mutation => {\n        if (mutation.attributeName === 'data-theme') {\n          const newTheme = document.documentElement.getAttribute('data-theme');\n          setIsDarkMode(newTheme === 'dark');\n          setTextColor('#000000');\n        }\n      });\n    });\n\n    // Start observing the document element for attribute changes\n    observer.observe(document.documentElement, {\n      attributes: true\n    });\n\n    // Create a function to check for CSS variable changes\n    const checkForColorChanges = () => {\n      const rootStyles = getComputedStyle(document.documentElement);\n      const newAccentColor = rootStyles.getPropertyValue('--accent-color').trim();\n      if (newAccentColor && newAccentColor !== accentColor) {\n        setAccentColor(newAccentColor);\n      }\n    };\n\n    // Set up an interval to check for color changes\n    const intervalId = setInterval(checkForColorChanges, 1000);\n\n    // Clean up\n    return () => {\n      observer.disconnect();\n      clearInterval(intervalId);\n    };\n  }, [accentColor]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `color-changing-logo ${className || ''}`,\n    style: dimensions,\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      id: \"svg\",\n      version: \"1.1\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n      width: \"100%\",\n      height: \"100%\",\n      viewBox: \"0, 0, 400, 264.9874055415617\",\n      \"aria-label\": alt,\n      children: [/*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n            .logo-primary-fill { fill: ${accentColor}; }\n            .logo-secondary-fill { fill: ${textColor}; }\n          `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n        id: \"svgg\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          id: \"path0\",\n          className: \"logo-secondary-fill\",\n          d: \"M167.817 49.801 C 167.466 49.899,166.834 50.405,166.413 50.926 L 165.647 51.872 165.665 64.432 L 165.683 76.993 166.672 78.012 C 167.880 79.258,169.870 80.216,171.621 80.395 C 173.323 80.570,173.540 80.914,173.714 83.711 C 173.936 87.281,173.777 169.728,173.548 170.191 C 173.342 170.607,170.047 172.017,169.190 172.056 C 168.453 172.089,166.696 173.159,166.141 173.911 C 165.444 174.855,165.374 199.812,166.067 200.200 C 166.288 200.323,166.468 200.589,166.468 200.791 C 166.468 200.992,166.695 201.229,166.974 201.318 C 167.252 201.406,167.546 201.652,167.628 201.866 C 167.758 202.203,173.414 202.253,211.734 202.253 L 255.691 202.253 256.739 201.180 L 257.787 200.107 257.787 180.525 L 257.787 160.944 257.124 160.226 C 255.839 158.833,255.447 158.781,246.270 158.781 C 235.579 158.781,234.653 159.122,234.164 163.246 C 234.083 163.924,233.857 165.017,233.661 165.673 C 233.465 166.329,233.207 167.343,233.087 167.926 C 232.676 169.929,232.793 169.913,218.688 169.911 C 211.909 169.909,205.909 169.833,205.356 169.742 C 203.441 169.425,203.579 172.894,203.579 125.046 L 203.579 82.008 204.320 81.298 C 204.833 80.806,205.464 80.537,206.374 80.421 C 207.096 80.330,207.951 80.090,208.274 79.889 C 208.597 79.688,209.088 79.523,209.364 79.523 C 209.641 79.523,210.450 78.980,211.163 78.316 L 212.459 77.109 212.459 64.598 L 212.459 52.087 211.841 51.222 C 210.600 49.481,211.795 49.567,189.079 49.596 C 177.736 49.610,168.169 49.702,167.817 49.801\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(ColorChangingLogo, \"sLxT40Iy1e/q4uV4uLZttG/jACY=\");\n_c = ColorChangingLogo;\nexport default ColorChangingLogo;\nvar _c;\n$RefreshReg$(_c, \"ColorChangingLogo\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ColorChangingLogo", "className", "alt", "size", "_s", "accentColor", "setAccentColor", "isDarkMode", "setIsDarkMode", "textColor", "setTextColor", "sizeMap", "small", "width", "height", "medium", "large", "dimensions", "theme", "document", "documentElement", "getAttribute", "rootStyles", "getComputedStyle", "currentAccentColor", "getPropertyValue", "trim", "observer", "MutationObserver", "mutations", "for<PERSON>ach", "mutation", "attributeName", "newTheme", "observe", "attributes", "checkForColorChanges", "newAccentColor", "intervalId", "setInterval", "disconnect", "clearInterval", "style", "children", "id", "version", "xmlns", "xmlnsXlink", "viewBox", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lingolink/client/src/components/ColorChangingLogo.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\n\nconst ColorChangingLogo = ({ className, alt = \"LingoLink\", size = 'medium' }) => {\n  const [accentColor, setAccentColor] = useState('#20B2AA'); // Default turquoise color\n  const [isDarkMode, setIsDarkMode] = useState(false);\n  const [textColor, setTextColor] = useState('#000000'); // Default text color\n\n  // Set size based on prop\n  const sizeMap = {\n    small: { width: '32px', height: '32px' },\n    medium: { width: '60px', height: '60px' },\n    large: { width: '100px', height: '100px' }\n  };\n\n  const dimensions = sizeMap[size] || sizeMap.medium;\n\n  // Listen for theme changes\n  useEffect(() => {\n    // Get initial theme\n    const theme = document.documentElement.getAttribute('data-theme') || 'light';\n    setIsDarkMode(theme === 'dark');\n    setTextColor('#000000');\n\n    // Get initial accent color\n    const rootStyles = getComputedStyle(document.documentElement);\n    const currentAccentColor = rootStyles.getPropertyValue('--accent-color').trim();\n    if (currentAccentColor) {\n      setAccentColor(currentAccentColor);\n    }\n\n    // Create a MutationObserver to watch for theme changes\n    const observer = new MutationObserver((mutations) => {\n      mutations.forEach((mutation) => {\n        if (mutation.attributeName === 'data-theme') {\n          const newTheme = document.documentElement.getAttribute('data-theme');\n          setIsDarkMode(newTheme === 'dark');\n          setTextColor('#000000');\n        }\n      });\n    });\n\n    // Start observing the document element for attribute changes\n    observer.observe(document.documentElement, { attributes: true });\n\n    // Create a function to check for CSS variable changes\n    const checkForColorChanges = () => {\n      const rootStyles = getComputedStyle(document.documentElement);\n      const newAccentColor = rootStyles.getPropertyValue('--accent-color').trim();\n      if (newAccentColor && newAccentColor !== accentColor) {\n        setAccentColor(newAccentColor);\n      }\n    };\n\n    // Set up an interval to check for color changes\n    const intervalId = setInterval(checkForColorChanges, 1000);\n\n    // Clean up\n    return () => {\n      observer.disconnect();\n      clearInterval(intervalId);\n    };\n  }, [accentColor]);\n\n  return (\n    <div className={`color-changing-logo ${className || ''}`} style={dimensions}>\n      <svg\n        id=\"svg\"\n        version=\"1.1\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n        width=\"100%\"\n        height=\"100%\"\n        viewBox=\"0, 0, 400, 264.9874055415617\"\n        aria-label={alt}\n      >\n        {/* Use the current accent color for the logo */}\n        <style>\n          {`\n            .logo-primary-fill { fill: ${accentColor}; }\n            .logo-secondary-fill { fill: ${textColor}; }\n          `}\n        </style>\n\n        <g id=\"svgg\">\n          <path\n            id=\"path0\"\n            className=\"logo-secondary-fill\"\n            d=\"M167.817 49.801 C 167.466 49.899,166.834 50.405,166.413 50.926 L 165.647 51.872 165.665 64.432 L 165.683 76.993 166.672 78.012 C 167.880 79.258,169.870 80.216,171.621 80.395 C 173.323 80.570,173.540 80.914,173.714 83.711 C 173.936 87.281,173.777 169.728,173.548 170.191 C 173.342 170.607,170.047 172.017,169.190 172.056 C 168.453 172.089,166.696 173.159,166.141 173.911 C 165.444 174.855,165.374 199.812,166.067 200.200 C 166.288 200.323,166.468 200.589,166.468 200.791 C 166.468 200.992,166.695 201.229,166.974 201.318 C 167.252 201.406,167.546 201.652,167.628 201.866 C 167.758 202.203,173.414 202.253,211.734 202.253 L 255.691 202.253 256.739 201.180 L 257.787 200.107 257.787 180.525 L 257.787 160.944 257.124 160.226 C 255.839 158.833,255.447 158.781,246.270 158.781 C 235.579 158.781,234.653 159.122,234.164 163.246 C 234.083 163.924,233.857 165.017,233.661 165.673 C 233.465 166.329,233.207 167.343,233.087 167.926 C 232.676 169.929,232.793 169.913,218.688 169.911 C 211.909 169.909,205.909 169.833,205.356 169.742 C 203.441 169.425,203.579 172.894,203.579 125.046 L 203.579 82.008 204.320 81.298 C 204.833 80.806,205.464 80.537,206.374 80.421 C 207.096 80.330,207.951 80.090,208.274 79.889 C 208.597 79.688,209.088 79.523,209.364 79.523 C 209.641 79.523,210.450 78.980,211.163 78.316 L 212.459 77.109 212.459 64.598 L 212.459 52.087 211.841 51.222 C 210.600 49.481,211.795 49.567,189.079 49.596 C 177.736 49.610,168.169 49.702,167.817 49.801\"\n          />\n        </g>\n      </svg>\n    </div>\n  );\n};\n\nexport default ColorChangingLogo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,GAAG,GAAG,WAAW;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAMc,OAAO,GAAG;IACdC,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAC;IACxCC,MAAM,EAAE;MAAEF,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAC;IACzCE,KAAK,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAQ;EAC3C,CAAC;EAED,MAAMG,UAAU,GAAGN,OAAO,CAACR,IAAI,CAAC,IAAIQ,OAAO,CAACI,MAAM;;EAElD;EACAnB,SAAS,CAAC,MAAM;IACd;IACA,MAAMsB,KAAK,GAAGC,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,CAAC,IAAI,OAAO;IAC5Eb,aAAa,CAACU,KAAK,KAAK,MAAM,CAAC;IAC/BR,YAAY,CAAC,SAAS,CAAC;;IAEvB;IACA,MAAMY,UAAU,GAAGC,gBAAgB,CAACJ,QAAQ,CAACC,eAAe,CAAC;IAC7D,MAAMI,kBAAkB,GAAGF,UAAU,CAACG,gBAAgB,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAC,CAAC;IAC/E,IAAIF,kBAAkB,EAAE;MACtBlB,cAAc,CAACkB,kBAAkB,CAAC;IACpC;;IAEA;IACA,MAAMG,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAK;MACnDA,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAK;QAC9B,IAAIA,QAAQ,CAACC,aAAa,KAAK,YAAY,EAAE;UAC3C,MAAMC,QAAQ,GAAGd,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,CAAC;UACpEb,aAAa,CAACyB,QAAQ,KAAK,MAAM,CAAC;UAClCvB,YAAY,CAAC,SAAS,CAAC;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAiB,QAAQ,CAACO,OAAO,CAACf,QAAQ,CAACC,eAAe,EAAE;MAAEe,UAAU,EAAE;IAAK,CAAC,CAAC;;IAEhE;IACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;MACjC,MAAMd,UAAU,GAAGC,gBAAgB,CAACJ,QAAQ,CAACC,eAAe,CAAC;MAC7D,MAAMiB,cAAc,GAAGf,UAAU,CAACG,gBAAgB,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAC,CAAC;MAC3E,IAAIW,cAAc,IAAIA,cAAc,KAAKhC,WAAW,EAAE;QACpDC,cAAc,CAAC+B,cAAc,CAAC;MAChC;IACF,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGC,WAAW,CAACH,oBAAoB,EAAE,IAAI,CAAC;;IAE1D;IACA,OAAO,MAAM;MACXT,QAAQ,CAACa,UAAU,CAAC,CAAC;MACrBC,aAAa,CAACH,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACjC,WAAW,CAAC,CAAC;EAEjB,oBACEN,OAAA;IAAKE,SAAS,EAAE,uBAAuBA,SAAS,IAAI,EAAE,EAAG;IAACyC,KAAK,EAAEzB,UAAW;IAAA0B,QAAA,eAC1E5C,OAAA;MACE6C,EAAE,EAAC,KAAK;MACRC,OAAO,EAAC,KAAK;MACbC,KAAK,EAAC,4BAA4B;MAClCC,UAAU,EAAC,8BAA8B;MACzClC,KAAK,EAAC,MAAM;MACZC,MAAM,EAAC,MAAM;MACbkC,OAAO,EAAC,8BAA8B;MACtC,cAAY9C,GAAI;MAAAyC,QAAA,gBAGhB5C,OAAA;QAAA4C,QAAA,EACG;AACX,yCAAyCtC,WAAW;AACpD,2CAA2CI,SAAS;AACpD;MAAW;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAERrD,OAAA;QAAG6C,EAAE,EAAC,MAAM;QAAAD,QAAA,eACV5C,OAAA;UACE6C,EAAE,EAAC,OAAO;UACV3C,SAAS,EAAC,qBAAqB;UAC/BoD,CAAC,EAAC;QAA26C;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC96C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CA3FIJ,iBAAiB;AAAAsD,EAAA,GAAjBtD,iBAAiB;AA6FvB,eAAeA,iBAAiB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}