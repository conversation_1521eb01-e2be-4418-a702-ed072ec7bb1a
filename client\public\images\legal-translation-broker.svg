<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background and effects -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#30D5C8" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#30D5C8" stop-opacity="0.3"/>
    </linearGradient>
    
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="4" dy="4" stdDeviation="3" flood-color="#000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="#f8f9fa" rx="10" ry="10"/>
  
  <!-- Central elements -->
  <g transform="translate(300, 200)">
    <!-- Connection lines representing the broker service -->
    <path d="M-150,0 C-100,-50 100,-50 150,0" stroke="#30D5C8" stroke-width="3" fill="none" stroke-dasharray="5,3"/>
    <path d="M-150,0 C-100,50 100,50 150,0" stroke="#30D5C8" stroke-width="3" fill="none" stroke-dasharray="5,3"/>
    
    <!-- Central broker icon -->
    <circle cx="0" cy="0" r="50" fill="url(#bgGradient)" filter="url(#shadow)"/>
    <g transform="translate(0, 0)">
      <path d="M-15,-15 L15,-15 L15,15 L-15,15 Z" fill="none" stroke="#30D5C8" stroke-width="2"/>
      <path d="M-15,-15 L15,15 M-15,15 L15,-15" stroke="#30D5C8" stroke-width="2"/>
      <circle cx="0" cy="0" r="25" fill="none" stroke="#30D5C8" stroke-width="2"/>
      <text x="0" y="35" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">LingoLink</text>
    </g>
    
    <!-- Left side: Client -->
    <g transform="translate(-150, 0)" filter="url(#shadow)">
      <rect x="-40" y="-50" width="80" height="100" rx="5" ry="5" fill="white" stroke="#ddd"/>
      
      <!-- Client icon -->
      <circle cx="0" cy="-25" r="15" fill="#30D5C8" opacity="0.8"/>
      <path d="M-5,-25 A5,5 0 0,1 5,-25 A5,5 0 0,1 0,-20 A5,5 0 0,1 -5,-25" fill="white"/>
      <circle cx="0" cy="-30" r="5" fill="white"/>
      
      <!-- Document lines -->
      <line x1="-25" y1="0" x2="25" y2="0" stroke="#333" stroke-width="1"/>
      <line x1="-25" y1="10" x2="25" y2="10" stroke="#333" stroke-width="1"/>
      <line x1="-25" y1="20" x2="25" y2="20" stroke="#333" stroke-width="1"/>
      <line x1="-25" y1="30" x2="15" y2="30" stroke="#333" stroke-width="1"/>
      
      <text x="0" y="60" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">Client</text>
    </g>
    
    <!-- Right side: Translator -->
    <g transform="translate(150, 0)" filter="url(#shadow)">
      <rect x="-40" y="-50" width="80" height="100" rx="5" ry="5" fill="white" stroke="#ddd"/>
      
      <!-- Legal document icon -->
      <rect x="-20" y="-40" width="40" height="50" fill="#f0f0f0" stroke="#ddd"/>
      <line x1="-15" y1="-30" x2="15" y2="-30" stroke="#333" stroke-width="1"/>
      <line x1="-15" y1="-20" x2="15" y2="-20" stroke="#333" stroke-width="1"/>
      <line x1="-15" y1="-10" x2="15" y2="-10" stroke="#333" stroke-width="1"/>
      
      <!-- Seal/stamp -->
      <circle cx="10" cy="-5" r="8" fill="#30D5C8" opacity="0.5"/>
      
      <!-- Translator icon -->
      <circle cx="0" cy="20" r="15" fill="#30D5C8" opacity="0.8"/>
      <text x="0" y="24" font-family="Arial" font-size="14" fill="white" text-anchor="middle">T</text>
      
      <text x="0" y="60" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">Translator</text>
    </g>
  </g>
  
  <!-- Legal symbols -->
  <g transform="translate(100, 80)">
    <circle cx="0" cy="0" r="30" fill="url(#bgGradient)" opacity="0.7" filter="url(#shadow)"/>
    <path d="M-10,-15 L10,-15 L10,15 L-10,15 Z" fill="none" stroke="#333" stroke-width="1.5"/>
    <path d="M-5,-10 L5,-10 M-5,-5 L5,-5 M-5,0 L5,0 M-5,5 L5,5 M-5,10 L5,10" stroke="#333" stroke-width="1"/>
  </g>
  
  <g transform="translate(500, 80)">
    <circle cx="0" cy="0" r="30" fill="url(#bgGradient)" opacity="0.7" filter="url(#shadow)"/>
    <path d="M-15,0 L15,0 M0,-15 L0,15" stroke="#333" stroke-width="2"/>
    <circle cx="0" cy="0" r="10" fill="none" stroke="#333" stroke-width="1.5"/>
  </g>
  
  <g transform="translate(500, 320)">
    <circle cx="0" cy="0" r="30" fill="url(#bgGradient)" opacity="0.7" filter="url(#shadow)"/>
    <path d="M-10,-10 L10,10 M-10,10 L10,-10" stroke="#333" stroke-width="2"/>
    <circle cx="0" cy="0" r="15" fill="none" stroke="#333" stroke-width="1.5"/>
  </g>
  
  <g transform="translate(100, 320)">
    <circle cx="0" cy="0" r="30" fill="url(#bgGradient)" opacity="0.7" filter="url(#shadow)"/>
    <path d="M-15,-5 L15,-5 L15,15 L-15,15 Z" fill="none" stroke="#333" stroke-width="1.5"/>
    <path d="M-10,-15 L0,-5 L10,-15" stroke="#333" stroke-width="1.5" fill="none"/>
  </g>
</svg>
