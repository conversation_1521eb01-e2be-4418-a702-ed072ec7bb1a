.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 400px;
  min-width: 300px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
}

.notification-info {
  background-color: rgba(66, 153, 225, 0.15);
  color: var(--info-color);
  border-left: 4px solid var(--info-color);
}

.notification-success {
  background-color: rgba(72, 187, 120, 0.15);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
}

.notification-warning {
  background-color: rgba(237, 137, 54, 0.15);
  color: var(--warning-color);
  border-left: 4px solid var(--warning-color);
}

.notification-error {
  background-color: rgba(245, 101, 101, 0.15);
  color: var(--error-color);
  border-left: 4px solid var(--error-color);
}

.notification-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
}

.notification-content p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.notification-close {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.notification-close:hover {
  opacity: 1;
}

/* Animation classes */
.notification-enter {
  animation: slideIn 0.3s forwards;
}

.notification-exit {
  animation: slideOut 0.3s forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: calc(100% - 20px);
    min-width: auto;
  }
}
