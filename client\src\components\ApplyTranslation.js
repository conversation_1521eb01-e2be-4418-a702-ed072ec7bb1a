import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './ApplyTranslation-new.css';

const ApplyTranslation = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    sourceLanguage: '',
    targetLanguage: '',
    documentType: 'general',
    pages: 1,
    pricePerPage: 15,
    deadline: '',
    urgency: 'standard',
    additionalServices: {
      proofreading: false,
      formatting: false,
      certification: false,
      notarization: false
    },
    description: '',
    attachments: [],
    contactMethod: 'email',
    specialInstructions: ''
  });

  // Languages data
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' }
  ];

  const documentTypes = [
    { value: 'general', label: 'General Document', icon: '📄' },
    { value: 'legal', label: 'Legal Document', icon: '⚖️' },
    { value: 'technical', label: 'Technical Document', icon: '🔧' },
    { value: 'medical', label: 'Medical Document', icon: '🏥' },
    { value: 'academic', label: 'Academic Document', icon: '🎓' },
    { value: 'business', label: 'Business Document', icon: '💼' },
    { value: 'marketing', label: 'Marketing Material', icon: '📢' }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setErrors(prev => ({ ...prev, [name]: '' })); // Clear error when user types

    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        additionalServices: {
          ...prev.additionalServices,
          [name]: checked
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    const validFiles = files.filter(file => {
      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
      return validTypes.includes(file.type) && file.size <= 10 * 1024 * 1024; // 10MB limit
    });

    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...validFiles]
    }));
  };

  const removeFile = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const calculateTotal = () => {
    let basePrice = formData.pages * formData.pricePerPage;
    let total = basePrice;

    // Urgency multiplier
    const urgencyMultipliers = {
      standard: 1,
      express: 1.25,
      urgent: 1.5,
      emergency: 2
    };
    total *= urgencyMultipliers[formData.urgency] || 1;

    // Additional services
    if (formData.additionalServices.proofreading) total *= 1.15;
    if (formData.additionalServices.formatting) total *= 1.10;
    if (formData.additionalServices.certification) total *= 1.20;
    if (formData.additionalServices.notarization) total *= 1.25;

    return {
      basePrice: basePrice.toFixed(2),
      total: total.toFixed(2),
      savings: basePrice > total ? (basePrice - total).toFixed(2) : 0
    };
  };

  const validateStep = (stepNumber) => {
    const newErrors = {};

    switch(stepNumber) {
      case 1:
        if (!formData.sourceLanguage) newErrors.sourceLanguage = 'Source language is required';
        if (!formData.targetLanguage) newErrors.targetLanguage = 'Target language is required';
        if (formData.sourceLanguage === formData.targetLanguage) newErrors.targetLanguage = 'Target language must be different from source';
        if (!formData.documentType) newErrors.documentType = 'Document type is required';
        break;
      case 2:
        if (!formData.pages || formData.pages < 1) newErrors.pages = 'Number of pages must be at least 1';
        if (!formData.pricePerPage || formData.pricePerPage < 15) newErrors.pricePerPage = 'Price per page must be at least 15 TND';
        if (!formData.deadline) newErrors.deadline = 'Deadline is required';
        break;
      case 3:
        if (!formData.description.trim()) newErrors.description = 'Project description is required';
        if (formData.attachments.length === 0) newErrors.attachments = 'At least one document must be uploaded';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(step)) {
      setStep(prev => Math.min(prev + 1, 4));
    }
  };

  const prevStep = () => {
    setStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateStep(4)) return;

    setIsLoading(true);
    try {
      // TODO: Implement actual API call
      const response = await fetch('/api/translation-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          ...formData,
          totalPrice: calculateTotal().total,
          status: 'pending',
          submittedAt: new Date().toISOString()
        })
      });

      if (response.ok) {
        navigate('/dashboard', {
          state: {
            message: 'Translation request submitted successfully!',
            type: 'success'
          }
        });
      } else {
        throw new Error('Failed to submit request');
      }
    } catch (error) {
      console.error('Error submitting translation request:', error);
      setErrors({ submit: 'Failed to submit request. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-calculate total when form data changes
  useEffect(() => {
    calculateTotal();
  }, [formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices]);

  const renderStep = () => {
    switch(step) {
      case 1:
        return (
          <div className="step-content">
            <div className="step-header">
              <h2>📋 Document Details</h2>
              <p>Tell us about your translation project</p>
            </div>

            <div className="form-grid">
              <div className="form-group">
                <label>Source Language</label>
                <div className="select-wrapper">
                  <select
                    name="sourceLanguage"
                    value={formData.sourceLanguage}
                    onChange={handleInputChange}
                    className={errors.sourceLanguage ? 'error' : ''}
                  >
                    <option value="">Choose source language</option>
                    {languages.map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                  {errors.sourceLanguage && <span className="error-message">{errors.sourceLanguage}</span>}
                </div>
              </div>

              <div className="form-group">
                <label>Target Language</label>
                <div className="select-wrapper">
                  <select
                    name="targetLanguage"
                    value={formData.targetLanguage}
                    onChange={handleInputChange}
                    className={errors.targetLanguage ? 'error' : ''}
                  >
                    <option value="">Choose target language</option>
                    {languages.map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                  {errors.targetLanguage && <span className="error-message">{errors.targetLanguage}</span>}
                </div>
              </div>
            </div>

            <div className="form-group">
              <label>Document Type</label>
              <div className="document-type-grid">
                {documentTypes.map(type => (
                  <div
                    key={type.value}
                    className={`document-type-card ${formData.documentType === type.value ? 'selected' : ''}`}
                    onClick={() => handleInputChange({ target: { name: 'documentType', value: type.value } })}
                  >
                    <div className="document-icon">{type.icon}</div>
                    <div className="document-label">{type.label}</div>
                  </div>
                ))}
              </div>
              {errors.documentType && <span className="error-message">{errors.documentType}</span>}
            </div>
          </div>
        );
      case 2:
        const pricing = calculateTotal();
        return (
          <div className="step-content">
            <div className="step-header">
              <h2>⚙️ Service Options</h2>
              <p>Configure your translation requirements</p>
            </div>

            <div className="form-grid">
              <div className="form-group">
                <label>Number of Pages</label>
                <div className="input-wrapper">
                  <input
                    type="number"
                    name="pages"
                    min="1"
                    value={formData.pages}
                    onChange={handleInputChange}
                    className={errors.pages ? 'error' : ''}
                    placeholder="Enter number of pages"
                  />
                  {errors.pages && <span className="error-message">{errors.pages}</span>}
                </div>
              </div>

              <div className="form-group">
                <label>Price per Page (Min. 15 TND)</label>
                <div className="input-wrapper">
                  <input
                    type="number"
                    name="pricePerPage"
                    min="15"
                    value={formData.pricePerPage}
                    onChange={handleInputChange}
                    className={errors.pricePerPage ? 'error' : ''}
                    placeholder="15"
                  />
                  <span className="input-suffix">TND</span>
                  {errors.pricePerPage && <span className="error-message">{errors.pricePerPage}</span>}
                </div>
              </div>
            </div>

            <div className="form-grid">
              <div className="form-group">
                <label>Deadline</label>
                <div className="input-wrapper">
                  <input
                    type="date"
                    name="deadline"
                    value={formData.deadline}
                    onChange={handleInputChange}
                    className={errors.deadline ? 'error' : ''}
                    min={new Date().toISOString().split('T')[0]}
                  />
                  {errors.deadline && <span className="error-message">{errors.deadline}</span>}
                </div>
              </div>

              <div className="form-group">
                <label>Urgency Level</label>
                <div className="urgency-options">
                  {[
                    { value: 'standard', label: 'Standard', desc: 'Normal delivery', multiplier: '1x' },
                    { value: 'express', label: 'Express', desc: '3-5 days', multiplier: '1.25x' },
                    { value: 'urgent', label: 'Urgent', desc: '1-2 days', multiplier: '1.5x' },
                    { value: 'emergency', label: 'Emergency', desc: 'Same day', multiplier: '2x' }
                  ].map(option => (
                    <div
                      key={option.value}
                      className={`urgency-card ${formData.urgency === option.value ? 'selected' : ''}`}
                      onClick={() => handleInputChange({ target: { name: 'urgency', value: option.value } })}
                    >
                      <div className="urgency-label">{option.label}</div>
                      <div className="urgency-desc">{option.desc}</div>
                      <div className="urgency-multiplier">{option.multiplier}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="additional-services">
              <h4>📋 Additional Services</h4>
              <div className="services-grid">
                {[
                  { key: 'proofreading', label: 'Proofreading', desc: 'Quality review and corrections', price: '+15%', icon: '✏️' },
                  { key: 'formatting', label: 'Formatting', desc: 'Document layout and styling', price: '+10%', icon: '📐' },
                  { key: 'certification', label: 'Certification', desc: 'Official certified translation', price: '+20%', icon: '🏆' },
                  { key: 'notarization', label: 'Notarization', desc: 'Notarized document service', price: '+25%', icon: '📋' }
                ].map(service => (
                  <div
                    key={service.key}
                    className={`service-card ${formData.additionalServices[service.key] ? 'selected' : ''}`}
                    onClick={() => handleInputChange({
                      target: {
                        name: service.key,
                        type: 'checkbox',
                        checked: !formData.additionalServices[service.key]
                      }
                    })}
                  >
                    <div className="service-icon">{service.icon}</div>
                    <div className="service-info">
                      <div className="service-label">{service.label}</div>
                      <div className="service-desc">{service.desc}</div>
                    </div>
                    <div className="service-price">{service.price}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="price-summary">
              <div className="price-breakdown">
                <div className="price-item">
                  <span>Base Price ({formData.pages} pages × {formData.pricePerPage} TND)</span>
                  <span>{pricing.basePrice} TND</span>
                </div>
                <div className="price-item total">
                  <span>Total Price</span>
                  <span className="total-amount">{pricing.total} TND</span>
                </div>
              </div>
            </div>
          </div>
        );
      case 3:
        return (
          <div className="step-content">
            <div className="step-header">
              <h2>📄 Documents & Details</h2>
              <p>Upload your documents and provide additional information</p>
            </div>

            <div className="form-group">
              <label>Project Description</label>
              <div className="textarea-wrapper">
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Please provide specific requirements, context, terminology preferences, or any special instructions for your translation..."
                  rows="5"
                  className={errors.description ? 'error' : ''}
                />
                <div className="char-count">{formData.description.length}/500</div>
                {errors.description && <span className="error-message">{errors.description}</span>}
              </div>
            </div>

            <div className="form-group">
              <label>Upload Documents</label>
              <div className="file-upload-area">
                <input
                  type="file"
                  multiple
                  onChange={handleFileChange}
                  accept=".doc,.docx,.pdf,.txt"
                  id="file-upload"
                  className="file-input"
                />
                <label htmlFor="file-upload" className="file-upload-label">
                  <div className="upload-icon">📁</div>
                  <div className="upload-text">
                    <div>Click to upload or drag and drop</div>
                    <div className="upload-hint">PDF, DOC, DOCX, TXT (Max 10MB each)</div>
                  </div>
                </label>
                {errors.attachments && <span className="error-message">{errors.attachments}</span>}
              </div>

              {formData.attachments.length > 0 && (
                <div className="uploaded-files">
                  <h4>Uploaded Files ({formData.attachments.length})</h4>
                  {formData.attachments.map((file, index) => (
                    <div key={index} className="file-item">
                      <div className="file-info">
                        <div className="file-icon">📄</div>
                        <div className="file-details">
                          <div className="file-name">{file.name}</div>
                          <div className="file-size">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
                        </div>
                      </div>
                      <button
                        type="button"
                        className="remove-file"
                        onClick={() => removeFile(index)}
                      >
                        ✕
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="form-group">
              <label>Special Instructions (Optional)</label>
              <div className="textarea-wrapper">
                <textarea
                  name="specialInstructions"
                  value={formData.specialInstructions}
                  onChange={handleInputChange}
                  placeholder="Any additional notes, glossary terms, or specific formatting requirements..."
                  rows="3"
                />
              </div>
            </div>

            <div className="form-group">
              <label>Preferred Contact Method</label>
              <div className="contact-methods">
                {[
                  { value: 'email', label: 'Email', icon: '📧' },
                  { value: 'phone', label: 'Phone', icon: '📞' },
                  { value: 'whatsapp', label: 'WhatsApp', icon: '💬' }
                ].map(method => (
                  <div
                    key={method.value}
                    className={`contact-method ${formData.contactMethod === method.value ? 'selected' : ''}`}
                    onClick={() => handleInputChange({ target: { name: 'contactMethod', value: method.value } })}
                  >
                    <div className="method-icon">{method.icon}</div>
                    <div className="method-label">{method.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      case 4:
        const finalPricing = calculateTotal();
        const selectedLanguages = {
          source: languages.find(l => l.code === formData.sourceLanguage),
          target: languages.find(l => l.code === formData.targetLanguage)
        };
        const selectedDocType = documentTypes.find(t => t.value === formData.documentType);
        const selectedServices = Object.entries(formData.additionalServices)
          .filter(([, value]) => value)
          .map(([key]) => key);

        return (
          <div className="step-content">
            <div className="step-header">
              <h2>✅ Review & Submit</h2>
              <p>Please review your translation request before submitting</p>
            </div>

            <div className="review-summary">
              <div className="summary-section">
                <h3>📋 Project Details</h3>
                <div className="summary-grid">
                  <div className="summary-item">
                    <div className="summary-label">Languages</div>
                    <div className="summary-value">
                      {selectedLanguages.source?.flag} {selectedLanguages.source?.name} → {selectedLanguages.target?.flag} {selectedLanguages.target?.name}
                    </div>
                  </div>
                  <div className="summary-item">
                    <div className="summary-label">Document Type</div>
                    <div className="summary-value">
                      {selectedDocType?.icon} {selectedDocType?.label}
                    </div>
                  </div>
                  <div className="summary-item">
                    <div className="summary-label">Pages</div>
                    <div className="summary-value">{formData.pages} pages</div>
                  </div>
                  <div className="summary-item">
                    <div className="summary-label">Deadline</div>
                    <div className="summary-value">{new Date(formData.deadline).toLocaleDateString()}</div>
                  </div>
                </div>
              </div>

              <div className="summary-section">
                <h3>⚙️ Service Options</h3>
                <div className="summary-grid">
                  <div className="summary-item">
                    <div className="summary-label">Urgency</div>
                    <div className="summary-value">{formData.urgency}</div>
                  </div>
                  <div className="summary-item">
                    <div className="summary-label">Additional Services</div>
                    <div className="summary-value">
                      {selectedServices.length > 0 ? selectedServices.join(', ') : 'None'}
                    </div>
                  </div>
                  <div className="summary-item">
                    <div className="summary-label">Contact Method</div>
                    <div className="summary-value">{formData.contactMethod}</div>
                  </div>
                  <div className="summary-item">
                    <div className="summary-label">Files</div>
                    <div className="summary-value">{formData.attachments.length} file(s)</div>
                  </div>
                </div>
              </div>

              <div className="summary-section">
                <h3>💰 Pricing Breakdown</h3>
                <div className="pricing-details">
                  <div className="pricing-item">
                    <span>Base Price ({formData.pages} pages × {formData.pricePerPage} TND)</span>
                    <span>{finalPricing.basePrice} TND</span>
                  </div>
                  {formData.urgency !== 'standard' && (
                    <div className="pricing-item">
                      <span>Urgency ({formData.urgency})</span>
                      <span>+{((calculateTotal().total / finalPricing.basePrice - 1) * 100).toFixed(0)}%</span>
                    </div>
                  )}
                  {selectedServices.map(service => (
                    <div key={service} className="pricing-item">
                      <span>{service}</span>
                      <span>Included</span>
                    </div>
                  ))}
                  <div className="pricing-item total">
                    <span>Total Amount</span>
                    <span className="total-price">{finalPricing.total} TND</span>
                  </div>
                </div>
              </div>

              {errors.submit && (
                <div className="error-banner">
                  <span>❌ {errors.submit}</span>
                </div>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const stepTitles = [
    { title: 'Document Details', icon: '📋' },
    { title: 'Service Options', icon: '⚙️' },
    { title: 'Documents & Details', icon: '📄' },
    { title: 'Review & Submit', icon: '✅' }
  ];

  return (
    <div className="apply-translation-container">
      <div className="apply-translation">
        {/* Header */}
        <div className="page-header">
          <button className="back-button" onClick={() => navigate('/dashboard')}>
            ← Back to Dashboard
          </button>
          <h1>Apply for Translation</h1>
          <p>Submit your translation request in 4 easy steps</p>
        </div>

        {/* Progress Bar */}
        <div className="progress-bar">
          <div className="progress-track">
            <div
              className="progress-fill"
              style={{ width: `${((step - 1) / 3) * 100}%` }}
            />
          </div>
          <div className="progress-steps">
            {stepTitles.map((stepInfo, index) => {
              const stepNumber = index + 1;
              return (
                <div
                  key={stepNumber}
                  className={`step ${stepNumber === step ? 'active' : ''} ${stepNumber < step ? 'completed' : ''}`}
                  onClick={() => stepNumber < step && setStep(stepNumber)}
                >
                  <div className="step-circle">
                    {stepNumber < step ? '✓' : stepInfo.icon}
                  </div>
                  <div className="step-label">
                    <div className="step-title">{stepInfo.title}</div>
                    <div className="step-number">Step {stepNumber}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="translation-form">
          <div className="form-container">
            {renderStep()}
          </div>

          {/* Navigation */}
          <div className="form-navigation">
            <div className="nav-buttons">
              {step > 1 && (
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={prevStep}
                  disabled={isLoading}
                >
                  ← Previous
                </button>
              )}
              <div className="nav-spacer" />
              {step < 4 ? (
                <button
                  type="button"
                  className="btn-primary"
                  onClick={nextStep}
                  disabled={isLoading}
                >
                  Next →
                </button>
              ) : (
                <button
                  type="submit"
                  className="btn-submit"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="spinner" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Request ✓
                    </>
                  )}
                </button>
              )}
            </div>
            <div className="step-indicator">
              Step {step} of 4
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ApplyTranslation;