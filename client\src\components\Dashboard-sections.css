/* Modern styles for Dashboard */
* {
  box-sizing: border-box;
}

/* Enhanced Welcome Banner with Glassmorphism */
.welcome-banner {
  border-radius: 2rem;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
  box-shadow:
    0 20px 40px rgba(var(--accent-color-rgb, 32, 178, 170), 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  color: black;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-banner:hover {
  transform: translateY(-5px);
  box-shadow:
    0 25px 50px rgba(var(--accent-color-rgb, 32, 178, 170), 0.2),
    0 12px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced Banner Styles */
.welcome-banner.banner-style-default {
  background: linear-gradient(135deg,
    var(--accent-color) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.9) 50%,
    var(--accent-color) 100%);
  position: relative;
}

.welcome-banner.banner-style-default::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

/* Gradient banner style */
.welcome-banner.banner-style-gradient {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    var(--accent-color) 50%,
    #f093fb 75%,
    #f5576c 100%);
  background-size: 300% 300%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Solid banner style with enhanced effects */
.welcome-banner.banner-style-solid {
  background: var(--accent-color);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.welcome-banner.banner-style-solid::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%);
  pointer-events: none;
}

/* Minimal banner style */
.welcome-banner.banner-style-minimal {
  background-color: rgba(var(--accent-color-rgb), 0.85);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.welcome-banner .decoration {
  width: 30%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  display: var(--show-decorations, block);
}

.welcome-banner .decoration::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 60%);
  animation: pulse 15s infinite linear;
}

.welcome-banner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

@keyframes pulse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.welcome-banner .content {
  padding: 2.5rem;
  flex: 1;
  position: relative;
  z-index: 1;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.25rem;
}

.welcome-header-left {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  animation: fadeInLeft 0.6s ease-out;
}

.welcome-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.welcome-avatar:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.5);
}

.welcome-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-avatar .avatar-placeholder {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.welcome-user-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.welcome-header h2 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeInDown 0.6s ease-out;
}

.date-time {
  color: rgba(255, 255, 255, 0.85);
  font-size: 0.95rem;
  font-weight: 500;
  animation: fadeInRight 0.6s ease-out;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.welcome-description {
  margin-bottom: 1.75rem;
  animation: fadeIn 0.8s ease-out;
}

.welcome-banner p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  max-width: 90%;
}

.user-bio {
  position: relative;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 1rem;
  padding: 1.25rem;
  backdrop-filter: blur(5px);
  border-left: 4px solid rgba(255, 255, 255, 0.3);
  margin-top: 0.5rem;
  animation: fadeInUp 0.8s ease-out;
}

.bio-text {
  color: rgba(255, 255, 255, 0.95) !important;
  font-style: italic;
  font-size: 1.05rem;
  line-height: 1.7;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bio-label {
  position: absolute;
  top: -10px;
  left: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.user-role-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 0.5rem 0.75rem;
  border-radius: 2rem;
  backdrop-filter: blur(5px);
  display: inline-flex;
  margin-top: 0.25rem;
}

.welcome-content > .user-role-indicator {
  margin-bottom: 1.25rem;
  font-size: 1rem;
  padding: 0.75rem 1.25rem;
  animation: fadeInUp 0.7s ease-out;
}

.user-role-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  border-radius: 50%;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Dashboard Summary Metrics */
.dashboard-summary {
  display: flex;
  gap: 1.5rem;
  margin: 2rem 0;
  animation: fadeIn 0.9s ease-out;
  perspective: 1000px;
}

.summary-metric {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  padding: 1.75rem 1.5rem;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 100%);
  border-radius: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInScale 0.8s ease-out forwards;
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  animation-delay: calc(var(--i) * 0.15s);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.summary-metric::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.8s ease;
}

.summary-metric:hover {
  transform: translateY(-10px) rotateX(5deg) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.25) 100%);
  border-color: rgba(255, 255, 255, 0.4);
}

.summary-metric:hover::before {
  left: 100%;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 100%);
  color: white;
  border-radius: 20px;
  flex-shrink: 0;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.metric-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent);
  animation: rotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.summary-metric:hover .metric-icon {
  transform: scale(1.15) rotate(10deg);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.3) 100%);
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.summary-metric:hover .metric-icon::before {
  opacity: 1;
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
  z-index: 1;
}

.metric-value {
  font-size: 2rem;
  font-weight: 800;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.summary-metric:hover .metric-value {
  transform: scale(1.05);
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.metric-label {
  font-size: 1.05rem;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.summary-metric:hover .metric-label {
  color: white;
  transform: translateY(-2px);
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.welcome-actions {
  margin-top: 2rem;
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.3s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.welcome-actions .action-button-primary {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.85rem 1.5rem;
  border-radius: 2rem;
  font-size: 1.05rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.welcome-actions .action-button-primary:hover {
  background-color: rgba(255, 255, 255, 0.35);
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Quick Access Widgets */
.quick-access-widgets {
  margin-bottom: 2rem;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.widget-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.widgets-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  width: 100%;
  overflow-x: hidden;
  perspective: 1000px;
}

.quick-widget {
  background: linear-gradient(135deg,
    var(--card-bg) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.02) 100%);
  border-radius: 1.5rem;
  padding: 2rem 1.75rem;
  border: 1px solid var(--border-color);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: slideInUp 0.8s ease-out;
  animation-delay: calc(var(--i, 0) * 0.1s);
  animation-fill-mode: both;
}

.quick-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    transparent 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.05) 50%,
    transparent 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
}

.quick-widget:hover {
  transform: translateY(-12px) rotateX(5deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(var(--accent-color-rgb, 32, 178, 170), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: var(--accent-color);
  background: linear-gradient(135deg,
    var(--card-bg) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.08) 100%);
}

.quick-widget:hover::before {
  opacity: 1;
}

.widget-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg,
    var(--accent-color) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.8) 100%);
  color: white;
  border-radius: 16px;
  margin-bottom: 0.75rem;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 16px rgba(var(--accent-color-rgb, 32, 178, 170), 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.widget-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  animation: rotate 3s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-widget:hover .widget-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 12px 24px rgba(var(--accent-color-rgb, 32, 178, 170), 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.quick-widget:hover .widget-icon::before {
  opacity: 1;
}

.widget-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.quick-widget:hover .widget-title {
  color: var(--accent-color);
  transform: translateY(-2px);
}

.widget-description {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.6;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.quick-widget:hover .widget-description {
  color: var(--text-primary);
  transform: translateY(-1px);
}

.action-button {
  padding: 0.75rem 1.25rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.action-button-primary {
  background-color: var(--accent-color);
  color: white;
  border: none;
}

.action-button-primary:hover {
  background-color: var(--accent-color-dark);
  color: white;
}

/* Dashboard Sections */
.dashboard-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
  max-width: 100%;
  overflow-x: hidden;
}

.dashboard-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.section-action {
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.section-action:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

/* User Profile Card */
.user-profile-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.user-profile-header {
  display: flex;
  gap: 1.5rem;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-color);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.user-profile-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.user-profile-email {
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.user-profile-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.profile-completion {
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.completion-percentage {
  font-weight: 600;
  color: var(--accent-color);
}

.completion-bar {
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.completion-progress {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 4px;
}

.completion-tips {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-style: italic;
}

.user-profile-verification {
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.verification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.verification-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.verification-status.not-verified {
  color: #f59e0b;
}

.verification-button {
  padding: 0.5rem 1rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.verification-button:hover {
  background-color: var(--accent-color-dark);
}

.verification-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.user-profile-actions {
  display: flex;
  gap: 1rem;
}

.user-profile-button {
  flex: 1;
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.user-profile-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

/* Enhanced Stats Cards */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  width: 100%;
  overflow-x: visible;
  max-width: 100%;
  perspective: 1000px;
}

.stats-card {
  background: linear-gradient(135deg,
    var(--card-bg) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.02) 100%);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  padding: 2rem 1.75rem;
  display: flex;
  gap: 1.75rem;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  animation: slideInUp 0.8s ease-out;
  animation-delay: calc(var(--i, 0) * 0.15s);
  animation-fill-mode: both;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    transparent 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.03) 50%,
    transparent 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
}

.stats-card:hover {
  transform: translateY(-12px) rotateX(5deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.12),
    0 10px 20px rgba(var(--accent-color-rgb, 32, 178, 170), 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: var(--accent-color);
  background: linear-gradient(135deg,
    var(--card-bg) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.05) 100%);
}

.stats-card:hover::before {
  opacity: 1;
}

.stats-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg,
    var(--accent-color) 0%,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.8) 100%);
  color: white;
  border-radius: 18px;
  flex-shrink: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 16px rgba(var(--accent-color-rgb, 32, 178, 170), 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.stats-card-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  animation: rotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover .stats-card-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 12px 24px rgba(var(--accent-color-rgb, 32, 178, 170), 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.stats-card:hover .stats-card-icon::before {
  opacity: 1;
}

.stats-card-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  position: relative;
  z-index: 1;
}

.stats-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.stats-card:hover .stats-card-title {
  color: var(--text-primary);
  transform: translateY(-2px);
}

.stats-card-value {
  font-size: 2.25rem;
  font-weight: 800;
  color: var(--text-primary);
  transition: all 0.3s ease;
  background: linear-gradient(135deg,
    var(--text-primary),
    var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-card:hover .stats-card-value {
  transform: scale(1.05);
}

.stats-card-description {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.6;
  transition: all 0.3s ease;
}

.stats-card:hover .stats-card-description {
  color: var(--text-primary);
  transform: translateY(-1px);
}

.stats-card-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #10b981;
  margin-top: 0.5rem;
}

.progress-container {
  margin-top: 0.5rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.progress-bar-container {
  height: 6px;
  background-color: var(--border-color);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 3px;
}

/* Activity Timeline */
.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
}

.activity-item {
  display: flex;
  gap: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 12px;
  flex-shrink: 0;
}

.activity-icon.completed {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.activity-icon.in-progress {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.activity-icon.pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.activity-icon.message {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.activity-icon.request {
  background-color: rgba(236, 72, 153, 0.1);
  color: #ec4899;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.activity-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.activity-time {
  font-size: 0.85rem;
  color: var(--text-muted);
}

.activity-description {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.activity-status {
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.activity-status.status-completed {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.activity-status.status-in-progress {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.activity-status.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.activity-words {
  font-size: 0.85rem;
  color: var(--text-muted);
}

.activity-text {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
  background-color: var(--bg-secondary);
  padding: 0.75rem;
  border-radius: 0.5rem;
  border-left: 3px solid var(--accent-color-light);
}

.activity-actions {
  display: flex;
  gap: 0.75rem;
}

.activity-action-btn {
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.activity-action-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.view-more-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.view-more-btn {
  padding: 0.75rem 1.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-more-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.empty-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-secondary);
}

.empty-activity-icon {
  color: var(--text-muted);
  opacity: 0.7;
  margin-bottom: 1.5rem;
}

.empty-activity h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.empty-activity p {
  margin: 0 0 1.5rem 0;
  max-width: 400px;
  color: var(--text-secondary);
}

.start-action-btn {
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.start-action-btn:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-2px);
}

/* Analytics Card */
.analytics-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-container {
  height: 300px;
  margin-bottom: 1rem;
}

.chart-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.chart-summary-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.summary-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 12px;
  flex-shrink: 0;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.chart-insights {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insights-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.insights-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
}

.insight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0;
}

.insight-icon.positive {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-icon.negative {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.insight-icon.neutral {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.insight-icon.tip {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.insight-text {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Chart Period Selector */
.chart-period-selector {
  display: flex;
  gap: 0.5rem;
}

.chart-period-btn {
  padding: 0.5rem 1rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-period-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.chart-period-btn.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-color: var(--accent-color);
  font-weight: 600;
}

/* Responsive Media Queries */
@media (max-width: 1200px) {
  .dashboard-summary {
    flex-wrap: wrap;
  }

  .summary-metric {
    min-width: calc(50% - 0.75rem);
  }
}

@media (max-width: 992px) {
  .welcome-banner {
    flex-direction: column;
  }

  .welcome-banner .decoration {
    width: 100%;
    height: 100px;
  }

  .chart-summary {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .user-profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .user-info {
    align-items: center;
  }

  .stats-cards {
    grid-template-columns: 1fr;
    padding: 0 0.5rem;
  }

  .stats-card {
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
  }

  .stats-card-content {
    align-items: center;
    width: 100%;
  }

  .activity-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .activity-header {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .activity-actions {
    justify-content: center;
  }

  .chart-period-selector {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .dashboard-main {
    padding: 1rem;
  }

  .welcome-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .summary-metric {
    min-width: 100%;
  }

  .verification-header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .user-profile-actions {
    flex-direction: column;
  }
}

/* Enhanced Mobile Animations and Interactions */
@media (max-width: 768px) {
  .welcome-banner {
    animation: mobileSlideIn 1s ease-out;
  }

  .dashboard-summary {
    animation: mobileStagger 1.2s ease-out;
  }

  .widgets-container {
    animation: mobileStagger 1.4s ease-out;
  }
}

@keyframes mobileSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes mobileStagger {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .summary-metric:hover,
  .quick-widget:hover {
    transform: none;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .summary-metric:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .quick-widget:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .widget-icon:active {
    transform: scale(0.95);
  }

  .metric-icon:active {
    transform: scale(0.95);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .summary-metric,
  .quick-widget {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .welcome-banner {
    border: 2px solid var(--text-primary);
  }

  .summary-metric,
  .quick-widget {
    border: 2px solid var(--border-color);
  }

  .widget-icon,
  .metric-icon {
    border: 1px solid var(--text-primary);
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .welcome-banner {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  .summary-metric,
  .quick-widget {
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }
}

/* Floating Animation Elements */
.floating-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.1),
    rgba(var(--accent-color-rgb, 32, 178, 170), 0.05));
  animation: float 20s infinite linear;
}

.floating-circle:nth-child(1) {
  width: 80px;
  height: 80px;
  left: 10%;
  animation-delay: 0s;
}

.floating-circle:nth-child(2) {
  width: 120px;
  height: 120px;
  left: 20%;
  animation-delay: 2s;
}

.floating-circle:nth-child(3) {
  width: 60px;
  height: 60px;
  left: 70%;
  animation-delay: 4s;
}

.floating-circle:nth-child(4) {
  width: 100px;
  height: 100px;
  left: 80%;
  animation-delay: 6s;
}

.floating-circle:nth-child(5) {
  width: 140px;
  height: 140px;
  left: 50%;
  animation-delay: 8s;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Pulse Animation for Interactive Elements */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb, 32, 178, 170), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--accent-color-rgb, 32, 178, 170), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb, 32, 178, 170), 0);
  }
}

/* Glow Effect for Important Elements */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    var(--accent-color),
    transparent,
    var(--accent-color));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::after {
  opacity: 0.3;
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    filter: blur(5px);
  }
  to {
    filter: blur(10px);
  }
}

/* Stagger Animation for Lists */
.stagger-animation > * {
  animation: slideInStagger 0.6s ease-out;
  animation-fill-mode: both;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInStagger {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading Skeleton Animation */
.skeleton {
  background: linear-gradient(90deg,
    var(--bg-secondary) 25%,
    var(--hover-bg) 50%,
    var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Micro-interactions */
.micro-bounce {
  transition: transform 0.2s ease;
}

.micro-bounce:active {
  transform: scale(0.95);
}

.micro-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Success Animation */
.success-checkmark {
  animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
