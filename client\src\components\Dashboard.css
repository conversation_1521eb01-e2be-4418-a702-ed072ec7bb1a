/* CSS Variables for theming */
:root {
  --bg-primary: #f9fafb;
  --bg-secondary: #ffffff;
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --border-color: #e5e7eb;
  --accent-color: #20B2AA;
  --accent-color-dark: #188F89;
  --accent-color-light: rgba(32, 178, 170, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --card-bg: #ffffff;
  --hover-bg: #f3f4f6;
}

/* Dark mode variables */
[data-theme="dark"] {
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --text-primary: #f3f4f6;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --border-color: #374151;
  --accent-color: #40E0D0;
  --accent-color-dark: #2cc4b4;
  --accent-color-light: rgba(64, 224, 208, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.3);
  --card-bg: #1e1e1e;
  --hover-bg: #2d2d2d;
}

/* Error container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
  background-color: var(--bg-primary);
}

.error-container h2 {
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-container p {
  margin-bottom: 2rem;
  color: var(--text-secondary);
}

.error-container button {
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-container button:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-2px);
}

.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%;
  max-width: 100vw; /* Ensure it doesn't exceed viewport width */
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--bg-secondary);
  box-shadow: 0 1px 3px var(--shadow-color);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.dashboard-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-logo .logo {
  height: 32px;
  width: auto;
}

.dashboard-logo h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  transition: color 0.3s ease;
}

.dashboard-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dashboard-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-right: 0.5rem;
}

.dashboard-user span {
  color: var(--text-secondary);
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.user-type-badge {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.user-type-badge::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--accent-color);
  margin-right: 4px;
  transition: background-color 0.3s ease;
}

.logout-btn {
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.dashboard-content {
  display: flex;
  flex: 1;
}

.dashboard-sidebar {
  width: 250px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  padding: 1.5rem 0;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.dashboard-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dashboard-nav li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
}

.dashboard-nav li:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.dashboard-nav li.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-left: 3px solid var(--accent-color);
  font-weight: 500;
}

.dashboard-nav li.active svg {
  opacity: 1;
  color: var(--accent-color);
}

.dashboard-nav li svg {
  width: 20px;
  height: 20px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.dashboard-main {
  flex: 1;
  padding: 2rem;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100%; /* Ensure content doesn't exceed container width */
  box-sizing: border-box; /* Include padding in width calculation */
}

.dashboard-main h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.stat-card h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-muted);
  margin-top: 0;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.dashboard-recent, .dashboard-chart-section {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 1.5rem;
}

.dashboard-recent h3, .dashboard-chart-section .chart-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-period-selector {
  display: flex;
  gap: 0.5rem;
}

.chart-period-btn {
  padding: 0.375rem 0.75rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-period-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.chart-period-btn.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.chart-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.chart-summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.empty-state, .empty-requests, .empty-conversations, .empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.empty-state p, .empty-requests p, .empty-conversations p, .empty-messages p {
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.empty-state-icon {
  margin-bottom: 1rem;
  color: var(--text-tertiary);
}

.empty-messages {
  height: 100%;
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
}

.no-data {
  color: var(--text-muted);
  font-style: italic;
}

.new-translation-btn, .view-all-btn, .submit-btn {
  padding: 0.75rem 1.5rem;
  background-color: #20B2AA;
  color: black;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.new-translation-btn:hover, .view-all-btn:hover, .submit-btn:hover {
  background-color: #199692;
  transform: translateY(-1px);
}

.new-translation-btn:disabled, .view-all-btn:disabled, .submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.view-all-btn {
  display: block;
  margin: 1rem auto 0;
  text-align: center;
}

.submit-btn {
  margin-top: 1.5rem;
  width: 100%;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.translations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.translation-item {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.translation-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.translation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.translation-languages {
  font-weight: 500;
  color: #4b5563;
}

.translation-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-in-progress {
  background-color: #e0f2fe;
  color: #0369a1;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.translation-text {
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.translation-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
}

.translations-list.full-list {
  margin-top: 1.5rem;
}

.translation-form {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.translation-form .form-group {
  margin-bottom: 1rem;
}

.translation-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.translation-form select, .translation-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.translation-form select:focus, .translation-form textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.translation-form textarea {
  resize: vertical;
}

.settings-section {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
  margin-bottom: 1.5rem;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.settings-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-label {
  width: 120px;
  font-weight: 500;
  color: #6b7280;
}

.info-value {
  color: #111827;
}

/* New Dashboard Elements */
.welcome-banner {
  background: linear-gradient(135deg, #20B2AA 0%, #1d9b94 100%);
  border-radius: 0.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.welcome-banner h2 {
  margin-top: 0;
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.welcome-banner p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
  max-width: 600px;
}

.user-role-indicator {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  gap: 0.75rem;
}

.user-role-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 6px;
}

.user-role-text {
  font-size: 0.95rem;
  font-weight: 400;
}

.user-role-text strong {
  font-weight: 600;
}

.welcome-banner .decoration {
  position: absolute;
  right: -50px;
  top: -50px;
  width: 200px;
  height: 200px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.welcome-banner .content {
  position: relative;
  z-index: 1;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .user-profile-card {
    grid-column: span 1;
  }
}

.dashboard-card {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.dashboard-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  transition: color 0.3s ease;
}

.dashboard-card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dashboard-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0.5rem 0;
  transition: color 0.3s ease;
}

.dashboard-card-description {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-top: auto;
  transition: color 0.3s ease;
}

/* User Profile Card Styles */
.user-profile-card {
  grid-column: span 2;
}

.user-profile-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.user-profile-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.user-profile-email {
  font-size: 0.95rem;
  color: #4b5563;
}

.user-profile-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  width: fit-content;
}

/* Profile Page Styles */
.profile-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-avatar svg {
  width: 40px;
  height: 40px;
  color: var(--accent-color);
}

.profile-title h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.profile-subtitle {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.profile-type-badge {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.profile-email {
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.profile-section {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
}

.profile-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1.25rem 0;
  color: var(--text-primary);
}

.profile-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.profile-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

.info-value {
  font-size: 1rem;
  color: var(--text-primary);
}

.specialization-tags, .language-pairs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.specialization-tag {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
}

.language-pair {
  background-color: var(--hover-bg);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
}

.profile-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.profile-stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--hover-bg);
  border-radius: 0.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.profile-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px var(--shadow-color);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon svg {
  width: 24px;
  height: 24px;
  color: var(--accent-color);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.profile-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.profile-edit-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.profile-edit-button:hover {
  background-color: var(--accent-color-dark);
}

.profile-edit-button svg {
  width: 16px;
  height: 16px;
}

.success-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  background-color: rgba(72, 187, 120, 0.1);
  color: #48bb78;
  border-radius: 0.5rem;
  border-left: 4px solid #48bb78;
}

.success-message svg {
  width: 20px;
  height: 20px;
  color: #48bb78;
}

.user-profile-type.client {
  background-color: rgba(32, 178, 170, 0.1);
  color: #20B2AA;
}

.user-profile-type.translator {
  background-color: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
}

.user-profile-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-profile-verification {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.verification-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  width: fit-content;
}

.verification-status.not-verified {
  background-color: #fee2e2;
  color: #b91c1c;
}

.verification-status.verified {
  background-color: #dcfce7;
  color: #166534;
}

.verification-status svg {
  width: 16px;
  height: 16px;
}

.verification-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: fit-content;
}

.verification-button:hover {
  background-color: var(--accent-color-dark);
}

.verification-container-wrapper {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

.user-profile-actions {
  margin-top: 1rem;
}

.user-profile-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #4b5563;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-profile-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.progress-container {
  margin-top: 0.5rem;
  background-color: var(--hover-bg);
  border-radius: 9999px;
  height: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 9999px;
}

.activity-list {
  margin-top: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.quick-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.action-button {
  padding: 0.75rem 1.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  color: #111827;
}

.action-button-primary {
  background-color: #20B2AA;
  border-color: #20B2AA;
  color: white;
}

.action-button-primary:hover {
  background-color: #199692;
  border-color: #199692;
  color: white;
}

/* Language dropdown styles */
.language-dropdown {
  position: relative;
  display: inline-block;
}

.language-dropdown-btn {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1), transform 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.language-dropdown-btn:hover {
  background-color: #f3f4f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.language-dropdown-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--accent-color);
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.language-dropdown-btn.changed::after {
  transform: scaleX(1);
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

.language-dropdown-btn.changed::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.language-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 0.5rem;
  background-color: var(--bg-secondary);
  min-width: 160px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  z-index: 20;
  border: 1px solid var(--border-color);
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.language-dropdown:hover .language-dropdown-content {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.language-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 3px solid transparent;
  position: relative;
  overflow: hidden;
}

.language-item:hover {
  background-color: var(--hover-bg);
  transform: translateX(3px);
}

.language-item.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  font-weight: 500;
  border-left: 3px solid var(--accent-color);
}

.language-item span {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.language-item:hover span {
  transform: scale(1.2);
}

/* Theme toggle styles */
.theme-toggle {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: #f3f4f6;
  transform: translateY(-2px) rotate(15deg);
}

/* Personalization options in settings */
.personalization-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.personalization-group {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

.personalization-group h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-top: 0;
  margin-bottom: 1rem;
}

.language-options, .theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.language-option, .theme-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-option:hover, .theme-option:hover {
  background-color: #f3f4f6;
  transform: translateY(-2px);
}

.language-option.active, .theme-option.active {
  background-color: rgba(32, 178, 170, 0.1);
  border-color: #20B2AA;
  color: #20B2AA;
  font-weight: 500;
}

.language-flag, .theme-icon {
  font-size: 1.2rem;
}

/* Notification options */
.notification-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.notification-option input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  cursor: pointer;
}

/* Settings actions */
.settings-actions {
  margin-top: 2rem;
  display: flex;
  justify-content: flex-end;
}

.save-settings-btn {
  padding: 0.75rem 1.5rem;
  background-color: #20B2AA;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-settings-btn:hover {
  background-color: #188F89;
  transform: translateY(-2px);
}

.change-password-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-password-btn:hover {
  background-color: #f3f4f6;
  color: #111827;
  border-color: #d1d5db;
}

.info-row {
  display: flex;
  margin-bottom: 0.75rem;
}

.info-label {
  width: 120px;
  font-weight: 500;
  color: var(--text-muted);
}

.info-value {
  font-weight: 500;
  color: var(--text-primary);
}

/* Advanced settings button */
.advanced-settings-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 12px;
  width: 100%;
  justify-content: center;
}

.advanced-settings-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  border-color: var(--accent-color);
}

.advanced-settings-button svg {
  color: var(--text-muted);
}

.advanced-settings-button:hover svg {
  color: var(--accent-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
  }

  .sidebar {
    display: none;
  }

  .dashboard-content {
    padding: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .card {
    margin-bottom: 1rem;
  }
}