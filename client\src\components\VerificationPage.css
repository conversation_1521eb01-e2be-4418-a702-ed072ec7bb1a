.verification-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.verification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.verification-header-content h2 {
  font-size: 1.8rem;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.verification-subtitle {
  color: #666;
  font-size: 1rem;
}

.verification-security-badge {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  gap: 0.75rem;
}

.security-icon {
  color: #4CAF50;
}

.security-text {
  display: flex;
  flex-direction: column;
}

.security-text span {
  font-weight: 600;
  color: #1a1a1a;
}

.security-text small {
  color: #666;
  font-size: 0.8rem;
}

.verification-status-indicator {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  border-radius: 6px;
  background: #ffffff;
}

.status-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.status-value {
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.status-value.verified {
  background: #e8f5e9;
  color: #2e7d32;
}

.status-value.pending {
  background: #fff3e0;
  color: #e65100;
}

.verification-progress-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.verification-progress-bar::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #eaeaea;
  transform: translateY(-50%);
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
}

.progress-step.completed .step-indicator {
  background: #4CAF50;
  color: #ffffff;
}

.progress-step.active .step-indicator {
  background: #2196F3;
  color: #ffffff;
  transform: scale(1.1);
}

.step-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #eaeaea;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.verification-step {
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.verification-step h3 {
  font-size: 1.4rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.verification-step p {
  color: #666;
  margin-bottom: 1.5rem;
}

.verification-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #1a1a1a;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #eaeaea;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2196F3;
}

.verification-button {
  padding: 0.75rem 1.5rem;
  background: #2196F3;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.verification-button:hover {
  background: #1976D2;
}

.verification-button:disabled {
  background: #bdbdbd;
  cursor: not-allowed;
}

.verification-error {
  color: #d32f2f;
  background: #ffebee;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.image-preview {
  margin-top: 1rem;
  max-width: 300px;
  border-radius: 6px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: auto;
  display: block;
}

.verification-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.verification-success,
.verification-failed {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.verification-success {
  background: #e8f5e9;
}

.verification-failed {
  background: #ffebee;
}

.success-icon,
.failed-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.success-icon {
  background: #4CAF50;
  color: #ffffff;
}

.failed-icon {
  background: #d32f2f;
  color: #ffffff;
}

.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.camera-modal-content {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.18);
  max-width: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  }

.camera-modal-content h3 {
  margin-bottom: 1rem;
}

.camera-modal-content video {
  border-radius: 8px;
  background: #000;
  margin-bottom: 1rem;
  }

.camera-modal-content .verification-button.secondary {
  background: #bdbdbd;
  color: #fff;
}
