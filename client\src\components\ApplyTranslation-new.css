/* Container and Layout */
.apply-translation-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 1rem;
}

.apply-translation {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 2rem;
  top: 2rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-5px);
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Progress Bar */
.progress-bar {
  padding: 2rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.progress-track {
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  transition: width 0.5s ease;
}

.progress-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e9ecef;
  color: #6c757d;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step.active .step-circle {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.step.completed .step-circle {
  background: #28a745;
  color: white;
}

.step-label {
  text-align: center;
}

.step-title {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.step.active .step-title {
  color: #667eea;
}

.step-number {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Form Container */
.form-container {
  padding: 2rem;
  min-height: 500px;
}

.step-content {
  max-width: 800px;
  margin: 0 auto;
}

.step-header {
  text-align: center;
  margin-bottom: 2rem;
}

.step-header h2 {
  color: #2c3e50;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.step-header p {
  color: #6c757d;
  font-size: 1.1rem;
  margin: 0;
}

/* Form Elements */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  color: #2c3e50;
  font-weight: 600;
  font-size: 1rem;
}

.select-wrapper,
.input-wrapper,
.textarea-wrapper {
  position: relative;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  color: #2c3e50;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.input-suffix {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-weight: 500;
}

.char-count {
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.error-message {
  display: block;
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Document Type Cards */
.document-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.document-type-card {
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.document-type-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.document-type-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.document-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.document-label {
  font-weight: 600;
  font-size: 0.9rem;
}

/* Urgency Options */
.urgency-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.urgency-card {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.urgency-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.urgency-card.selected {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.urgency-label {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.urgency-desc {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.urgency-multiplier {
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0.9;
}

/* Additional Services */
.additional-services {
  margin-top: 2rem;
}

.additional-services h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.service-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.service-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-card.selected {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

.service-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.service-info {
  flex: 1;
}

.service-label {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.service-desc {
  font-size: 0.85rem;
  opacity: 0.8;
}

.service-price {
  font-weight: 600;
  font-size: 0.9rem;
}

/* Price Summary */
.price-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.5rem;
  border-radius: 12px;
  margin-top: 2rem;
}

.price-breakdown {
  max-width: 400px;
  margin: 0 auto;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
}

.price-item:last-child {
  border-bottom: none;
}

.price-item.total {
  font-weight: 700;
  font-size: 1.2rem;
  color: #667eea;
  border-top: 2px solid #667eea;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.total-amount {
  color: #667eea;
  font-size: 1.3rem;
}

/* File Upload */
.file-upload-area {
  margin-top: 1rem;
}

.file-input {
  display: none;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  border: 3px dashed #dee2e6;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.file-upload-label:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #6c757d;
}

.upload-text {
  text-align: center;
}

.upload-hint {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

/* Uploaded Files */
.uploaded-files {
  margin-top: 1.5rem;
}

.uploaded-files h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.file-size {
  font-size: 0.85rem;
  color: #6c757d;
}

.remove-file {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-file:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Contact Methods */
.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.contact-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.contact-method:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.contact-method.selected {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.method-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.method-label {
  font-weight: 600;
  font-size: 0.9rem;
}

/* Review Summary */
.review-summary {
  max-width: 800px;
  margin: 0 auto;
}

.summary-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.summary-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.summary-item {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1rem;
}

.pricing-details {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
}

.pricing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.pricing-item:last-child {
  border-bottom: none;
}

.pricing-item.total {
  font-weight: 700;
  font-size: 1.2rem;
  color: #667eea;
  border-top: 2px solid #667eea;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.total-price {
  color: #667eea;
  font-size: 1.4rem;
  font-weight: 700;
}

.error-banner {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #f5c6cb;
}

/* Form Navigation */
.form-navigation {
  background: #f8f9fa;
  padding: 2rem;
  border-top: 1px solid #e9ecef;
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.nav-spacer {
  flex: 1;
}

.btn-primary,
.btn-secondary,
.btn-submit {
  padding: 1rem 2rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: white;
  color: #6c757d;
  border: 2px solid #e9ecef;
}

.btn-secondary:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.btn-submit {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-submit:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-primary:disabled,
.btn-secondary:disabled,
.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.step-indicator {
  text-align: center;
  color: #6c757d;
  font-size: 0.9rem;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .apply-translation-container {
    padding: 1rem 0.5rem;
  }

  .page-header {
    padding: 1.5rem 1rem;
  }

  .back-button {
    left: 1rem;
    top: 1rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .progress-bar {
    padding: 1.5rem 1rem;
  }

  .progress-steps {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .step {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }

  .step-circle {
    width: 50px;
    height: 50px;
    margin-right: 1rem;
    margin-bottom: 0;
  }

  .form-container {
    padding: 1.5rem 1rem;
  }

  .step-header h2 {
    font-size: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .document-type-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .urgency-options {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .contact-methods {
    grid-template-columns: repeat(3, 1fr);
  }

  .nav-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-primary,
  .btn-secondary,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.5rem;
  }

  .step-circle {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .document-type-grid {
    grid-template-columns: 1fr;
  }

  .contact-methods {
    grid-template-columns: 1fr;
  }

  .summary-section {
    padding: 1.5rem;
  }
}
