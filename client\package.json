{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.82", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "chart.js": "^4.4.9", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "set PORT=3000 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "proxy": "http://localhost:5002", "homepage": ".", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}