:root {
  --primary-color: #4a90e2;
  --primary-hover: #357abd;
  --secondary-color: #2c3e50;
  --background-dark: #1a1a1a;
  --background-darker: #121212;
  --background-light: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --border-color: #333333;
  --success-color: #2ecc71;
  --error-color: #e74c3c;
  --transition-speed: 0.3s;
  --border-radius: 12px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.contact-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: var(--background-dark);
  color: var(--text-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.contact-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: fadeIn 0.5s ease-out;
}

.contact-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--primary-color), var(--success-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.contact-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.info-card {
  background-color: var(--background-darker);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  text-align: center;
  transition: all var(--transition-speed) ease;
  border: 2px solid transparent;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
  border-color: var(--primary-color);
}

.info-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.info-card h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.info-card p {
  color: var(--text-secondary);
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.contact-form-container {
  background-color: var(--background-darker);
  padding: 2rem;
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  background-color: var(--background-dark);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-speed) ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.2);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  border-color: var(--primary-color);
}

.submit-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: bold;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-hover);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-loader {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

.error-message,
.success-message {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: slideIn 0.3s ease-out;
}

.error-message {
  background-color: var(--error-color);
  color: white;
}

.success-message {
  background-color: var(--success-color);
  color: white;
}

.error-message button,
.success-message button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.5rem;
}

.contact-map {
  margin-top: 3rem;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
  }

  .contact-info {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .contact-container {
    padding: 1rem;
    margin: 1rem;
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .contact-header h1 {
    font-size: 2rem;
  }

  .contact-form-container {
    padding: 1.5rem;
  }
} 