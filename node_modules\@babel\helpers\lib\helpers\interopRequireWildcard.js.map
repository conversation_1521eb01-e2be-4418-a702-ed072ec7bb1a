{"version": 3, "names": ["_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "_interopRequireWildcard", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "__proto__", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set"], "sources": ["../../src/helpers/interopRequireWildcard.ts"], "sourcesContent": ["/* @minVersion 7.14.0 */\n\nfunction _getRequireWildcardCache(nodeInterop: boolean) {\n  if (typeof WeakMap !== \"function\") return null;\n\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  // @ts-expect-error assign to function\n  return (_getRequireWildcardCache = function (nodeInterop: boolean) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\n\nexport default function _interopRequireWildcard(\n  obj: any,\n  nodeInterop: boolean,\n) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n\n  if (obj === null || (typeof obj !== \"object\" && typeof obj !== \"function\")) {\n    return { default: obj };\n  }\n\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n\n  var newObj: { [key: string]: any } = { __proto__: null };\n  var hasPropertyDescriptor =\n    // @ts-expect-error check if Object.defineProperty is available\n    (Object.defineProperty && Object.getOwnPropertyDescriptor) as\n      | typeof Object.getOwnPropertyDescriptor\n      | undefined;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor\n        ? Object.getOwnPropertyDescriptor(obj, key)\n        : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\n"], "mappings": ";;;;;;AAEA,SAASA,wBAAwBA,CAACC,WAAoB,EAAE;EACtD,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAE9C,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EACrC,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAEpC,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAoB,EAAE;IACjE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAC3D,CAAC,EAAEF,WAAW,CAAC;AACjB;AAEe,SAASI,uBAAuBA,CAC7CC,GAAQ,EACRL,WAAoB,EACpB;EACA,IAAI,CAACA,WAAW,IAAIK,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IACzC,OAAOD,GAAG;EACZ;EAEA,IAAIA,GAAG,KAAK,IAAI,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAW,EAAE;IAC1E,OAAO;MAAEE,OAAO,EAAEF;IAAI,CAAC;EACzB;EAEA,IAAIG,KAAK,GAAGT,wBAAwB,CAACC,WAAW,CAAC;EACjD,IAAIQ,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACJ,GAAG,CAAC,EAAE;IAC3B,OAAOG,KAAK,CAACE,GAAG,CAACL,GAAG,CAAC;EACvB;EAEA,IAAIM,MAA8B,GAAG;IAAEC,SAAS,EAAE;EAAK,CAAC;EACxD,IAAIC,qBAAqB,GAEtBC,MAAM,CAACC,cAAc,IAAID,MAAM,CAACE,wBAEpB;EACf,KAAK,IAAIC,GAAG,IAAIZ,GAAG,EAAE;IACnB,IAAIY,GAAG,KAAK,SAAS,IAAIH,MAAM,CAACI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACf,GAAG,EAAEY,GAAG,CAAC,EAAE;MACvE,IAAII,IAAI,GAAGR,qBAAqB,GAC5BC,MAAM,CAACE,wBAAwB,CAACX,GAAG,EAAEY,GAAG,CAAC,GACzC,IAAI;MACR,IAAII,IAAI,KAAKA,IAAI,CAACX,GAAG,IAAIW,IAAI,CAACC,GAAG,CAAC,EAAE;QAClCR,MAAM,CAACC,cAAc,CAACJ,MAAM,EAAEM,GAAG,EAAEI,IAAI,CAAC;MAC1C,CAAC,MAAM;QACLV,MAAM,CAACM,GAAG,CAAC,GAAGZ,GAAG,CAACY,GAAG,CAAC;MACxB;IACF;EACF;EACAN,MAAM,CAACJ,OAAO,GAAGF,GAAG;EACpB,IAAIG,KAAK,EAAE;IACTA,KAAK,CAACc,GAAG,CAACjB,GAAG,EAAEM,MAAM,CAAC;EACxB;EACA,OAAOA,MAAM;AACf", "ignoreList": []}