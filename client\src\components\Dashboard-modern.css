/* Modern styles for Find Translator section */
.find-translator-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Search bar styles */
.search-bar {
  width: 100%;
  margin-bottom: 1rem;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input-container svg {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.clear-search {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

/* Filter section styles */
.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.language-pair-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Price range slider */
.price-range-slider {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.price-slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: var(--border-color);
  border-radius: 3px;
  outline: none;
}

.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.price-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.price-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.price-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.price-range-values {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* View toggle and sort dropdown */
.view-toggle {
  display: flex;
  gap: 0.25rem;
  margin-right: 1rem;
}

.view-toggle-btn {
  padding: 0.5rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-toggle-btn.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.view-toggle-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.sort-dropdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
}

.sort-dropdown label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.sort-select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.sort-select:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* Active filters display */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.active-filters-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.35rem 0.75rem;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.filter-tag button {
  background: none;
  border: none;
  color: var(--accent-color);
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 0.25rem;
  transition: all 0.2s ease;
}

.filter-tag button:hover {
  color: var(--accent-color-dark);
  transform: scale(1.2);
}

.clear-all-filters {
  padding: 0.35rem 0.75rem;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-filters:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.clear-filters-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-select {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  padding-right: 2.5rem;
}

.language-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.language-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.rating-filter {
  display: flex;
  gap: 1rem;
}

.rating-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.rating-option input {
  cursor: pointer;
}

.filter-actions {
  display: flex;
  gap: 0.75rem;
  margin-left: auto;
  align-self: flex-end;
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button {
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.search-button:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-2px);
}

.translator-results {
  display: grid;
  gap: 1.5rem;
}

.translator-results.grid-view {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.translator-results.list-view {
  grid-template-columns: 1fr;
}

.translator-results.list-view .translator-card {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-areas:
    "header header header"
    "specialties specialties specialties"
    "description stats actions";
  align-items: center;
}

.translator-results.list-view .translator-header {
  grid-area: header;
}

.translator-results.list-view .translator-specialties {
  grid-area: specialties;
}

.translator-results.list-view .translator-description {
  grid-area: description;
  border-right: 1px solid var(--border-color);
  border-bottom: none;
  height: 100%;
}

.translator-results.list-view .translator-stats {
  grid-area: stats;
  border-bottom: none;
  padding: 1rem 2rem;
}

.translator-results.list-view .translator-actions {
  grid-area: actions;
  flex-direction: column;
  padding: 1rem;
}

.translator-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  position: relative;
}

.translator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color-light);
}

.translator-header {
  display: flex;
  gap: 1rem;
  padding: 1.25rem;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}

.translator-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  flex-shrink: 0;
  overflow: hidden;
}

.translator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.translator-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.translator-info h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.translator-languages {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.translator-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stars {
  color: #f59e0b;
  letter-spacing: 0.1em;
}

.rating-count {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.translator-verified-badge {
  position: absolute;
  top: 1.25rem;
  right: 1.25rem;
  color: #10b981;
}

.translator-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem 1.25rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.specialty-tag {
  padding: 0.35rem 0.75rem;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.translator-description {
  padding: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
}

.translator-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  padding: 1rem 1.25rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.translator-actions {
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
  background-color: var(--bg-secondary);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--accent-color-light);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  text-align: center;
  gap: 1rem;
}

.empty-state-icon {
  color: var(--text-muted);
  opacity: 0.7;
}

.empty-state h3 {
  margin: 0;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0;
  max-width: 400px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 0.5rem;
  color: #ef4444;
  margin-bottom: 1.5rem;
  width: 100%;
}

.error-icon {
  flex-shrink: 0;
  color: #ef4444;
}

.error-message p {
  margin: 0;
  font-size: 0.95rem;
}

/* Modern styles for Translation Requests section */
.requests-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.requests-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.filter-tab {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.filter-tab:hover {
  color: var(--text-primary);
  background-color: var(--hover-bg);
}

.filter-tab.active {
  color: var(--accent-color);
  background-color: var(--accent-color-light);
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: var(--accent-color);
  border-radius: 3px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding-top: 0.5rem;
}

.filter-select {
  flex: 1;
  min-width: 200px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  padding-right: 2.5rem;
}

.filter-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.requests-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.request-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
}

.request-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color-light);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background: linear-gradient(to right, var(--accent-color-light), rgba(255, 255, 255, 0.05));
  border-bottom: 1px solid var(--border-color);
}

.request-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.request-price {
  font-weight: 700;
  color: var(--accent-color);
  background-color: var(--accent-color-light);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 1.1rem;
}

.request-details {
  padding: 1.25rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-weight: 500;
  color: var(--text-primary);
}

.request-description {
  padding: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
  border-bottom: 1px solid var(--border-color);
}

.request-actions {
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
  background-color: var(--bg-secondary);
}

.action-button {
  padding: 0.75rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex: 1;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.action-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.action-button-primary {
  background-color: var(--accent-color);
  color: black;
  border: none;
}

.action-button-primary:hover {
  background-color: var(--accent-color-dark);
  color: black;
  transform: translateY(-2px);
}

/* Modern styles for Client Messages section */
.messages-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 0;
  height: 70vh;
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.messages-sidebar {
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

.messages-search {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  padding-left: 2.5rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 0.75rem center;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.conversation-item:hover {
  background-color: var(--hover-bg);
}

.conversation-item.active {
  background-color: var(--accent-color-light);
}

.conversation-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--accent-color);
}

.conversation-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  flex-shrink: 0;
}

.conversation-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.conversation-name {
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-preview {
  font-size: 0.85rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.messages-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-weight: 600;
  color: var(--text-primary);
}

.contact-status {
  font-size: 0.8rem;
  color: #10b981;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.contact-status::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
}

.messages-actions {
  display: flex;
  gap: 0.5rem;
}

.message-action-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.message-action-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.messages-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--bg-primary);
}

.message-date {
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-muted);
  margin: 0.5rem 0;
  position: relative;
}

.message-date::before,
.message-date::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background-color: var(--border-color);
}

.message-date::before {
  left: 0;
}

.message-date::after {
  right: 0;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 75%;
  position: relative;
}

.message.received {
  align-self: flex-start;
}

.message.sent {
  align-self: flex-end;
}

.message-content {
  padding: 1rem;
  border-radius: 1rem;
  position: relative;
}

.message.received .message-content {
  background-color: var(--bg-secondary);
  border-bottom-left-radius: 0.25rem;
  color: var(--text-primary);
}

.message.sent .message-content {
  background-color: var(--accent-color);
  border-bottom-right-radius: 0.25rem;
  color: white;
}

.message-content p {
  margin: 0;
  line-height: 1.5;
}

.message-time {
  font-size: 0.7rem;
  margin-top: 0.25rem;
  align-self: flex-end;
  color: var(--text-muted);
}

.message.sent .message-time {
  color: var(--text-secondary);
}

.messages-footer {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.message-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 1.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover {
  background-color: var(--accent-color-dark);
  transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .messages-container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .messages-sidebar {
    display: none;
  }

  .requests-list {
    grid-template-columns: 1fr;
  }
}

.modern-calculator {
  display: flex;
  gap: 2.5rem;
  align-items: flex-start;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.calculator-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 2rem 2.5rem;
  min-width: 340px;
  max-width: 400px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.calculator-form .form-group label {
  font-weight: 600;
  color: #222;
  margin-bottom: 0.5rem;
}

.calculator-form .form-control {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.calculator-form .form-hint {
  color: #888;
  font-size: 0.9rem;
  margin-top: -0.5rem;
  margin-bottom: 0.5rem;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-label {
  font-size: 1rem;
  color: #444;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.calculate-button {
  margin-top: 1rem;
  width: 100%;
  font-size: 1.1rem;
  padding: 0.9rem 0;
  border-radius: 8px;
  background: var(--accent-color, #009688);
  color: #000;
  border: none;
  font-weight: 600;
  transition: background 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

.calculate-button:hover {
  background: var(--accent-color-dark, #00796b);
}

.price-result {
  flex: 1;
  min-width: 320px;
  max-width: 400px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.modern-price-card {
  background: #f8f9fa;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 2.5rem 2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.2rem;
}

.price-value {
  font-size: 2.2rem;
  color: #009688;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.price-breakdown {
  width: 100%;
  margin: 1rem 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  font-size: 1rem;
  color: #444;
}

.price-info {
  color: #888;
  font-size: 0.95rem;
  text-align: center;
}

.price-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  width: 100%;
  justify-content: center;
}

@media (max-width: 900px) {
  .modern-calculator {
    flex-direction: column;
    gap: 1.5rem;
  }
  .calculator-form, .price-result {
    max-width: 100%;
    min-width: 0;
  }
}

/* Translator Profile Modal Styles */
.profile-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

.profile-modal {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2rem;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  max-width: 1200px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.profile-modal-header {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 10;
}

.profile-modal-close {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profile-modal-close:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.profile-modal-content {
  overflow-y: auto;
  max-height: 90vh;
}

.profile-modal-hero {
  position: relative;
  padding: 3rem 3rem 2rem;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(168, 85, 247, 0.1) 50%,
    rgba(236, 72, 153, 0.1) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: heroBackgroundFloat 6s ease-in-out infinite;
}

@keyframes heroBackgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

.profile-hero-content {
  position: relative;
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.profile-avatar-modal {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: avatarFloat 4s ease-in-out infinite;
}

@keyframes avatarFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-5px) scale(1.02); }
}

.profile-avatar-modal img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.verified-badge-modal {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: linear-gradient(145deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4); }
  50% { transform: scale(1.1); box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6); }
}

.profile-hero-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.profile-modal-name {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1f2937, #4f46e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: nameShimmer 3s ease-in-out infinite;
}

@keyframes nameShimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.profile-modal-languages {
  font-size: 1.2rem;
  color: #6b7280;
  font-weight: 500;
  opacity: 0.9;
}

.profile-modal-rating {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stars-modal {
  color: #f59e0b;
  letter-spacing: 0.1em;
  font-size: 1.4rem;
  filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
}

.rating-text-modal {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
}

.profile-modal-location {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
}

.profile-modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.modal-action-btn {
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  border: none;
}

.modal-action-btn.primary {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow:
    0 10px 25px rgba(79, 70, 229, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.modal-action-btn.primary:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 15px 35px rgba(79, 70, 229, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.modal-action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: #4b5563;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.modal-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.modal-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modal-action-btn:hover::before {
  left: 100%;
}

.profile-modal-body {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  padding: 2rem 3rem 3rem;
}

.profile-modal-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-modal-section {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  animation: sectionSlideUp 0.6s ease-out;
}

@keyframes sectionSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-modal-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.section-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title svg {
  color: var(--accent-color);
  filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.2));
}

.profile-modal-description {
  color: #4b5563;
  line-height: 1.8;
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

.specializations-modal {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.specialty-tag {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
  border: 1px solid rgba(79, 70, 229, 0.2);
  border-radius: 2rem;
  color: #4f46e5;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.specialty-tag:hover {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(124, 58, 237, 0.2));
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.2);
}

.specialty-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.specialty-tag:hover::before {
  left: 100%;
}

.reviews-modal {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-modal-item {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.review-modal-item:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.review-modal-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.review-modal-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.review-modal-info {
  flex: 1;
}

.review-modal-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.review-modal-stars {
  color: #f59e0b;
  font-size: 0.9rem;
}

.review-modal-date {
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: 500;
}

.review-modal-text {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
  font-style: italic;
}

.profile-modal-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stats-modal-card,
.availability-modal-card,
.experience-modal-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  animation: sidebarSlideIn 0.8s ease-out;
}

@keyframes sidebarSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stats-modal-card:hover,
.availability-modal-card:hover,
.experience-modal-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.sidebar-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
}

.stats-modal-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-modal-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.stat-modal-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.02);
}

.stat-modal-icon {
  color: var(--accent-color);
  flex-shrink: 0;
}

.stat-modal-content {
  flex: 1;
}

.stat-modal-value {
  font-weight: 700;
  color: #1f2937;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.stat-modal-label {
  font-size: 0.8rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

.availability-modal-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-modal-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-modal-indicator.available {
  background: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2); }
  50% { box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.1); }
}

.status-modal-text {
  font-weight: 600;
  color: #10b981;
}

.availability-modal-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.availability-modal-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #4b5563;
  font-size: 0.9rem;
  font-weight: 500;
}

.experience-modal-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.experience-modal-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.experience-modal-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(5px);
}

.experience-modal-icon {
  color: var(--accent-color);
  flex-shrink: 0;
}

.experience-modal-content {
  flex: 1;
}

.experience-modal-label {
  font-size: 0.8rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.experience-modal-value {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.95rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.stat-value {
  font-weight: 700;
  color: var(--accent-color);
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.availability-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.available {
  background-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.availability-info p {
  margin: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.review-item {
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.review-rating {
  margin-bottom: 0.5rem;
}

.review-rating .stars {
  color: #f59e0b;
  font-size: 0.9rem;
}

.review-text {
  margin: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  font-style: italic;
}

.review-author {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
}

.view-all-reviews {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-reviews:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

/* Responsive design for modal */
@media (max-width: 1024px) {
  .profile-modal {
    max-width: 95vw;
    max-height: 95vh;
  }

  .profile-modal-body {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .profile-modal-sidebar {
    order: -1;
  }

  .stats-modal-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .profile-modal-overlay {
    padding: 1rem;
  }

  .profile-modal {
    border-radius: 1.5rem;
  }

  .profile-modal-hero {
    padding: 2rem 2rem 1.5rem;
  }

  .profile-hero-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .profile-modal-actions {
    flex-direction: column;
    width: 100%;
  }

  .modal-action-btn {
    justify-content: center;
  }

  .profile-modal-body {
    padding: 1.5rem 2rem 2rem;
  }

  .stats-modal-grid {
    grid-template-columns: 1fr 1fr;
  }

  .profile-modal-name {
    font-size: 2rem;
  }

  .profile-modal-close {
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .profile-modal-overlay {
    padding: 0.5rem;
  }

  .profile-modal {
    max-height: 98vh;
  }

  .profile-modal-hero {
    padding: 1.5rem;
  }

  .profile-modal-body {
    padding: 1rem 1.5rem 1.5rem;
  }

  .profile-modal-section {
    padding: 1.5rem;
  }

  .stats-modal-card,
  .availability-modal-card,
  .experience-modal-card {
    padding: 1rem;
  }

  .profile-avatar-modal {
    width: 100px;
    height: 100px;
  }

  .profile-modal-name {
    font-size: 1.8rem;
  }

  .specializations-modal {
    gap: 0.5rem;
  }

  .specialty-tag {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

/* Help & Setup Options */
.help-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.help-option-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  color: #4b5563;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.help-option-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  color: var(--accent-color);
}

.help-option-btn svg {
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.help-option-btn:hover svg {
  transform: scale(1.1);
}