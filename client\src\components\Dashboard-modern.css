/* Modern styles for Find Translator section */
.find-translator-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Search bar styles */
.search-bar {
  width: 100%;
  margin-bottom: 1rem;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input-container svg {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.clear-search {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

/* Filter section styles */
.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.language-pair-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Price range slider */
.price-range-slider {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.price-slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: var(--border-color);
  border-radius: 3px;
  outline: none;
}

.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.price-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.price-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.price-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.price-range-values {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* View toggle and sort dropdown */
.view-toggle {
  display: flex;
  gap: 0.25rem;
  margin-right: 1rem;
}

.view-toggle-btn {
  padding: 0.5rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-toggle-btn.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.view-toggle-btn:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.sort-dropdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
}

.sort-dropdown label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.sort-select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.sort-select:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* Active filters display */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.active-filters-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.35rem 0.75rem;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.filter-tag button {
  background: none;
  border: none;
  color: var(--accent-color);
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 0.25rem;
  transition: all 0.2s ease;
}

.filter-tag button:hover {
  color: var(--accent-color-dark);
  transform: scale(1.2);
}

.clear-all-filters {
  padding: 0.35rem 0.75rem;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-filters:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.clear-filters-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-select {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  padding-right: 2.5rem;
}

.language-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.language-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.rating-filter {
  display: flex;
  gap: 1rem;
}

.rating-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.rating-option input {
  cursor: pointer;
}

.filter-actions {
  display: flex;
  gap: 0.75rem;
  margin-left: auto;
  align-self: flex-end;
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button {
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.search-button:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-2px);
}

.translator-results {
  display: grid;
  gap: 1.5rem;
}

.translator-results.grid-view {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.translator-results.list-view {
  grid-template-columns: 1fr;
}

.translator-results.list-view .translator-card {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-areas:
    "header header header"
    "specialties specialties specialties"
    "description stats actions";
  align-items: center;
}

.translator-results.list-view .translator-header {
  grid-area: header;
}

.translator-results.list-view .translator-specialties {
  grid-area: specialties;
}

.translator-results.list-view .translator-description {
  grid-area: description;
  border-right: 1px solid var(--border-color);
  border-bottom: none;
  height: 100%;
}

.translator-results.list-view .translator-stats {
  grid-area: stats;
  border-bottom: none;
  padding: 1rem 2rem;
}

.translator-results.list-view .translator-actions {
  grid-area: actions;
  flex-direction: column;
  padding: 1rem;
}

.translator-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  position: relative;
}

.translator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color-light);
}

.translator-header {
  display: flex;
  gap: 1rem;
  padding: 1.25rem;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}

.translator-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  flex-shrink: 0;
  overflow: hidden;
}

.translator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.translator-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.translator-info h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.translator-languages {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.translator-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stars {
  color: #f59e0b;
  letter-spacing: 0.1em;
}

.rating-count {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.translator-verified-badge {
  position: absolute;
  top: 1.25rem;
  right: 1.25rem;
  color: #10b981;
}

.translator-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem 1.25rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.specialty-tag {
  padding: 0.35rem 0.75rem;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.translator-description {
  padding: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
}

.translator-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  padding: 1rem 1.25rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.translator-actions {
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
  background-color: var(--bg-secondary);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--accent-color-light);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  text-align: center;
  gap: 1rem;
}

.empty-state-icon {
  color: var(--text-muted);
  opacity: 0.7;
}

.empty-state h3 {
  margin: 0;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0;
  max-width: 400px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 0.5rem;
  color: #ef4444;
  margin-bottom: 1.5rem;
  width: 100%;
}

.error-icon {
  flex-shrink: 0;
  color: #ef4444;
}

.error-message p {
  margin: 0;
  font-size: 0.95rem;
}

/* Modern styles for Translation Requests section */
.requests-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.requests-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.filter-tab {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.filter-tab:hover {
  color: var(--text-primary);
  background-color: var(--hover-bg);
}

.filter-tab.active {
  color: var(--accent-color);
  background-color: var(--accent-color-light);
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: var(--accent-color);
  border-radius: 3px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding-top: 0.5rem;
}

.filter-select {
  flex: 1;
  min-width: 200px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  padding-right: 2.5rem;
}

.filter-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.requests-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.request-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
}

.request-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color-light);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background: linear-gradient(to right, var(--accent-color-light), rgba(255, 255, 255, 0.05));
  border-bottom: 1px solid var(--border-color);
}

.request-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.request-price {
  font-weight: 700;
  color: var(--accent-color);
  background-color: var(--accent-color-light);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 1.1rem;
}

.request-details {
  padding: 1.25rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-weight: 500;
  color: var(--text-primary);
}

.request-description {
  padding: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  flex-grow: 1;
  border-bottom: 1px solid var(--border-color);
}

.request-actions {
  padding: 1.25rem;
  display: flex;
  gap: 1rem;
  background-color: var(--bg-secondary);
}

.action-button {
  padding: 0.75rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex: 1;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.action-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.action-button-primary {
  background-color: var(--accent-color);
  color: black;
  border: none;
}

.action-button-primary:hover {
  background-color: var(--accent-color-dark);
  color: black;
  transform: translateY(-2px);
}

/* Modern styles for Client Messages section */
.messages-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 0;
  height: 70vh;
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.messages-sidebar {
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

.messages-search {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  padding-left: 2.5rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 0.75rem center;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.conversation-item:hover {
  background-color: var(--hover-bg);
}

.conversation-item.active {
  background-color: var(--accent-color-light);
}

.conversation-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--accent-color);
}

.conversation-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  flex-shrink: 0;
}

.conversation-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.conversation-name {
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-preview {
  font-size: 0.85rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.messages-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-weight: 600;
  color: var(--text-primary);
}

.contact-status {
  font-size: 0.8rem;
  color: #10b981;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.contact-status::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
}

.messages-actions {
  display: flex;
  gap: 0.5rem;
}

.message-action-button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.message-action-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.messages-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--bg-primary);
}

.message-date {
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-muted);
  margin: 0.5rem 0;
  position: relative;
}

.message-date::before,
.message-date::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background-color: var(--border-color);
}

.message-date::before {
  left: 0;
}

.message-date::after {
  right: 0;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 75%;
  position: relative;
}

.message.received {
  align-self: flex-start;
}

.message.sent {
  align-self: flex-end;
}

.message-content {
  padding: 1rem;
  border-radius: 1rem;
  position: relative;
}

.message.received .message-content {
  background-color: var(--bg-secondary);
  border-bottom-left-radius: 0.25rem;
  color: var(--text-primary);
}

.message.sent .message-content {
  background-color: var(--accent-color);
  border-bottom-right-radius: 0.25rem;
  color: white;
}

.message-content p {
  margin: 0;
  line-height: 1.5;
}

.message-time {
  font-size: 0.7rem;
  margin-top: 0.25rem;
  align-self: flex-end;
  color: var(--text-muted);
}

.message.sent .message-time {
  color: var(--text-secondary);
}

.messages-footer {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.message-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 1.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-color-light);
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover {
  background-color: var(--accent-color-dark);
  transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .messages-container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .messages-sidebar {
    display: none;
  }

  .requests-list {
    grid-template-columns: 1fr;
  }
}

.modern-calculator {
  display: flex;
  gap: 2.5rem;
  align-items: flex-start;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.calculator-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 2rem 2.5rem;
  min-width: 340px;
  max-width: 400px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.calculator-form .form-group label {
  font-weight: 600;
  color: #222;
  margin-bottom: 0.5rem;
}

.calculator-form .form-control {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.calculator-form .form-hint {
  color: #888;
  font-size: 0.9rem;
  margin-top: -0.5rem;
  margin-bottom: 0.5rem;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-label {
  font-size: 1rem;
  color: #444;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.calculate-button {
  margin-top: 1rem;
  width: 100%;
  font-size: 1.1rem;
  padding: 0.9rem 0;
  border-radius: 8px;
  background: var(--accent-color, #009688);
  color: #000;
  border: none;
  font-weight: 600;
  transition: background 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

.calculate-button:hover {
  background: var(--accent-color-dark, #00796b);
}

.price-result {
  flex: 1;
  min-width: 320px;
  max-width: 400px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.modern-price-card {
  background: #f8f9fa;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 2.5rem 2rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.2rem;
}

.price-value {
  font-size: 2.2rem;
  color: #009688;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.price-breakdown {
  width: 100%;
  margin: 1rem 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  font-size: 1rem;
  color: #444;
}

.price-info {
  color: #888;
  font-size: 0.95rem;
  text-align: center;
}

.price-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  width: 100%;
  justify-content: center;
}

@media (max-width: 900px) {
  .modern-calculator {
    flex-direction: column;
    gap: 1.5rem;
  }
  .calculator-form, .price-result {
    max-width: 100%;
    min-width: 0;
  }
}

/* Translator Profile Styles */
.profile-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.back-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
  transform: translateX(-2px);
}

.translator-profile-container {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  margin-top: 1rem;
}

.profile-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.profile-header-section {
  display: flex;
  gap: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, var(--accent-color-light), rgba(255, 255, 255, 0.05));
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.profile-avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: var(--accent-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.verified-badge-large {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: #10b981;
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.profile-info h1 {
  margin: 0;
  font-size: 2rem;
  color: var(--text-primary);
  font-weight: 700;
}

.profile-languages {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.profile-rating {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stars-large {
  color: #f59e0b;
  letter-spacing: 0.1em;
  font-size: 1.5rem;
}

.rating-text {
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-self: flex-start;
}

.profile-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.profile-section h3 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--text-primary);
  font-weight: 600;
  border-bottom: 2px solid var(--accent-color-light);
  padding-bottom: 0.5rem;
}

.profile-description {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1rem;
  margin: 0;
}

.specializations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.specialty-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.specialty-card:hover {
  background-color: var(--accent-color-light);
  border-color: var(--accent-color);
  transform: translateY(-2px);
}

.specialty-icon {
  color: var(--accent-color);
  flex-shrink: 0;
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.experience-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.experience-item:hover {
  background-color: var(--hover-bg);
  transform: translateY(-2px);
}

.experience-icon {
  color: var(--accent-color);
  flex-shrink: 0;
}

.experience-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.experience-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stats-card,
.availability-card,
.reviews-card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.stats-card h3,
.availability-card h3,
.reviews-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: var(--text-primary);
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.stat-value {
  font-weight: 700;
  color: var(--accent-color);
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.availability-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.available {
  background-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.availability-info p {
  margin: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.review-item {
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.review-rating {
  margin-bottom: 0.5rem;
}

.review-rating .stars {
  color: #f59e0b;
  font-size: 0.9rem;
}

.review-text {
  margin: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  font-style: italic;
}

.review-author {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
}

.view-all-reviews {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-reviews:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

/* Responsive design for profile */
@media (max-width: 1024px) {
  .translator-profile-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .profile-sidebar {
    order: -1;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .profile-header-section {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .profile-actions {
    align-self: center;
    flex-direction: row;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .specializations-grid,
  .experience-grid {
    grid-template-columns: 1fr;
  }
}
