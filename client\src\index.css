:root {
  /* Base font settings */
  --base-font-size: 1rem;
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;

  /* Animation settings */
  --transition-speed: 0.3s;
  --animation-speed-fast: 0.5s;
  --animation-speed-medium: 0.8s;
  --animation-speed-slow: 1.2s;
  --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-rgb: 37, 99, 235;
  --accent-color-rgb: 32, 178, 170; /* Default turquoise in RGB format */
  --success-color: #22c55e;
  --border-color: #e5e7eb;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --bg-card: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-input: #ffffff;
  --bg-hover: #f3f4f6;
}

[dir="rtl"] .settings-tab-list {
  flex-direction: row-reverse;
}

[data-sidebar-position="right"] .dashboard-content {
  flex-direction: row-reverse;
}

.compact-mode .dashboard-card,
.compact-mode .settings-section,
.compact-mode .translation-item {
  padding: 12px;
  margin-bottom: 12px;
}

.compact-mode .dashboard-header,
.compact-mode .dashboard-nav {
  padding: 8px;
}

body {
  margin: 0;
  font-family: var(--font-family);
  font-size: var(--base-font-size);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%;
  max-width: 100vw;
}

/* Disable animations when needed */
.no-animations *,
.no-animations *::before,
.no-animations *::after {
  transition: none !important;
  animation: none !important;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

[data-theme='dark'] {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --success-color: #22c55e;
  --border-color: #374151;
  --text-primary: #000000;
  --text-secondary: #333333;
  --bg-card: #1f2937;
  --bg-secondary: #111827;
  --bg-input: #374151;
  --bg-hover: #2d3748;
  /* Dark theme specific shadow for welcome banner */
  --welcome-banner-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}
