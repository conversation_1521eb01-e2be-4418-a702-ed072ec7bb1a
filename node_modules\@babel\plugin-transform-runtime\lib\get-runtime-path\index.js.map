{"version": 3, "names": ["_path", "require", "_default", "moduleName", "dirname", "absoluteRuntime", "resolveAbsoluteRuntime", "path", "resolve", "v", "w", "split", "process", "versions", "node", "r", "paths", "b", "M", "f", "_findPath", "_nodeModulePaths", "concat", "Error", "code", "replace", "err", "Object", "assign", "runtime", "resolveFSPath"], "sources": ["../../src/get-runtime-path/index.ts"], "sourcesContent": ["import path from \"path\";\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import.meta.url);\n\nexport default function (\n  moduleName: string,\n  dirname: string,\n  absoluteRuntime: string | boolean,\n) {\n  if (absoluteRuntime === false) return moduleName;\n\n  return resolveAbsoluteRuntime(\n    moduleName,\n    path.resolve(dirname, absoluteRuntime === true ? \".\" : absoluteRuntime),\n  );\n}\n\nfunction resolveAbsoluteRuntime(moduleName: string, dirname: string) {\n  try {\n    return path\n      .dirname(\n        require.resolve(`${moduleName}/package.json`, { paths: [dirname] }),\n      )\n      .replace(/\\\\/g, \"/\");\n  } catch (err) {\n    if (err.code !== \"MODULE_NOT_FOUND\") throw err;\n\n    throw Object.assign(\n      new Error(`Failed to resolve \"${moduleName}\" relative to \"${dirname}\"`),\n      {\n        code: \"BABEL_RUNTIME_NOT_FOUND\",\n        runtime: moduleName,\n        dirname,\n      },\n    );\n  }\n}\n\nexport function resolveFSPath(path: string) {\n  return require.resolve(path).replace(/\\\\/g, \"/\");\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEAA,OAAA;AAGe,SAAAC,SACbC,UAAkB,EAClBC,OAAe,EACfC,eAAiC,EACjC;EACA,IAAIA,eAAe,KAAK,KAAK,EAAE,OAAOF,UAAU;EAEhD,OAAOG,sBAAsB,CAC3BH,UAAU,EACVI,KAAI,CAACC,OAAO,CAACJ,OAAO,EAAEC,eAAe,KAAK,IAAI,GAAG,GAAG,GAAGA,eAAe,CACxE,CAAC;AACH;AAEA,SAASC,sBAAsBA,CAACH,UAAkB,EAAEC,OAAe,EAAE;EACnE,IAAI;IACF,OAAOG,KAAI,CACRH,OAAO,CACN,GAAAK,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,WAAAb,OAAA,CAAAO,OAAA,IAAAO,CAAA;MAAAC,KAAA,GAAAC,CAAA;IAAA,GAAAC,CAAA,GAAAjB,OAAA;MAAA,IAAAkB,CAAA,GAAAD,CAAA,CAAAE,SAAA,CAAAL,CAAA,EAAAG,CAAA,CAAAG,gBAAA,CAAAJ,CAAA,EAAAK,MAAA,CAAAL,CAAA;MAAA,IAAAE,CAAA,SAAAA,CAAA;MAAAA,CAAA,OAAAI,KAAA,2BAAAR,CAAA;MAAAI,CAAA,CAAAK,IAAA;MAAA,MAAAL,CAAA;IAAA,GAAgB,GAAGhB,UAAU,eAAe,EAAE;MAAEa,KAAK,EAAE,CAACZ,OAAO;IAAE,CAAC,CACpE,CAAC,CACAqB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACxB,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,IAAIA,GAAG,CAACF,IAAI,KAAK,kBAAkB,EAAE,MAAME,GAAG;IAE9C,MAAMC,MAAM,CAACC,MAAM,CACjB,IAAIL,KAAK,CAAC,sBAAsBpB,UAAU,kBAAkBC,OAAO,GAAG,CAAC,EACvE;MACEoB,IAAI,EAAE,yBAAyB;MAC/BK,OAAO,EAAE1B,UAAU;MACnBC;IACF,CACF,CAAC;EACH;AACF;AAEO,SAAS0B,aAAaA,CAACvB,IAAY,EAAE;EAC1C,OAAON,OAAO,CAACO,OAAO,CAACD,IAAI,CAAC,CAACkB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAClD", "ignoreList": []}