.card {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 6px var(--shadow-color);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
}

.card-hoverable {
  cursor: pointer;
}

.card-hoverable:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px var(--shadow-color);
}

.card-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.card-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--accent-color-light);
  color: var(--accent-color);
}

.card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* Card grid layout */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Card with accent color */
.card-accent-primary {
  border-top: 4px solid var(--primary);
}

.card-accent-success {
  border-top: 4px solid var(--success-color);
}

.card-accent-warning {
  border-top: 4px solid var(--warning-color);
}

.card-accent-error {
  border-top: 4px solid var(--error-color);
}

.card-accent-info {
  border-top: 4px solid var(--info-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}
