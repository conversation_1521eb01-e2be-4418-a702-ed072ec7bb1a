/* Celebration Banner Styles */
.celebration-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--gradient-bg);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: celebrationSlideDown 0.6s ease-out;
  overflow: hidden;
}

.celebration-banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.celebration-banner-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.celebration-banner-emoji {
  font-size: 2.5rem;
  animation: celebrationBounce 2s infinite;
}

.celebration-banner-text h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

.celebration-banner-text p {
  margin: 0.25rem 0 0 0;
  font-size: 0.95rem;
  opacity: 0.9;
  color: white;
}

.celebration-banner-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.celebration-banner-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.celebration-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

/* Celebration Animations */
@keyframes celebrationSlideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes celebrationBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes celebrationFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes celebrationSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes celebrationPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

/* Decoration Elements */
.decoration {
  position: absolute;
  pointer-events: none;
}

.decoration-snowflakes::before,
.decoration-snowflakes::after {
  content: '❄️';
  position: absolute;
  font-size: 1.5rem;
  animation: celebrationFloat 3s infinite ease-in-out;
}

.decoration-snowflakes::before {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-snowflakes::after {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.decoration-christmas-lights::before,
.decoration-christmas-lights::after {
  content: '🎄';
  position: absolute;
  font-size: 1.2rem;
  animation: celebrationPulse 2s infinite;
}

.decoration-christmas-lights::before {
  top: 15%;
  left: 20%;
}

.decoration-christmas-lights::after {
  top: 25%;
  right: 25%;
  animation-delay: 1s;
}

.decoration-hearts::before,
.decoration-hearts::after {
  content: '💕';
  position: absolute;
  font-size: 1.3rem;
  animation: celebrationFloat 2.5s infinite ease-in-out;
}

.decoration-hearts::before {
  top: 10%;
  left: 15%;
}

.decoration-hearts::after {
  top: 20%;
  right: 20%;
  animation-delay: 1.2s;
}

.decoration-flowers::before,
.decoration-flowers::after {
  content: '🌸';
  position: absolute;
  font-size: 1.4rem;
  animation: celebrationSpin 4s infinite linear;
}

.decoration-flowers::before {
  top: 12%;
  left: 18%;
}

.decoration-flowers::after {
  top: 18%;
  right: 22%;
  animation-delay: 2s;
}

.decoration-confetti::before,
.decoration-confetti::after {
  content: '🎊';
  position: absolute;
  font-size: 1.6rem;
  animation: celebrationBounce 1.5s infinite;
}

.decoration-confetti::before {
  top: 8%;
  left: 12%;
}

.decoration-confetti::after {
  top: 22%;
  right: 18%;
  animation-delay: 0.7s;
}

.decoration-fireworks::before,
.decoration-fireworks::after {
  content: '🎆';
  position: absolute;
  font-size: 1.5rem;
  animation: celebrationPulse 1.8s infinite;
}

.decoration-fireworks::before {
  top: 5%;
  left: 25%;
}

.decoration-fireworks::after {
  top: 15%;
  right: 30%;
  animation-delay: 0.9s;
}

/* Celebration Controls in Settings */
.celebration-controls {
  margin-top: 1rem;
}

.celebration-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.celebration-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  width: 50px;
  height: 26px;
  background: #ccc;
  border-radius: 13px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.celebration-toggle input:checked + .toggle-slider {
  background: var(--primary-color);
}

.celebration-toggle input:checked + .toggle-slider::before {
  transform: translateX(24px);
}

.toggle-label {
  font-weight: 600;
  color: var(--text-color);
}

.celebration-description {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.celebration-demos h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-color);
}

.celebration-demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
}

.celebration-demo-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.celebration-demo-btn:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--accent-color-rgb), 0.15);
}

.celebration-emoji {
  font-size: 1.5rem;
  animation: celebrationBounce 2s infinite;
}

.celebration-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .celebration-banner {
    padding: 0.75rem 1rem;
  }
  
  .celebration-banner-emoji {
    font-size: 2rem;
  }
  
  .celebration-banner-text h3 {
    font-size: 1.1rem;
  }
  
  .celebration-banner-text p {
    font-size: 0.85rem;
  }
  
  .celebration-demo-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }
  
  .celebration-demo-btn {
    padding: 0.5rem;
  }
  
  .celebration-emoji {
    font-size: 1.3rem;
  }
  
  .celebration-name {
    font-size: 0.75rem;
  }
}
