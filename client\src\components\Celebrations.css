/* Full Celebration System - Google-style */
.celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9999;
  overflow: hidden;
}

/* Celebration Banner Styles */
.celebration-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--gradient-bg);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: celebrationSlideDown 0.6s ease-out;
  overflow: hidden;
  pointer-events: auto;
}

/* Animated Header Logo for Celebrations */
.celebration-header-logo {
  position: relative;
  display: inline-block;
  animation: celebrationLogoBounce 2s infinite ease-in-out;
}

.celebration-header-logo::before {
  content: '';
  position: absolute;
  top: -10px;
  right: -10px;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #FFD700, #FFA500);
  border-radius: 50%;
  animation: celebrationSparkle 1.5s infinite;
}

/* Falling Particles System */
.celebration-particles {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9998;
  overflow: hidden;
}

.particle {
  position: absolute;
  pointer-events: none;
  user-select: none;
  font-size: 1.5rem;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

/* Christmas Particles */
.particle.snowflake {
  animation: snowfall 8s linear infinite;
  color: #E6F3FF;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.particle.christmas-tree {
  animation: gentleFall 12s linear infinite;
  color: #228B22;
  filter: drop-shadow(0 0 5px #32CD32);
}

.particle.santa {
  animation: santaFly 15s linear infinite;
  font-size: 2rem;
}

/* Valentine's Particles */
.particle.heart {
  animation: heartFloat 6s ease-in-out infinite;
  color: #FF69B4;
  filter: drop-shadow(0 0 8px #FF1493);
}

.particle.rose {
  animation: petalFall 10s linear infinite;
  color: #DC143C;
}

/* Women's Day Particles */
.particle.flower {
  animation: flowerSpin 8s linear infinite;
  color: #FF69B4;
  filter: drop-shadow(0 0 6px #FFB6C1);
}

.particle.butterfly {
  animation: butterflyFly 12s ease-in-out infinite;
  color: #9370DB;
  font-size: 1.8rem;
}

/* Halloween Particles */
.particle.pumpkin {
  animation: pumpkinBounce 4s ease-in-out infinite;
  color: #FF8C00;
  filter: drop-shadow(0 0 10px #FF4500);
}

.particle.ghost {
  animation: ghostFloat 6s ease-in-out infinite;
  color: #F8F8FF;
  opacity: 0.8;
}

.particle.bat {
  animation: batFly 8s linear infinite;
  color: #2F4F4F;
  font-size: 1.3rem;
}

/* New Year Particles */
.particle.confetti {
  animation: confettiFall 5s linear infinite;
  font-size: 1rem;
}

.particle.firework {
  animation: fireworkExplode 3s ease-out infinite;
  font-size: 2rem;
}

.particle.star {
  animation: starTwinkle 2s ease-in-out infinite;
  color: #FFD700;
  filter: drop-shadow(0 0 8px #FFA500);
}

.celebration-banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.celebration-banner-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.celebration-banner-emoji {
  font-size: 2.5rem;
  animation: celebrationBounce 2s infinite;
}

.celebration-banner-text h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

.celebration-banner-text p {
  margin: 0.25rem 0 0 0;
  font-size: 0.95rem;
  opacity: 0.9;
  color: white;
}

.celebration-banner-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.celebration-banner-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.celebration-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

/* Advanced Celebration Animations */
@keyframes celebrationSlideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes celebrationLogoBounce {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  75% {
    transform: scale(1.1) rotate(5deg);
  }
}

@keyframes celebrationSparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Particle Animations */
@keyframes snowfall {
  0% {
    transform: translateY(-100vh) translateX(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes gentleFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(180deg);
    opacity: 0.5;
  }
}

@keyframes santaFly {
  0% {
    transform: translateX(-100px) translateY(20vh);
  }
  50% {
    transform: translateX(50vw) translateY(10vh);
  }
  100% {
    transform: translateX(100vw) translateY(20vh);
  }
}

@keyframes heartFloat {
  0%, 100% {
    transform: translateY(0) scale(1) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-20px) scale(1.1) rotate(-10deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-40px) scale(1.2) rotate(0deg);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-20px) scale(1.1) rotate(10deg);
    opacity: 1;
  }
}

@keyframes petalFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

@keyframes flowerSpin {
  0% {
    transform: translateY(-100vh) rotate(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(50vh) rotate(180deg) scale(1.2);
    opacity: 0.9;
  }
  100% {
    transform: translateY(100vh) rotate(360deg) scale(0.8);
    opacity: 0;
  }
}

@keyframes butterflyFly {
  0% {
    transform: translateX(-100px) translateY(30vh) rotate(0deg);
  }
  25% {
    transform: translateX(25vw) translateY(20vh) rotate(10deg);
  }
  50% {
    transform: translateX(50vw) translateY(40vh) rotate(-5deg);
  }
  75% {
    transform: translateX(75vw) translateY(25vh) rotate(15deg);
  }
  100% {
    transform: translateX(100vw) translateY(35vh) rotate(0deg);
  }
}

@keyframes pumpkinBounce {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-30px) rotate(-10deg);
  }
  50% {
    transform: translateY(-50px) rotate(0deg);
  }
  75% {
    transform: translateY(-30px) rotate(10deg);
  }
}

@keyframes ghostFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.9;
  }
}

@keyframes batFly {
  0% {
    transform: translateX(-100px) translateY(10vh) scaleX(1);
  }
  25% {
    transform: translateX(25vw) translateY(5vh) scaleX(-1);
  }
  50% {
    transform: translateX(50vw) translateY(15vh) scaleX(1);
  }
  75% {
    transform: translateX(75vw) translateY(8vh) scaleX(-1);
  }
  100% {
    transform: translateX(100vw) translateY(12vh) scaleX(1);
  }
}

@keyframes confettiFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(1080deg);
    opacity: 0;
  }
}

@keyframes fireworkExplode {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.5) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.5) rotate(360deg);
    opacity: 0;
  }
}

@keyframes starTwinkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.3) rotate(180deg);
    opacity: 1;
  }
}

@keyframes celebrationBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes celebrationFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes celebrationSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes celebrationPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

/* Decoration Elements */
.decoration {
  position: absolute;
  pointer-events: none;
}

.decoration-snowflakes::before,
.decoration-snowflakes::after {
  content: '❄️';
  position: absolute;
  font-size: 1.5rem;
  animation: celebrationFloat 3s infinite ease-in-out;
}

.decoration-snowflakes::before {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-snowflakes::after {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.decoration-christmas-lights::before,
.decoration-christmas-lights::after {
  content: '🎄';
  position: absolute;
  font-size: 1.2rem;
  animation: celebrationPulse 2s infinite;
}

.decoration-christmas-lights::before {
  top: 15%;
  left: 20%;
}

.decoration-christmas-lights::after {
  top: 25%;
  right: 25%;
  animation-delay: 1s;
}

.decoration-hearts::before,
.decoration-hearts::after {
  content: '💕';
  position: absolute;
  font-size: 1.3rem;
  animation: celebrationFloat 2.5s infinite ease-in-out;
}

.decoration-hearts::before {
  top: 10%;
  left: 15%;
}

.decoration-hearts::after {
  top: 20%;
  right: 20%;
  animation-delay: 1.2s;
}

.decoration-flowers::before,
.decoration-flowers::after {
  content: '🌸';
  position: absolute;
  font-size: 1.4rem;
  animation: celebrationSpin 4s infinite linear;
}

.decoration-flowers::before {
  top: 12%;
  left: 18%;
}

.decoration-flowers::after {
  top: 18%;
  right: 22%;
  animation-delay: 2s;
}

.decoration-confetti::before,
.decoration-confetti::after {
  content: '🎊';
  position: absolute;
  font-size: 1.6rem;
  animation: celebrationBounce 1.5s infinite;
}

.decoration-confetti::before {
  top: 8%;
  left: 12%;
}

.decoration-confetti::after {
  top: 22%;
  right: 18%;
  animation-delay: 0.7s;
}

.decoration-fireworks::before,
.decoration-fireworks::after {
  content: '🎆';
  position: absolute;
  font-size: 1.5rem;
  animation: celebrationPulse 1.8s infinite;
}

.decoration-fireworks::before {
  top: 5%;
  left: 25%;
}

.decoration-fireworks::after {
  top: 15%;
  right: 30%;
  animation-delay: 0.9s;
}

/* Celebration Controls in Settings */
.celebration-controls {
  margin-top: 1rem;
}

.celebration-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.celebration-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  width: 50px;
  height: 26px;
  background: #ccc;
  border-radius: 13px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.celebration-toggle input:checked + .toggle-slider {
  background: var(--primary-color);
}

.celebration-toggle input:checked + .toggle-slider::before {
  transform: translateX(24px);
}

.toggle-label {
  font-weight: 600;
  color: var(--text-color);
}

.celebration-description {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.celebration-demos h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-color);
}

.celebration-demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
}

.celebration-demo-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.celebration-demo-btn:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--accent-color-rgb), 0.15);
}

.celebration-emoji {
  font-size: 1.5rem;
  animation: celebrationBounce 2s infinite;
}

.celebration-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-color);
}

/* Interactive Celebration Elements */
.celebration-interactive {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  pointer-events: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 2rem;
  padding: 2rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: celebrationPopIn 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: center;
  max-width: 400px;
}

@keyframes celebrationPopIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.celebration-interactive h2 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  background: var(--gradient-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.celebration-interactive-emoji {
  font-size: 4rem;
  animation: celebrationBounce 2s infinite;
  display: block;
  margin-bottom: 1rem;
}

.celebration-interactive-message {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.celebration-interactive-button {
  background: var(--gradient-bg);
  color: white;
  border: none;
  border-radius: 1rem;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(var(--accent-color-rgb), 0.3);
}

.celebration-interactive-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--accent-color-rgb), 0.4);
}

/* Special Effects */
.celebration-fireworks {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9997;
}

.firework-burst {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: fireworkBurst 2s ease-out forwards;
}

@keyframes fireworkBurst {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(20);
    opacity: 0.8;
  }
  100% {
    transform: scale(40);
    opacity: 0;
  }
}

/* Header Celebration Indicator */
.header-celebration-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(var(--accent-color-rgb), 0.1);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  margin-right: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(var(--accent-color-rgb), 0.2);
  animation: celebrationPulse 2s infinite;
}

.header-celebration-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(var(--accent-color-rgb), 0.3);
}

.header-celebration-emoji {
  font-size: 1.2rem;
  position: relative;
  z-index: 2;
  animation: celebrationBounce 2s infinite ease-in-out;
}

/* Celebration Sound Effects Indicator (Legacy - kept for compatibility) */
.celebration-sound-indicator {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 10001;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.celebration-sound-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.celebration-sound-waves {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  animation: soundWaves 2s infinite;
}

@keyframes soundWaves {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Header Logo Animation */
.dashboard-logo.celebration-active {
  animation: celebrationLogoBounce 2s infinite ease-in-out;
}

.dashboard-logo.celebration-active::after {
  content: '✨';
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 1.2rem;
  animation: celebrationSparkle 1.5s infinite;
}

/* Celebration Background Effects */
.celebration-background-effect {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9996;
  background: radial-gradient(circle at 50% 50%,
    rgba(var(--accent-color-rgb), 0.05) 0%,
    transparent 70%);
  animation: backgroundPulse 4s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Celebration Cursor Trail (Legacy - replaced by optimized version) */

/* Celebration Sub-Options */
.celebration-sub-option {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(var(--accent-color-rgb), 0.05);
  border-radius: 0.75rem;
  border: 1px solid rgba(var(--accent-color-rgb), 0.1);
}

.celebration-sub-option .celebration-toggle {
  margin-bottom: 0.5rem;
}

.sub-option-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
  font-style: italic;
}

/* Optimized Cursor Trail */
.celebration-cursor-trail {
  position: fixed;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9995;
  background: var(--gradient-bg);
  animation: cursorTrailOptimized 0.8s ease-out forwards;
  will-change: transform, opacity;
}

@keyframes cursorTrailOptimized {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.3);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .celebration-banner {
    padding: 0.75rem 1rem;
  }

  .celebration-banner-emoji {
    font-size: 2rem;
  }

  .celebration-banner-text h3 {
    font-size: 1.1rem;
  }

  .celebration-banner-text p {
    font-size: 0.85rem;
  }

  .celebration-demo-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
  }

  .celebration-demo-btn {
    padding: 0.5rem;
  }

  .celebration-emoji {
    font-size: 1.3rem;
  }

  .celebration-name {
    font-size: 0.75rem;
  }

  .celebration-interactive {
    max-width: 90vw;
    padding: 1.5rem;
  }

  .celebration-interactive-emoji {
    font-size: 3rem;
  }

  .particle {
    font-size: 1.2rem;
  }

  .celebration-sound-indicator {
    width: 40px;
    height: 40px;
    top: 0.5rem;
    right: 0.5rem;
  }

  .header-celebration-indicator {
    width: 32px;
    height: 32px;
    margin-right: 0.25rem;
  }

  .header-celebration-emoji {
    font-size: 1rem;
  }

  .celebration-sub-option {
    padding: 0.75rem;
    margin: 0.75rem 0;
  }

  .sub-option-description {
    font-size: 0.8rem;
  }
}
