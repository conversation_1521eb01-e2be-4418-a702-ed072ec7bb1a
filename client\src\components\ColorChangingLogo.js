import React, { useEffect, useState } from 'react';

const ColorChangingLogo = ({ className, alt = "LingoLink", size = 'medium' }) => {
  const [accentColor, setAccentColor] = useState('#20B2AA'); // Default turquoise color
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [textColor, setTextColor] = useState('#000000'); // Default text color

  // Set size based on prop
  const sizeMap = {
    small: { width: '32px', height: '32px' },
    medium: { width: '60px', height: '60px' },
    large: { width: '100px', height: '100px' }
  };

  const dimensions = sizeMap[size] || sizeMap.medium;

  // Listen for theme changes
  useEffect(() => {
    // Get initial theme
    const theme = document.documentElement.getAttribute('data-theme') || 'light';
    setIsDarkMode(theme === 'dark');
    setTextColor('#000000');

    // Get initial accent color
    const rootStyles = getComputedStyle(document.documentElement);
    const currentAccentColor = rootStyles.getPropertyValue('--accent-color').trim();
    if (currentAccentColor) {
      setAccentColor(currentAccentColor);
    }

    // Create a MutationObserver to watch for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          const newTheme = document.documentElement.getAttribute('data-theme');
          setIsDarkMode(newTheme === 'dark');
          setTextColor('#000000');
        }
      });
    });

    // Start observing the document element for attribute changes
    observer.observe(document.documentElement, { attributes: true });

    // Create a function to check for CSS variable changes
    const checkForColorChanges = () => {
      const rootStyles = getComputedStyle(document.documentElement);
      const newAccentColor = rootStyles.getPropertyValue('--accent-color').trim();
      if (newAccentColor && newAccentColor !== accentColor) {
        setAccentColor(newAccentColor);
      }
    };

    // Set up an interval to check for color changes
    const intervalId = setInterval(checkForColorChanges, 1000);

    // Clean up
    return () => {
      observer.disconnect();
      clearInterval(intervalId);
    };
  }, [accentColor]);

  return (
    <div className={`color-changing-logo ${className || ''}`} style={dimensions}>
      <svg
        id="svg"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width="100%"
        height="100%"
        viewBox="0, 0, 400, 264.9874055415617"
        aria-label={alt}
      >
        {/* Use the current accent color for the logo */}
        <style>
          {`
            .logo-primary-fill { fill: ${accentColor}; }
            .logo-secondary-fill { fill: ${textColor}; }
          `}
        </style>

        <g id="svgg">
          <path
            id="path0"
            className="logo-secondary-fill"
            d="M167.817 49.801 C 167.466 49.899,166.834 50.405,166.413 50.926 L 165.647 51.872 165.665 64.432 L 165.683 76.993 166.672 78.012 C 167.880 79.258,169.870 80.216,171.621 80.395 C 173.323 80.570,173.540 80.914,173.714 83.711 C 173.936 87.281,173.777 169.728,173.548 170.191 C 173.342 170.607,170.047 172.017,169.190 172.056 C 168.453 172.089,166.696 173.159,166.141 173.911 C 165.444 174.855,165.374 199.812,166.067 200.200 C 166.288 200.323,166.468 200.589,166.468 200.791 C 166.468 200.992,166.695 201.229,166.974 201.318 C 167.252 201.406,167.546 201.652,167.628 201.866 C 167.758 202.203,173.414 202.253,211.734 202.253 L 255.691 202.253 256.739 201.180 L 257.787 200.107 257.787 180.525 L 257.787 160.944 257.124 160.226 C 255.839 158.833,255.447 158.781,246.270 158.781 C 235.579 158.781,234.653 159.122,234.164 163.246 C 234.083 163.924,233.857 165.017,233.661 165.673 C 233.465 166.329,233.207 167.343,233.087 167.926 C 232.676 169.929,232.793 169.913,218.688 169.911 C 211.909 169.909,205.909 169.833,205.356 169.742 C 203.441 169.425,203.579 172.894,203.579 125.046 L 203.579 82.008 204.320 81.298 C 204.833 80.806,205.464 80.537,206.374 80.421 C 207.096 80.330,207.951 80.090,208.274 79.889 C 208.597 79.688,209.088 79.523,209.364 79.523 C 209.641 79.523,210.450 78.980,211.163 78.316 L 212.459 77.109 212.459 64.598 L 212.459 52.087 211.841 51.222 C 210.600 49.481,211.795 49.567,189.079 49.596 C 177.736 49.610,168.169 49.702,167.817 49.801"
          />
        </g>
      </svg>
    </div>
  );
};

export default ColorChangingLogo;
