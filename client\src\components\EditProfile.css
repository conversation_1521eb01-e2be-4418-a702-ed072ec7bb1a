.edit-profile-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.edit-profile-container h2 {
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.edit-profile-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-image-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.profile-image-upload {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: var(--hover-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  transition: all 0.2s ease;
  border: 2px dashed var(--border-color);
}

.profile-image-upload:hover {
  opacity: 0.9;
  border-color: var(--accent-color);
}

.upload-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--text-muted);
}

.image-upload-text {
  display: flex;
  flex-direction: column;
}

.image-upload-text p {
  margin: 0;
  font-weight: 500;
  color: var(--text-primary);
}

.image-upload-text span {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.form-section {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px var(--shadow-color);
}

.form-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 1.25rem;
  color: var(--text-primary);
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(32, 178, 170, 0.15);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #e53e3e;
}

.error-message {
  color: #e53e3e;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.submit-error {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: rgba(229, 62, 62, 0.1);
  border-radius: 0.375rem;
  text-align: center;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-label input {
  width: auto;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.cancel-button,
.save-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.cancel-button:hover {
  background-color: var(--hover-bg);
}

.save-button {
  background-color: var(--accent-color);
  border: none;
  color: white;
}

.save-button:hover {
  background-color: var(--accent-color-dark);
}

.save-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .checkbox-group {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}
