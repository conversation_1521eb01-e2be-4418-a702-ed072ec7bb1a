<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions for gradients and filters -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8f9fa"/>
      <stop offset="100%" stop-color="#e9ecef"/>
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#30D5C8"/>
      <stop offset="100%" stop-color="#20B2AA"/>
    </linearGradient>
    
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.15"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="white"/>
  
  <!-- Main content area -->
  <g transform="translate(300, 200)">
    <!-- Central connecting element -->
    <g filter="url(#dropShadow)">
      <circle cx="0" cy="0" r="50" fill="white" stroke="#30D5C8" stroke-width="2"/>
      
      <!-- Simple logo/icon -->
      <g transform="translate(0, 0)">
        <path d="M-20,-20 L-20,20 L20,20 L20,-20 Z" fill="none" stroke="#30D5C8" stroke-width="2.5" stroke-linejoin="round"/>
        <line x1="-15" y1="0" x2="15" y2="0" stroke="#30D5C8" stroke-width="2.5"/>
        <line x1="-15" y1="-10" x2="15" y2="-10" stroke="#30D5C8" stroke-width="1.5"/>
        <line x1="-15" y1="10" x2="15" y2="10" stroke="#30D5C8" stroke-width="1.5"/>
      </g>
    </g>
    
    <!-- Connection lines -->
    <path d="M-50,0 L-140,0" stroke="#30D5C8" stroke-width="2" stroke-dasharray="4,2"/>
    <path d="M50,0 L140,0" stroke="#30D5C8" stroke-width="2" stroke-dasharray="4,2"/>
    
    <!-- Left side: Client with legal document -->
    <g transform="translate(-190, 0)" filter="url(#dropShadow)">
      <rect x="-50" y="-70" width="100" height="140" rx="5" ry="5" fill="white"/>
      
      <!-- Simple document icon -->
      <g transform="translate(0, 0)">
        <rect x="-30" y="-40" width="60" height="80" fill="#f8f9fa" stroke="#ddd"/>
        <line x1="-20" y1="-25" x2="20" y2="-25" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="-10" x2="20" y2="-10" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="5" x2="20" y2="5" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="20" x2="10" y2="20" stroke="#666" stroke-width="1"/>
        
        <!-- Legal stamp/seal -->
        <circle cx="15" cy="25" r="10" fill="none" stroke="#30D5C8" stroke-width="1"/>
        <path d="M10,25 L20,25 M15,20 L15,30" stroke="#30D5C8" stroke-width="1"/>
      </g>
    </g>
    
    <!-- Right side: Translated document -->
    <g transform="translate(190, 0)" filter="url(#dropShadow)">
      <rect x="-50" y="-70" width="100" height="140" rx="5" ry="5" fill="white"/>
      
      <!-- Simple translated document -->
      <g transform="translate(0, 0)">
        <rect x="-30" y="-40" width="60" height="80" fill="#f8f9fa" stroke="#ddd"/>
        <line x1="-20" y1="-25" x2="20" y2="-25" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="-10" x2="20" y2="-10" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="5" x2="20" y2="5" stroke="#666" stroke-width="1"/>
        <line x1="-20" y1="20" x2="10" y2="20" stroke="#666" stroke-width="1"/>
        
        <!-- Certification mark -->
        <g transform="translate(15, 25)">
          <path d="M-5,-5 L0,5 L5,-5" fill="none" stroke="#30D5C8" stroke-width="1.5"/>
          <circle cx="0" cy="0" r="10" fill="none" stroke="#30D5C8" stroke-width="1"/>
        </g>
      </g>
    </g>
  </g>
  
  <!-- Simple decorative elements -->
  <rect x="0" y="0" width="600" height="10" fill="url(#accentGradient)" opacity="0.2"/>
  <rect x="0" y="390" width="600" height="10" fill="url(#accentGradient)" opacity="0.2"/>
</svg>
