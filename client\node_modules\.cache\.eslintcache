[{"C:\\Users\\<USER>\\lingolink\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\lingolink\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\lingolink\\client\\src\\App.js": "3", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AuthPage.js": "4", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Dashboard.js": "5", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\DashboardTest.js": "6", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleDashboard.js": "7", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\EditProfile.js": "8", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ThemeSelector.js": "9", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Modal.js": "10", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Card.js": "11", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\NotificationManager.js": "12", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\OnboardingTutorial.js": "13", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Notification.js": "14", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LoginPage.js": "15", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AnimationSettings.js": "16", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\FontSettings.js": "17", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LayoutSettings.js": "18", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TranslatorChart.js": "19", "C:\\Users\\<USER>\\lingolink\\client\\src\\services\\translatorService.js": "20", "C:\\Users\\<USER>\\lingolink\\client\\src\\config.js": "21", "C:\\Users\\<USER>\\lingolink\\client\\src\\supabase.js": "22", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\VerificationPage.js": "23", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ApplyTranslation.js": "24", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ContactPage.js": "25", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerDecorations.js": "26", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerCustomizer.js": "27", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleAuthPage.js": "28", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ColorChangingLogo.js": "29"}, {"size": 535, "mtime": 1743682608622, "results": "30", "hashOfConfig": "31"}, {"size": 362, "mtime": 1743682608955, "results": "32", "hashOfConfig": "31"}, {"size": 26696, "mtime": 1747330717862, "results": "33", "hashOfConfig": "31"}, {"size": 58188, "mtime": 1746448784487, "results": "34", "hashOfConfig": "31"}, {"size": 164714, "mtime": 1748545450672, "results": "35", "hashOfConfig": "31"}, {"size": 4471, "mtime": 1743886909180, "results": "36", "hashOfConfig": "31"}, {"size": 1654, "mtime": 1743881759076, "results": "37", "hashOfConfig": "31"}, {"size": 9875, "mtime": 1745710992777, "results": "38", "hashOfConfig": "31"}, {"size": 5016, "mtime": 1745702900138, "results": "39", "hashOfConfig": "31"}, {"size": 2020, "mtime": 1745702359421, "results": "40", "hashOfConfig": "31"}, {"size": 811, "mtime": 1744291612413, "results": "41", "hashOfConfig": "31"}, {"size": 1245, "mtime": 1744291585611, "results": "42", "hashOfConfig": "31"}, {"size": 5807, "mtime": 1746443652985, "results": "43", "hashOfConfig": "31"}, {"size": 3260, "mtime": 1744291549883, "results": "44", "hashOfConfig": "31"}, {"size": 3298, "mtime": 1747330962007, "results": "45", "hashOfConfig": "31"}, {"size": 2958, "mtime": 1745703111532, "results": "46", "hashOfConfig": "31"}, {"size": 3239, "mtime": 1745703067243, "results": "47", "hashOfConfig": "31"}, {"size": 2751, "mtime": 1745703156121, "results": "48", "hashOfConfig": "31"}, {"size": 3326, "mtime": 1745703608964, "results": "49", "hashOfConfig": "31"}, {"size": 15663, "mtime": 1745767598508, "results": "50", "hashOfConfig": "31"}, {"size": 92, "mtime": 1745705228669, "results": "51", "hashOfConfig": "31"}, {"size": 16002, "mtime": 1745767815002, "results": "52", "hashOfConfig": "31"}, {"size": 36685, "mtime": 1746443580857, "results": "53", "hashOfConfig": "31"}, {"size": 27917, "mtime": 1748524708313, "results": "54", "hashOfConfig": "31"}, {"size": 141, "mtime": 1746447548815, "results": "55", "hashOfConfig": "31"}, {"size": 4861, "mtime": 1747319226771, "results": "56", "hashOfConfig": "31"}, {"size": 6054, "mtime": 1747319050065, "results": "57", "hashOfConfig": "31"}, {"size": 9397, "mtime": 1747324268559, "results": "58", "hashOfConfig": "31"}, {"size": 4505, "mtime": 1748523256871, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d1yd02", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\lingolink\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\App.js", ["147", "148"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AuthPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Dashboard.js", ["149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\DashboardTest.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleDashboard.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\EditProfile.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ThemeSelector.js", ["171"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Modal.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Card.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\NotificationManager.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\OnboardingTutorial.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Notification.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LoginPage.js", ["172"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AnimationSettings.js", ["173", "174"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\FontSettings.js", ["175", "176"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LayoutSettings.js", ["177"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TranslatorChart.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\services\\translatorService.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\config.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\supabase.js", ["178", "179", "180"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\VerificationPage.js", ["181"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ApplyTranslation.js", ["182", "183"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ContactPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerDecorations.js", ["184"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerCustomizer.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleAuthPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ColorChangingLogo.js", ["185"], [], {"ruleId": "186", "severity": 1, "message": "187", "line": 7, "column": 8, "nodeType": "188", "messageId": "189", "endLine": 7, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "190", "line": 14, "column": 7, "nodeType": "188", "messageId": "189", "endLine": 14, "endColumn": 14}, {"ruleId": "186", "severity": 1, "message": "191", "line": 1, "column": 38, "nodeType": "188", "messageId": "189", "endLine": 1, "endColumn": 44}, {"ruleId": "192", "severity": 1, "message": "193", "line": 87, "column": 5, "nodeType": "194", "messageId": "195", "endLine": 87, "endColumn": 15}, {"ruleId": "192", "severity": 1, "message": "196", "line": 90, "column": 5, "nodeType": "194", "messageId": "195", "endLine": 90, "endColumn": 15}, {"ruleId": "192", "severity": 1, "message": "197", "line": 91, "column": 5, "nodeType": "194", "messageId": "195", "endLine": 91, "endColumn": 14}, {"ruleId": "192", "severity": 1, "message": "198", "line": 92, "column": 5, "nodeType": "194", "messageId": "195", "endLine": 92, "endColumn": 16}, {"ruleId": "186", "severity": 1, "message": "199", "line": 402, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 402, "endColumn": 21}, {"ruleId": "186", "severity": 1, "message": "200", "line": 409, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 409, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "201", "line": 410, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 410, "endColumn": 25}, {"ruleId": "186", "severity": 1, "message": "202", "line": 411, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 411, "endColumn": 25}, {"ruleId": "186", "severity": 1, "message": "203", "line": 447, "column": 23, "nodeType": "188", "messageId": "189", "endLine": 447, "endColumn": 37}, {"ruleId": "186", "severity": 1, "message": "204", "line": 448, "column": 25, "nodeType": "188", "messageId": "189", "endLine": 448, "endColumn": 41}, {"ruleId": "186", "severity": 1, "message": "205", "line": 449, "column": 27, "nodeType": "188", "messageId": "189", "endLine": 449, "endColumn": 45}, {"ruleId": "186", "severity": 1, "message": "206", "line": 450, "column": 27, "nodeType": "188", "messageId": "189", "endLine": 450, "endColumn": 45}, {"ruleId": "207", "severity": 1, "message": "208", "line": 609, "column": 6, "nodeType": "209", "endLine": 609, "endColumn": 98, "suggestions": "210"}, {"ruleId": "186", "severity": 1, "message": "211", "line": 988, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 988, "endColumn": 32}, {"ruleId": "186", "severity": 1, "message": "212", "line": 992, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 992, "endColumn": 35}, {"ruleId": "186", "severity": 1, "message": "213", "line": 1004, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 1004, "endColumn": 29}, {"ruleId": "186", "severity": 1, "message": "214", "line": 1012, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 1012, "endColumn": 29}, {"ruleId": "186", "severity": 1, "message": "215", "line": 1050, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 1050, "endColumn": 33}, {"ruleId": "186", "severity": 1, "message": "216", "line": 1059, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 1059, "endColumn": 28}, {"ruleId": "186", "severity": 1, "message": "217", "line": 1068, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 1068, "endColumn": 34}, {"ruleId": "186", "severity": 1, "message": "218", "line": 1077, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 1077, "endColumn": 27}, {"ruleId": "207", "severity": 1, "message": "219", "line": 16, "column": 6, "nodeType": "209", "endLine": 16, "endColumn": 40, "suggestions": "220"}, {"ruleId": "186", "severity": 1, "message": "221", "line": 7, "column": 27, "nodeType": "188", "messageId": "189", "endLine": 7, "endColumn": 45}, {"ruleId": "186", "severity": 1, "message": "222", "line": 12, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 12, "endColumn": 10}, {"ruleId": "207", "severity": 1, "message": "223", "line": 19, "column": 6, "nodeType": "209", "endLine": 19, "endColumn": 42, "suggestions": "224"}, {"ruleId": "186", "severity": 1, "message": "222", "line": 12, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 12, "endColumn": 10}, {"ruleId": "207", "severity": 1, "message": "225", "line": 20, "column": 6, "nodeType": "209", "endLine": 20, "endColumn": 42, "suggestions": "226"}, {"ruleId": "186", "severity": 1, "message": "222", "line": 12, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 12, "endColumn": 10}, {"ruleId": "186", "severity": 1, "message": "227", "line": 368, "column": 13, "nodeType": "188", "messageId": "189", "endLine": 368, "endColumn": 17}, {"ruleId": "186", "severity": 1, "message": "227", "line": 482, "column": 13, "nodeType": "188", "messageId": "189", "endLine": 482, "endColumn": 17}, {"ruleId": "186", "severity": 1, "message": "228", "line": 508, "column": 19, "nodeType": "188", "messageId": "189", "endLine": 508, "endColumn": 30}, {"ruleId": "186", "severity": 1, "message": "229", "line": 50, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 50, "endColumn": 19}, {"ruleId": "230", "severity": 1, "message": "231", "line": 123, "column": 5, "nodeType": "232", "messageId": "233", "endLine": 139, "endColumn": 6}, {"ruleId": "207", "severity": 1, "message": "234", "line": 197, "column": 6, "nodeType": "209", "endLine": 197, "endColumn": 92, "suggestions": "235"}, {"ruleId": "207", "severity": 1, "message": "236", "line": 10, "column": 6, "nodeType": "209", "endLine": 10, "endColumn": 15, "suggestions": "237"}, {"ruleId": "186", "severity": 1, "message": "238", "line": 5, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 5, "endColumn": 20}, "no-unused-vars", "'SimpleDashboard' is defined but never used.", "Identifier", "unusedVar", "'API_URL' is assigned a value but never used.", "'useRef' is defined but never used.", "no-dupe-keys", "Duplicate key 'appearance'.", "ObjectExpression", "unexpected", "Duplicate key 'lightTheme'.", "Duplicate key 'darkTheme'.", "Duplicate key 'systemTheme'.", "'settingsTab' is assigned a value but never used.", "'passwordError' is assigned a value but never used.", "'passwordSuccess' is assigned a value but never used.", "'settingsChanged' is assigned a value but never used.", "'setBannerStyle' is assigned a value but never used.", "'setBannerPattern' is assigned a value but never used.", "'setBannerAnimation' is assigned a value but never used.", "'setShowDecorations' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", "ArrayExpression", ["239"], "'handleSettingsTabChange' is assigned a value but never used.", "'handleTogglePasswordChange' is assigned a value but never used.", "'handlePasswordChange' is assigned a value but never used.", "'handlePasswordSubmit' is assigned a value but never used.", "'handleNotificationChange' is assigned a value but never used.", "'handlePrivacyChange' is assigned a value but never used.", "'handleAccessibilityChange' is assigned a value but never used.", "'handleSaveSettings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'applyAccentColor'. Either include it or remove the dependency array.", ["240"], "'setCurrentLanguage' is assigned a value but never used.", "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'applyAnimationSettings'. Either include it or remove the dependency array.", ["241"], "React Hook useEffect has missing dependencies: 'applyFontFamily' and 'applyFontSize'. Either include them or remove the dependency array.", ["242"], "'data' is assigned a value but never used.", "'profileData' is assigned a value but never used.", "'faceImage' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'calculateTotal'. Either include it or remove the dependency array.", ["243"], "React Hook useEffect has a missing dependency: 'generateDecorations'. Either include it or remove the dependency array.", ["244"], "'isDarkMode' is assigned a value but never used.", {"desc": "245", "fix": "246"}, {"desc": "247", "fix": "248"}, {"desc": "249", "fix": "250"}, {"desc": "251", "fix": "252"}, {"desc": "253", "fix": "254"}, {"desc": "255", "fix": "256"}, "Update the dependencies array to be: [translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery, applyFilters]", {"range": "257", "text": "258"}, "Update the dependencies array to be: [currentTheme, currentAccentColor, applyAccentColor]", {"range": "259", "text": "260"}, "Update the dependencies array to be: [animationsEnabled, applyAnimationSettings, transitionSpeed]", {"range": "261", "text": "262"}, "Update the dependencies array to be: [currentFontSize, currentFontFamily, applyFontSize, applyFontFamily]", {"range": "263", "text": "264"}, "Update the dependencies array to be: [formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices, calculateTotal]", {"range": "265", "text": "266"}, "Update the dependencies array to be: [generateDecorations, pattern]", {"range": "267", "text": "268"}, [22836, 22928], "[translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery, applyFilters]", [641, 675], "[currentTheme, currentAccentColor, applyAccentColor]", [656, 692], "[animationsEnabled, applyAnimationSettings, transitionSpeed]", [639, 675], "[currentFontSize, currentFontFamily, applyFontSize, applyFontFamily]", [6347, 6433], "[formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices, calculateTotal]", [315, 324], "[generateDecorations, pattern]"]