[{"C:\\Users\\<USER>\\lingolink\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\lingolink\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\lingolink\\client\\src\\App.js": "3", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AuthPage.js": "4", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Dashboard.js": "5", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\DashboardTest.js": "6", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleDashboard.js": "7", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\EditProfile.js": "8", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ThemeSelector.js": "9", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Modal.js": "10", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Card.js": "11", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\NotificationManager.js": "12", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\OnboardingTutorial.js": "13", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Notification.js": "14", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LoginPage.js": "15", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AnimationSettings.js": "16", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\FontSettings.js": "17", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LayoutSettings.js": "18", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TranslatorChart.js": "19", "C:\\Users\\<USER>\\lingolink\\client\\src\\services\\translatorService.js": "20", "C:\\Users\\<USER>\\lingolink\\client\\src\\config.js": "21", "C:\\Users\\<USER>\\lingolink\\client\\src\\supabase.js": "22", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\VerificationPage.js": "23", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ApplyTranslation.js": "24", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ContactPage.js": "25", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerDecorations.js": "26", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerCustomizer.js": "27", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleAuthPage.js": "28", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ColorChangingLogo.js": "29", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ProfileCustomizationPopup.js": "30", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TutorialPopup.js": "31"}, {"size": 535, "mtime": 1743682608622, "results": "32", "hashOfConfig": "33"}, {"size": 362, "mtime": 1743682608955, "results": "34", "hashOfConfig": "33"}, {"size": 26696, "mtime": 1747330717862, "results": "35", "hashOfConfig": "33"}, {"size": 58188, "mtime": 1746448784487, "results": "36", "hashOfConfig": "33"}, {"size": 202737, "mtime": 1748551087030, "results": "37", "hashOfConfig": "33"}, {"size": 4471, "mtime": 1743886909180, "results": "38", "hashOfConfig": "33"}, {"size": 1654, "mtime": 1743881759076, "results": "39", "hashOfConfig": "33"}, {"size": 9875, "mtime": 1745710992777, "results": "40", "hashOfConfig": "33"}, {"size": 5016, "mtime": 1745702900138, "results": "41", "hashOfConfig": "33"}, {"size": 2020, "mtime": 1745702359421, "results": "42", "hashOfConfig": "33"}, {"size": 811, "mtime": 1744291612413, "results": "43", "hashOfConfig": "33"}, {"size": 1245, "mtime": 1744291585611, "results": "44", "hashOfConfig": "33"}, {"size": 5807, "mtime": 1746443652985, "results": "45", "hashOfConfig": "33"}, {"size": 3260, "mtime": 1744291549883, "results": "46", "hashOfConfig": "33"}, {"size": 3298, "mtime": 1747330962007, "results": "47", "hashOfConfig": "33"}, {"size": 2958, "mtime": 1745703111532, "results": "48", "hashOfConfig": "33"}, {"size": 3239, "mtime": 1745703067243, "results": "49", "hashOfConfig": "33"}, {"size": 2751, "mtime": 1745703156121, "results": "50", "hashOfConfig": "33"}, {"size": 3326, "mtime": 1745703608964, "results": "51", "hashOfConfig": "33"}, {"size": 15663, "mtime": 1745767598508, "results": "52", "hashOfConfig": "33"}, {"size": 92, "mtime": 1745705228669, "results": "53", "hashOfConfig": "33"}, {"size": 16002, "mtime": 1745767815002, "results": "54", "hashOfConfig": "33"}, {"size": 36685, "mtime": 1746443580857, "results": "55", "hashOfConfig": "33"}, {"size": 27917, "mtime": 1748524708313, "results": "56", "hashOfConfig": "33"}, {"size": 141, "mtime": 1746447548815, "results": "57", "hashOfConfig": "33"}, {"size": 4861, "mtime": 1747319226771, "results": "58", "hashOfConfig": "33"}, {"size": 6054, "mtime": 1747319050065, "results": "59", "hashOfConfig": "33"}, {"size": 9397, "mtime": 1747324268559, "results": "60", "hashOfConfig": "33"}, {"size": 4505, "mtime": 1748523256871, "results": "61", "hashOfConfig": "33"}, {"size": 18515, "mtime": 1748546918616, "results": "62", "hashOfConfig": "33"}, {"size": 6389, "mtime": 1748546792792, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d1yd02", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\lingolink\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\App.js", ["157", "158"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AuthPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Dashboard.js", ["159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181"], ["182"], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\DashboardTest.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleDashboard.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\EditProfile.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ThemeSelector.js", ["183"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Modal.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Card.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\NotificationManager.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\OnboardingTutorial.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Notification.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LoginPage.js", ["184"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AnimationSettings.js", ["185", "186"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\FontSettings.js", ["187", "188"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LayoutSettings.js", ["189"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TranslatorChart.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\services\\translatorService.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\config.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\supabase.js", ["190", "191", "192"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\VerificationPage.js", ["193"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ApplyTranslation.js", ["194", "195"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ContactPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerDecorations.js", ["196"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerCustomizer.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleAuthPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ColorChangingLogo.js", ["197"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ProfileCustomizationPopup.js", ["198"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TutorialPopup.js", ["199"], [], {"ruleId": "200", "severity": 1, "message": "201", "line": 7, "column": 8, "nodeType": "202", "messageId": "203", "endLine": 7, "endColumn": 23}, {"ruleId": "200", "severity": 1, "message": "204", "line": 14, "column": 7, "nodeType": "202", "messageId": "203", "endLine": 14, "endColumn": 14}, {"ruleId": "200", "severity": 1, "message": "205", "line": 1, "column": 38, "nodeType": "202", "messageId": "203", "endLine": 1, "endColumn": 44}, {"ruleId": "206", "severity": 1, "message": "207", "line": 91, "column": 5, "nodeType": "208", "messageId": "209", "endLine": 91, "endColumn": 15}, {"ruleId": "206", "severity": 1, "message": "210", "line": 94, "column": 5, "nodeType": "208", "messageId": "209", "endLine": 94, "endColumn": 15}, {"ruleId": "206", "severity": 1, "message": "211", "line": 95, "column": 5, "nodeType": "208", "messageId": "209", "endLine": 95, "endColumn": 14}, {"ruleId": "206", "severity": 1, "message": "212", "line": 96, "column": 5, "nodeType": "208", "messageId": "209", "endLine": 96, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "213", "line": 392, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 392, "endColumn": 19}, {"ruleId": "200", "severity": 1, "message": "214", "line": 412, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 412, "endColumn": 21}, {"ruleId": "200", "severity": 1, "message": "215", "line": 419, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 419, "endColumn": 23}, {"ruleId": "200", "severity": 1, "message": "216", "line": 420, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 420, "endColumn": 25}, {"ruleId": "200", "severity": 1, "message": "217", "line": 421, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 421, "endColumn": 25}, {"ruleId": "200", "severity": 1, "message": "218", "line": 457, "column": 23, "nodeType": "202", "messageId": "203", "endLine": 457, "endColumn": 37}, {"ruleId": "200", "severity": 1, "message": "219", "line": 458, "column": 25, "nodeType": "202", "messageId": "203", "endLine": 458, "endColumn": 41}, {"ruleId": "200", "severity": 1, "message": "220", "line": 459, "column": 27, "nodeType": "202", "messageId": "203", "endLine": 459, "endColumn": 45}, {"ruleId": "200", "severity": 1, "message": "221", "line": 460, "column": 27, "nodeType": "202", "messageId": "203", "endLine": 460, "endColumn": 45}, {"ruleId": "222", "severity": 1, "message": "223", "line": 1003, "column": 6, "nodeType": "224", "endLine": 1003, "endColumn": 98, "suggestions": "225"}, {"ruleId": "200", "severity": 1, "message": "226", "line": 1590, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1590, "endColumn": 32}, {"ruleId": "200", "severity": 1, "message": "227", "line": 1594, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1594, "endColumn": 35}, {"ruleId": "200", "severity": 1, "message": "228", "line": 1606, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1606, "endColumn": 29}, {"ruleId": "200", "severity": 1, "message": "229", "line": 1614, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1614, "endColumn": 29}, {"ruleId": "200", "severity": 1, "message": "230", "line": 1652, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1652, "endColumn": 33}, {"ruleId": "200", "severity": 1, "message": "231", "line": 1661, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1661, "endColumn": 28}, {"ruleId": "200", "severity": 1, "message": "232", "line": 1670, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1670, "endColumn": 34}, {"ruleId": "200", "severity": 1, "message": "233", "line": 1679, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 1679, "endColumn": 27}, {"ruleId": "222", "severity": 1, "message": "234", "line": 1216, "column": 6, "nodeType": "224", "endLine": 1216, "endColumn": 142, "suggestions": "235", "suppressions": "236"}, {"ruleId": "222", "severity": 1, "message": "237", "line": 16, "column": 6, "nodeType": "224", "endLine": 16, "endColumn": 40, "suggestions": "238"}, {"ruleId": "200", "severity": 1, "message": "239", "line": 7, "column": 27, "nodeType": "202", "messageId": "203", "endLine": 7, "endColumn": 45}, {"ruleId": "200", "severity": 1, "message": "240", "line": 12, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 12, "endColumn": 10}, {"ruleId": "222", "severity": 1, "message": "241", "line": 19, "column": 6, "nodeType": "224", "endLine": 19, "endColumn": 42, "suggestions": "242"}, {"ruleId": "200", "severity": 1, "message": "240", "line": 12, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 12, "endColumn": 10}, {"ruleId": "222", "severity": 1, "message": "243", "line": 20, "column": 6, "nodeType": "224", "endLine": 20, "endColumn": 42, "suggestions": "244"}, {"ruleId": "200", "severity": 1, "message": "240", "line": 12, "column": 9, "nodeType": "202", "messageId": "203", "endLine": 12, "endColumn": 10}, {"ruleId": "200", "severity": 1, "message": "245", "line": 368, "column": 13, "nodeType": "202", "messageId": "203", "endLine": 368, "endColumn": 17}, {"ruleId": "200", "severity": 1, "message": "245", "line": 482, "column": 13, "nodeType": "202", "messageId": "203", "endLine": 482, "endColumn": 17}, {"ruleId": "200", "severity": 1, "message": "246", "line": 508, "column": 19, "nodeType": "202", "messageId": "203", "endLine": 508, "endColumn": 30}, {"ruleId": "200", "severity": 1, "message": "247", "line": 50, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 50, "endColumn": 19}, {"ruleId": "248", "severity": 1, "message": "249", "line": 123, "column": 5, "nodeType": "250", "messageId": "251", "endLine": 139, "endColumn": 6}, {"ruleId": "222", "severity": 1, "message": "252", "line": 197, "column": 6, "nodeType": "224", "endLine": 197, "endColumn": 92, "suggestions": "253"}, {"ruleId": "222", "severity": 1, "message": "254", "line": 10, "column": 6, "nodeType": "224", "endLine": 10, "endColumn": 15, "suggestions": "255"}, {"ruleId": "200", "severity": 1, "message": "256", "line": 5, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 5, "endColumn": 20}, {"ruleId": "200", "severity": 1, "message": "257", "line": 1, "column": 27, "nodeType": "202", "messageId": "203", "endLine": 1, "endColumn": 36}, {"ruleId": "200", "severity": 1, "message": "257", "line": 1, "column": 27, "nodeType": "202", "messageId": "203", "endLine": 1, "endColumn": 36}, "no-unused-vars", "'SimpleDashboard' is defined but never used.", "Identifier", "unusedVar", "'API_URL' is assigned a value but never used.", "'useRef' is defined but never used.", "no-dupe-keys", "Duplicate key 'appearance'.", "ObjectExpression", "unexpected", "Duplicate key 'lightTheme'.", "Duplicate key 'darkTheme'.", "Duplicate key 'systemTheme'.", "'isNewUser' is assigned a value but never used.", "'settingsTab' is assigned a value but never used.", "'passwordError' is assigned a value but never used.", "'passwordSuccess' is assigned a value but never used.", "'settingsChanged' is assigned a value but never used.", "'setBannerStyle' is assigned a value but never used.", "'setBannerPattern' is assigned a value but never used.", "'setBannerAnimation' is assigned a value but never used.", "'setShowDecorations' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", "ArrayExpression", ["258"], "'handleSettingsTabChange' is assigned a value but never used.", "'handleTogglePasswordChange' is assigned a value but never used.", "'handlePasswordChange' is assigned a value but never used.", "'handlePasswordSubmit' is assigned a value but never used.", "'handleNotificationChange' is assigned a value but never used.", "'handlePrivacyChange' is assigned a value but never used.", "'handleAccessibilityChange' is assigned a value but never used.", "'handleSaveSettings' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyTheme', 'celebrationsEnabled', and 'detectCurrentCelebration'. Either include them or remove the dependency array.", ["259"], ["260"], "React Hook useEffect has a missing dependency: 'applyAccentColor'. Either include it or remove the dependency array.", ["261"], "'setCurrentLanguage' is assigned a value but never used.", "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'applyAnimationSettings'. Either include it or remove the dependency array.", ["262"], "React Hook useEffect has missing dependencies: 'applyFontFamily' and 'applyFontSize'. Either include them or remove the dependency array.", ["263"], "'data' is assigned a value but never used.", "'profileData' is assigned a value but never used.", "'faceImage' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'calculateTotal'. Either include it or remove the dependency array.", ["264"], "React Hook useEffect has a missing dependency: 'generateDecorations'. Either include it or remove the dependency array.", ["265"], "'isDarkMode' is assigned a value but never used.", "'useEffect' is defined but never used.", {"desc": "266", "fix": "267"}, {"desc": "268", "fix": "269"}, {"kind": "270", "justification": "271"}, {"desc": "272", "fix": "273"}, {"desc": "274", "fix": "275"}, {"desc": "276", "fix": "277"}, {"desc": "278", "fix": "279"}, {"desc": "280", "fix": "281"}, "Update the dependencies array to be: [translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery, applyFilters]", {"range": "282", "text": "283"}, "Update the dependencies array to be: [currentTheme, animationsEnabled, animationSpeed, compactMode, borderRadius, shadowIntensity, currentLanguage, user, refreshTranslators, applyTheme, celebrationsEnabled, detectCurrentCelebration]", {"range": "284", "text": "285"}, "directive", "", "Update the dependencies array to be: [currentTheme, currentAccentColor, applyAccentColor]", {"range": "286", "text": "287"}, "Update the dependencies array to be: [animationsEnabled, applyAnimationSettings, transitionSpeed]", {"range": "288", "text": "289"}, "Update the dependencies array to be: [currentFontSize, currentFontFamily, applyFontSize, applyFontFamily]", {"range": "290", "text": "291"}, "Update the dependencies array to be: [formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices, calculateTotal]", {"range": "292", "text": "293"}, "Update the dependencies array to be: [generateDecorations, pattern]", {"range": "294", "text": "295"}, [35326, 35418], "[translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery, applyFilters]", [44247, 44383], "[currentTheme, animationsEnabled, animationSpeed, compactMode, borderRadius, shadowIntensity, currentLanguage, user, refreshTranslators, applyTheme, celebrationsEnabled, detectCurrentCelebration]", [641, 675], "[currentTheme, currentAccentColor, applyAccentColor]", [656, 692], "[animationsEnabled, applyAnimationSettings, transitionSpeed]", [639, 675], "[currentFontSize, currentFontFamily, applyFontSize, applyFontFamily]", [6347, 6433], "[formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices, calculateTotal]", [315, 324], "[generateDecorations, pattern]"]