[{"C:\\Users\\<USER>\\lingolink\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\lingolink\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\lingolink\\client\\src\\App.js": "3", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AuthPage.js": "4", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Dashboard.js": "5", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\DashboardTest.js": "6", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleDashboard.js": "7", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\EditProfile.js": "8", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ThemeSelector.js": "9", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Modal.js": "10", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Card.js": "11", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\NotificationManager.js": "12", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\OnboardingTutorial.js": "13", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Notification.js": "14", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LoginPage.js": "15", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AnimationSettings.js": "16", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\FontSettings.js": "17", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LayoutSettings.js": "18", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TranslatorChart.js": "19", "C:\\Users\\<USER>\\lingolink\\client\\src\\services\\translatorService.js": "20", "C:\\Users\\<USER>\\lingolink\\client\\src\\config.js": "21", "C:\\Users\\<USER>\\lingolink\\client\\src\\supabase.js": "22", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\VerificationPage.js": "23", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ApplyTranslation.js": "24", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ContactPage.js": "25", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerDecorations.js": "26", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerCustomizer.js": "27", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleAuthPage.js": "28", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ColorChangingLogo.js": "29", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ProfileCustomizationPopup.js": "30", "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TutorialPopup.js": "31"}, {"size": 535, "mtime": 1743682608622, "results": "32", "hashOfConfig": "33"}, {"size": 362, "mtime": 1743682608955, "results": "34", "hashOfConfig": "33"}, {"size": 26696, "mtime": 1747330717862, "results": "35", "hashOfConfig": "33"}, {"size": 58188, "mtime": 1746448784487, "results": "36", "hashOfConfig": "33"}, {"size": 175513, "mtime": 1748547484799, "results": "37", "hashOfConfig": "33"}, {"size": 4471, "mtime": 1743886909180, "results": "38", "hashOfConfig": "33"}, {"size": 1654, "mtime": 1743881759076, "results": "39", "hashOfConfig": "33"}, {"size": 9875, "mtime": 1745710992777, "results": "40", "hashOfConfig": "33"}, {"size": 5016, "mtime": 1745702900138, "results": "41", "hashOfConfig": "33"}, {"size": 2020, "mtime": 1745702359421, "results": "42", "hashOfConfig": "33"}, {"size": 811, "mtime": 1744291612413, "results": "43", "hashOfConfig": "33"}, {"size": 1245, "mtime": 1744291585611, "results": "44", "hashOfConfig": "33"}, {"size": 5807, "mtime": 1746443652985, "results": "45", "hashOfConfig": "33"}, {"size": 3260, "mtime": 1744291549883, "results": "46", "hashOfConfig": "33"}, {"size": 3298, "mtime": 1747330962007, "results": "47", "hashOfConfig": "33"}, {"size": 2958, "mtime": 1745703111532, "results": "48", "hashOfConfig": "33"}, {"size": 3239, "mtime": 1745703067243, "results": "49", "hashOfConfig": "33"}, {"size": 2751, "mtime": 1745703156121, "results": "50", "hashOfConfig": "33"}, {"size": 3326, "mtime": 1745703608964, "results": "51", "hashOfConfig": "33"}, {"size": 15663, "mtime": 1745767598508, "results": "52", "hashOfConfig": "33"}, {"size": 92, "mtime": 1745705228669, "results": "53", "hashOfConfig": "33"}, {"size": 16002, "mtime": 1745767815002, "results": "54", "hashOfConfig": "33"}, {"size": 36685, "mtime": 1746443580857, "results": "55", "hashOfConfig": "33"}, {"size": 27917, "mtime": 1748524708313, "results": "56", "hashOfConfig": "33"}, {"size": 141, "mtime": 1746447548815, "results": "57", "hashOfConfig": "33"}, {"size": 4861, "mtime": 1747319226771, "results": "58", "hashOfConfig": "33"}, {"size": 6054, "mtime": 1747319050065, "results": "59", "hashOfConfig": "33"}, {"size": 9397, "mtime": 1747324268559, "results": "60", "hashOfConfig": "33"}, {"size": 4505, "mtime": 1748523256871, "results": "61", "hashOfConfig": "33"}, {"size": 18515, "mtime": 1748546918616, "results": "62", "hashOfConfig": "33"}, {"size": 6389, "mtime": 1748546792792, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d1yd02", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\lingolink\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\App.js", ["157", "158"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AuthPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Dashboard.js", ["159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\DashboardTest.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleDashboard.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\EditProfile.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ThemeSelector.js", ["184"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Modal.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Card.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\NotificationManager.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\OnboardingTutorial.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\Notification.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LoginPage.js", ["185"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\AnimationSettings.js", ["186", "187"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\FontSettings.js", ["188", "189"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\LayoutSettings.js", ["190"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TranslatorChart.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\services\\translatorService.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\config.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\supabase.js", ["191", "192", "193"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\VerificationPage.js", ["194"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ApplyTranslation.js", ["195", "196"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ContactPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerDecorations.js", ["197"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\BannerCustomizer.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\SimpleAuthPage.js", [], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ColorChangingLogo.js", ["198"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\ProfileCustomizationPopup.js", ["199"], [], "C:\\Users\\<USER>\\lingolink\\client\\src\\components\\TutorialPopup.js", ["200"], [], {"ruleId": "201", "severity": 1, "message": "202", "line": 7, "column": 8, "nodeType": "203", "messageId": "204", "endLine": 7, "endColumn": 23}, {"ruleId": "201", "severity": 1, "message": "205", "line": 14, "column": 7, "nodeType": "203", "messageId": "204", "endLine": 14, "endColumn": 14}, {"ruleId": "201", "severity": 1, "message": "206", "line": 1, "column": 38, "nodeType": "203", "messageId": "204", "endLine": 1, "endColumn": 44}, {"ruleId": "207", "severity": 1, "message": "208", "line": 89, "column": 5, "nodeType": "209", "messageId": "210", "endLine": 89, "endColumn": 15}, {"ruleId": "207", "severity": 1, "message": "211", "line": 92, "column": 5, "nodeType": "209", "messageId": "210", "endLine": 92, "endColumn": 15}, {"ruleId": "207", "severity": 1, "message": "212", "line": 93, "column": 5, "nodeType": "209", "messageId": "210", "endLine": 93, "endColumn": 14}, {"ruleId": "207", "severity": 1, "message": "213", "line": 94, "column": 5, "nodeType": "209", "messageId": "210", "endLine": 94, "endColumn": 16}, {"ruleId": "201", "severity": 1, "message": "214", "line": 390, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 390, "endColumn": 19}, {"ruleId": "201", "severity": 1, "message": "215", "line": 410, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 410, "endColumn": 21}, {"ruleId": "201", "severity": 1, "message": "216", "line": 417, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 417, "endColumn": 23}, {"ruleId": "201", "severity": 1, "message": "217", "line": 418, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 418, "endColumn": 25}, {"ruleId": "201", "severity": 1, "message": "218", "line": 419, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 419, "endColumn": 25}, {"ruleId": "201", "severity": 1, "message": "219", "line": 455, "column": 23, "nodeType": "203", "messageId": "204", "endLine": 455, "endColumn": 37}, {"ruleId": "201", "severity": 1, "message": "220", "line": 456, "column": 25, "nodeType": "203", "messageId": "204", "endLine": 456, "endColumn": 41}, {"ruleId": "201", "severity": 1, "message": "221", "line": 457, "column": 27, "nodeType": "203", "messageId": "204", "endLine": 457, "endColumn": 45}, {"ruleId": "201", "severity": 1, "message": "222", "line": 458, "column": 27, "nodeType": "203", "messageId": "204", "endLine": 458, "endColumn": 45}, {"ruleId": "223", "severity": 1, "message": "224", "line": 617, "column": 6, "nodeType": "225", "endLine": 617, "endColumn": 98, "suggestions": "226"}, {"ruleId": "201", "severity": 1, "message": "227", "line": 1085, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1085, "endColumn": 32}, {"ruleId": "201", "severity": 1, "message": "228", "line": 1089, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1089, "endColumn": 35}, {"ruleId": "201", "severity": 1, "message": "229", "line": 1101, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1101, "endColumn": 29}, {"ruleId": "201", "severity": 1, "message": "230", "line": 1109, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1109, "endColumn": 29}, {"ruleId": "201", "severity": 1, "message": "231", "line": 1147, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1147, "endColumn": 33}, {"ruleId": "201", "severity": 1, "message": "232", "line": 1156, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1156, "endColumn": 28}, {"ruleId": "201", "severity": 1, "message": "233", "line": 1165, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1165, "endColumn": 34}, {"ruleId": "201", "severity": 1, "message": "234", "line": 1174, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 1174, "endColumn": 27}, {"ruleId": "235", "severity": 2, "message": "236", "line": 3666, "column": 19, "nodeType": "203", "messageId": "237", "endLine": 3666, "endColumn": 27}, {"ruleId": "235", "severity": 2, "message": "236", "line": 3674, "column": 19, "nodeType": "203", "messageId": "237", "endLine": 3674, "endColumn": 27}, {"ruleId": "223", "severity": 1, "message": "238", "line": 16, "column": 6, "nodeType": "225", "endLine": 16, "endColumn": 40, "suggestions": "239"}, {"ruleId": "201", "severity": 1, "message": "240", "line": 7, "column": 27, "nodeType": "203", "messageId": "204", "endLine": 7, "endColumn": 45}, {"ruleId": "201", "severity": 1, "message": "241", "line": 12, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 12, "endColumn": 10}, {"ruleId": "223", "severity": 1, "message": "242", "line": 19, "column": 6, "nodeType": "225", "endLine": 19, "endColumn": 42, "suggestions": "243"}, {"ruleId": "201", "severity": 1, "message": "241", "line": 12, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 12, "endColumn": 10}, {"ruleId": "223", "severity": 1, "message": "244", "line": 20, "column": 6, "nodeType": "225", "endLine": 20, "endColumn": 42, "suggestions": "245"}, {"ruleId": "201", "severity": 1, "message": "241", "line": 12, "column": 9, "nodeType": "203", "messageId": "204", "endLine": 12, "endColumn": 10}, {"ruleId": "201", "severity": 1, "message": "246", "line": 368, "column": 13, "nodeType": "203", "messageId": "204", "endLine": 368, "endColumn": 17}, {"ruleId": "201", "severity": 1, "message": "246", "line": 482, "column": 13, "nodeType": "203", "messageId": "204", "endLine": 482, "endColumn": 17}, {"ruleId": "201", "severity": 1, "message": "247", "line": 508, "column": 19, "nodeType": "203", "messageId": "204", "endLine": 508, "endColumn": 30}, {"ruleId": "201", "severity": 1, "message": "248", "line": 50, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 50, "endColumn": 19}, {"ruleId": "249", "severity": 1, "message": "250", "line": 123, "column": 5, "nodeType": "251", "messageId": "252", "endLine": 139, "endColumn": 6}, {"ruleId": "223", "severity": 1, "message": "253", "line": 197, "column": 6, "nodeType": "225", "endLine": 197, "endColumn": 92, "suggestions": "254"}, {"ruleId": "223", "severity": 1, "message": "255", "line": 10, "column": 6, "nodeType": "225", "endLine": 10, "endColumn": 15, "suggestions": "256"}, {"ruleId": "201", "severity": 1, "message": "257", "line": 5, "column": 10, "nodeType": "203", "messageId": "204", "endLine": 5, "endColumn": 20}, {"ruleId": "201", "severity": 1, "message": "258", "line": 1, "column": 27, "nodeType": "203", "messageId": "204", "endLine": 1, "endColumn": 36}, {"ruleId": "201", "severity": 1, "message": "258", "line": 1, "column": 27, "nodeType": "203", "messageId": "204", "endLine": 1, "endColumn": 36}, "no-unused-vars", "'SimpleDashboard' is defined but never used.", "Identifier", "unusedVar", "'API_URL' is assigned a value but never used.", "'useRef' is defined but never used.", "no-dupe-keys", "Duplicate key 'appearance'.", "ObjectExpression", "unexpected", "Duplicate key 'lightTheme'.", "Duplicate key 'darkTheme'.", "Duplicate key 'systemTheme'.", "'isNewUser' is assigned a value but never used.", "'settingsTab' is assigned a value but never used.", "'passwordError' is assigned a value but never used.", "'passwordSuccess' is assigned a value but never used.", "'settingsChanged' is assigned a value but never used.", "'setBannerStyle' is assigned a value but never used.", "'setBannerPattern' is assigned a value but never used.", "'setBannerAnimation' is assigned a value but never used.", "'setShowDecorations' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", "ArrayExpression", ["259"], "'handleSettingsTabChange' is assigned a value but never used.", "'handleTogglePasswordChange' is assigned a value but never used.", "'handlePasswordChange' is assigned a value but never used.", "'handlePasswordSubmit' is assigned a value but never used.", "'handleNotificationChange' is assigned a value but never used.", "'handlePrivacyChange' is assigned a value but never used.", "'handleAccessibilityChange' is assigned a value but never used.", "'handleSaveSettings' is assigned a value but never used.", "no-undef", "'userType' is not defined.", "undef", "React Hook useEffect has a missing dependency: 'applyAccentColor'. Either include it or remove the dependency array.", ["260"], "'setCurrentLanguage' is assigned a value but never used.", "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'applyAnimationSettings'. Either include it or remove the dependency array.", ["261"], "React Hook useEffect has missing dependencies: 'applyFontFamily' and 'applyFontSize'. Either include them or remove the dependency array.", ["262"], "'data' is assigned a value but never used.", "'profileData' is assigned a value but never used.", "'faceImage' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'calculateTotal'. Either include it or remove the dependency array.", ["263"], "React Hook useEffect has a missing dependency: 'generateDecorations'. Either include it or remove the dependency array.", ["264"], "'isDarkMode' is assigned a value but never used.", "'useEffect' is defined but never used.", {"desc": "265", "fix": "266"}, {"desc": "267", "fix": "268"}, {"desc": "269", "fix": "270"}, {"desc": "271", "fix": "272"}, {"desc": "273", "fix": "274"}, {"desc": "275", "fix": "276"}, "Update the dependencies array to be: [translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery, applyFilters]", {"range": "277", "text": "278"}, "Update the dependencies array to be: [currentTheme, currentAccentColor, applyAccentColor]", {"range": "279", "text": "280"}, "Update the dependencies array to be: [animationsEnabled, applyAnimationSettings, transitionSpeed]", {"range": "281", "text": "282"}, "Update the dependencies array to be: [currentFontSize, currentFontFamily, applyFontSize, applyFontFamily]", {"range": "283", "text": "284"}, "Update the dependencies array to be: [formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices, calculateTotal]", {"range": "285", "text": "286"}, "Update the dependencies array to be: [generateDecorations, pattern]", {"range": "287", "text": "288"}, [23255, 23347], "[translators, sourceLang, targetLang, specialty, minRating, priceRange, sortBy, searchQuery, applyFilters]", [641, 675], "[currentTheme, currentAccentColor, applyAccentColor]", [656, 692], "[animationsEnabled, applyAnimationSettings, transitionSpeed]", [639, 675], "[currentFontSize, currentFontFamily, applyFontSize, applyFontFamily]", [6347, 6433], "[formData.pages, formData.pricePerPage, formData.urgency, formData.additionalServices, calculateTotal]", [315, 324], "[generateDecorations, pattern]"]