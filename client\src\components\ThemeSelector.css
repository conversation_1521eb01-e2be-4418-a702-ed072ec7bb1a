.theme-selector {
  position: relative;
}

.theme-selector-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.theme-selector-toggle:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.theme-icon {
  font-size: 1.2rem;
}

.theme-name {
  font-size: 0.95rem;
  font-weight: 500;
}

.theme-selector-dropdown {
  position: relative;
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  padding: 16px;
  z-index: 100;
  border: 1px solid var(--border-color);
  animation: fadeIn 0.2s ease;
  margin-top: 10px;
}

.theme-section {
  margin-bottom: 16px;
}

.theme-section:last-child {
  margin-bottom: 0;
}

.theme-section h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-muted);
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.theme-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  border-radius: 8px;
  background-color: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  color: var(--text-secondary);
}

.theme-option:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.theme-option.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.theme-option-icon {
  font-size: 1.2rem;
}

.theme-option-name {
  font-size: 0.95rem;
  font-weight: 500;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: var(--text-primary);
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
